/* Mobile Izin Dinas <PERSON>i Style */

/* Base Styles */
.mobile-izin-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
    padding-bottom: 80px;
}

@media (min-width: 768px) {
    .mobile-izin-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-izin-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-izin-container {
        max-width: 840px;
    }
}

/* Header Styles */
.izin-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.izin-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.izin-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
}

.izin-header .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
}

.izin-header .user-details {
    flex: 1;
}

.izin-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.izin-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.izin-header .date-info {
    font-size: 14px;
    opacity: 0.9;
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
}

.izin-header .date-info i {
    margin-right: 8px;
}

/* Navigation */
.izin-navigation {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: white;
    border-radius: 15px;
    margin: 0 15px 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.izin-navigation .back-button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4e73df;
    margin-right: 15px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.izin-navigation .back-button:hover {
    background-color: #4e73df;
    color: white;
    transform: translateX(-3px);
}

.izin-navigation .page-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* Izin Card */
.izin-card {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    margin: 0 15px 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.izin-card .card-title {
    color: #4e73df;
    font-size: 18px;
    margin-bottom: 20px;
}

.izin-card .form-label {
    font-weight: 600;
    font-size: 14px;
    color: #4e5155;
    margin-bottom: 8px;
}

.izin-card .form-control {
    border-radius: 10px;
    padding: 12px 15px;
    border: 1px solid #e2e8f0;
    font-size: 14px;
    transition: all 0.3s ease;
}

.izin-card .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.izin-card textarea.form-control {
    min-height: 100px;
}

.izin-card .input-group-text {
    border-radius: 10px 0 0 10px;
    border: 1px solid #e2e8f0;
    background-color: #f8f9fa;
}

/* Info Section */
.info-section {
    background-color: white;
    border-radius: 15px;
    margin: 0 15px 15px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.info-section .info-title {
    background-color: #4e73df;
    color: white;
    padding: 15px;
    font-weight: 600;
    font-size: 16px;
}

.info-section .info-content {
    padding: 15px;
}

/* Camera Preview */
.camera-preview {
    width: 100%;
    height: 300px;
    background-color: #343a40;
    border-radius: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.camera-preview i {
    font-size: 48px;
    opacity: 0.5;
    position: absolute;
    z-index: 5;
}

.camera-preview .camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px dashed rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    margin: 10px;
    pointer-events: none;
}

.camera-preview .camera-message {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 14px;
    opacity: 0.8;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px;
}

/* Bottom Spacing */
.bottom-spacing {
    height: 80px;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .izin-header {
        padding: 15px;
    }
    
    .izin-card, .info-section, .izin-navigation {
        margin: 0 10px 15px;
    }
    
    .camera-preview {
        height: 250px;
    }
}

@media (min-width: 992px) {
    .izin-header {
        padding: 25px;
    }
    
    .camera-preview {
        height: 350px;
    }
}
