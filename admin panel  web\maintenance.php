<?php
// Include file konfigurasi
require_once 'config/config.php';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .maintenance-container {
            text-align: center;
            padding: 100px 0;
        }
        .maintenance-icon {
            font-size: 100px;
            color: #0d6efd;
            margin-bottom: 20px;
        }
        .maintenance-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="maintenance-container">
            <div class="maintenance-icon">
                <i class="fas fa-tools"></i>
            </div>
            <div class="maintenance-title">Sedang Dalam Pemeliharaan</div>
            <p class="mb-4">Maaf, saat ini sistem sedang dalam pemeliharaan. Silakan coba lagi nanti.</p>
            <p>Estimasi waktu pemeliharaan: <span id="countdown">30:00</span></p>
        </div>
    </div>
    
    <script>
        // Countdown timer
        function startCountdown() {
            var minutes = 30;
            var seconds = 0;
            
            var interval = setInterval(function() {
                if (seconds == 0) {
                    if (minutes == 0) {
                        clearInterval(interval);
                        window.location.href = '<?php echo BASE_URL; ?>';
                        return;
                    }
                    minutes--;
                    seconds = 59;
                } else {
                    seconds--;
                }
                
                document.getElementById('countdown').textContent = 
                    (minutes < 10 ? '0' + minutes : minutes) + ':' + 
                    (seconds < 10 ? '0' + seconds : seconds);
            }, 1000);
        }
        
        // Start countdown
        startCountdown();
    </script>
</body>
</html>
