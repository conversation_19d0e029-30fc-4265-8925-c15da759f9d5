<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil parameter
$id = isset($_GET['id']) ? clean($_GET['id']) : null;
$type = isset($_GET['type']) ? clean($_GET['type']) : null;
$value = isset($_GET['value']) ? (int)$_GET['value'] : 0;

// Validasi parameter
if (!$id || !$type || !in_array($type, ['barcode', 'face']) || !in_array($value, [0, 1])) {
    setMessage('danger', 'Parameter tidak valid!');
    redirect('admin/karyawan.php');
}

// Cek apakah karyawan ada
$query = "SELECT * FROM users WHERE id = '$id' AND role = 'karyawan'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Karyawan tidak ditemukan!');
    redirect('admin/karyawan.php');
}

// Update perizinan
$column = $type == 'barcode' ? 'allow_barcode' : 'allow_face';
$query = "UPDATE users SET $column = '$value' WHERE id = '$id'";

if (mysqli_query($conn, $query)) {
    $status = $value == 1 ? 'diaktifkan' : 'dinonaktifkan';
    $permission_type = $type == 'barcode' ? 'absensi barcode' : 'absensi wajah';
    setMessage('success', "Perizinan $permission_type berhasil $status!");
} else {
    setMessage('danger', 'Gagal mengubah perizinan: ' . mysqli_error($conn));
}

// Redirect ke halaman karyawan
redirect('admin/karyawan.php');
?>
