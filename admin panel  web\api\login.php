<?php
/**
 * API untuk login karyawan dari aplikasi mobile
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Hanya terima method POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Method tidak diizinkan'
    ]);
    exit;
}

// Ambil data JSON dari request body
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// Validasi data
if (!$data || !isset($data['nik']) || !isset($data['password'])) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'NIK dan password harus diisi'
    ]);
    exit;
}

$nik = clean($data['nik']);
$password = $data['password'];

// Cek user di database
$query = "SELECT u.*, b.nama_bidang, l.nama_lokasi 
          FROM users u 
          LEFT JOIN bidang b ON u.bidang_id = b.id 
          LEFT JOIN lokasi l ON u.lokasi_id = l.id 
          WHERE u.nik = '$nik' AND u.role = 'karyawan'";
$result = mysqli_query($conn, $query);

if (!$result || mysqli_num_rows($result) == 0) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'NIK tidak ditemukan'
    ]);
    exit;
}

$user = mysqli_fetch_assoc($result);

// Verifikasi password
if (!password_verify($password, $user['password'])) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'Password salah'
    ]);
    exit;
}

// Login berhasil
echo json_encode([
    'status' => 'success',
    'message' => 'Login berhasil',
    'data' => [
        'id' => $user['id'],
        'nik' => $user['nik'],
        'nama' => $user['nama'],
        'bidang_id' => $user['bidang_id'],
        'bidang_nama' => $user['nama_bidang'],
        'jabatan' => $user['jabatan'],
        'lokasi_id' => $user['lokasi_id'],
        'lokasi_nama' => $user['nama_lokasi'],
        'foto_profil' => $user['foto_profil'],
        'allow_barcode' => isset($user['allow_barcode']) ? (bool)$user['allow_barcode'] : false,
        'allow_face' => isset($user['allow_face']) ? (bool)$user['allow_face'] : false
    ]
]);
?>
