<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID barcode tidak valid!');
    redirect('admin/barcode_config.php');
}

$id = clean($_GET['id']);

// Ambil data barcode
$query = "SELECT bc.*, l.nama_lokasi 
          FROM barcode_config bc 
          JOIN lokasi l ON bc.lokasi_id = l.id 
          WHERE bc.id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Data barcode tidak ditemukan!');
    redirect('admin/barcode_config.php');
}

$barcode = mysqli_fetch_assoc($result);

// Set no_sidebar untuk tampilan cetak
$no_sidebar = true;

// Include header
include_once '../includes/header.php';
?>

<style>
body {
    background-color: white;
}

.print-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
}

.barcode-card {
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.barcode-image {
    margin: 20px auto;
    padding: 10px;
    background: white;
    display: inline-block;
    border: 1px solid #eee;
}

.barcode-info {
    margin-top: 20px;
    text-align: left;
}

.barcode-info table {
    width: 100%;
    border-collapse: collapse;
}

.barcode-info table td {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.barcode-info table td:first-child {
    font-weight: bold;
    width: 150px;
}

.print-buttons {
    margin: 20px 0;
    text-align: center;
}

@media print {
    .print-buttons {
        display: none;
    }
    
    .barcode-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}
</style>

<div class="print-container">
    <div class="print-buttons">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print"></i> Cetak Barcode
        </button>
        <a href="barcode_config.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
    
    <div class="barcode-card">
        <h2><?php echo APP_NAME; ?> - Absensi Barcode</h2>
        <h4><?php echo $barcode['nama_lokasi']; ?></h4>
        
        <div class="barcode-image">
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=<?php echo urlencode($barcode['barcode_value']); ?>" alt="Barcode" class="img-fluid">
        </div>
        
        <p class="text-muted">Scan barcode ini untuk melakukan absensi</p>
        
       
    </div>
    
    <div class="print-buttons">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print"></i> Cetak Barcode
        </button>
        <a href="barcode_config.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
    // Delay printing to ensure everything is loaded
    setTimeout(function() {
        // window.print();
    }, 500);
};
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
