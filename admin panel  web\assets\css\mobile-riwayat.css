/* Mobile Riwayat Style */

/* Base Styles */
.mobile-riwayat-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

@media (min-width: 768px) {
    .mobile-riwayat-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-riwayat-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-riwayat-container {
        max-width: 840px;
    }
}

/* Header Styles */
.riwayat-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.riwayat-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.riwayat-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
}

.riwayat-header .user-avatar .user-photo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
    max-height: 100%;
}

.riwayat-header .user-details {
    flex: 1;
}

.riwayat-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.riwayat-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.riwayat-header .date-info {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 14px;
}

.riwayat-header .date-info i {
    margin-right: 5px;
}

/* Filter Section */
.filter-section {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 15px;
}

.filter-section .filter-title {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 10px;
}

.filter-section .filter-form {
    display: flex;
    gap: 10px;
}

.filter-section .filter-form select {
    flex: 1;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 8px 12px;
    font-size: 14px;
}

.filter-section .filter-form button {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: 500;
}

/* Stats Cards */
.stats-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

@media (min-width: 768px) {
    .stats-row {
        grid-template-columns: repeat(4, 1fr);
    }
}

.stat-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 12px;
}

.stat-card .stat-title {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 8px;
}

.stat-card .stat-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stat-card .stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
}

.stat-card .stat-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.stat-card .stat-icon.primary {
    background-color: #4e73df;
}

.stat-card .stat-icon.success {
    background-color: #1cc88a;
}

.stat-card .stat-icon.warning {
    background-color: #f6c23e;
}

.stat-card .stat-icon.danger {
    background-color: #e74a3b;
}

/* Presensi Cards */
.presensi-list {
    margin-bottom: 15px;
}

.presensi-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 10px;
}

.presensi-card .presensi-date {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.presensi-card .presensi-date i {
    color: #4e73df;
    margin-right: 8px;
}

.presensi-card .presensi-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 10px;
}

.presensi-card .info-item {
    display: flex;
    flex-direction: column;
}

.presensi-card .info-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.presensi-card .info-value {
    font-size: 14px;
    font-weight: 500;
    color: #343a40;
}

.presensi-card .presensi-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

.presensi-card .presensi-actions {
    margin-top: 10px;
}

.presensi-card .btn-detail {
    background-color: #4e73df;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
}

.presensi-card .btn-detail i {
    margin-right: 5px;
}

/* Denda Section */
.denda-section {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 15px;
}

.denda-section .denda-title {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
    margin-bottom: 15px;
}

.denda-section .denda-info {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #495057;
    border-left: 3px solid #4e73df;
}

.denda-section .denda-info i {
    color: #4e73df;
    margin-right: 5px;
}

.denda-section .denda-info ul {
    margin-top: 8px;
    margin-bottom: 0;
    padding-left: 25px;
}

.denda-section .denda-info ul li {
    margin-bottom: 5px;
}

.denda-section .denda-info ul li:last-child {
    margin-bottom: 0;
}

.denda-section .denda-note {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
    margin-top: 5px;
}

.denda-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.denda-item:last-child {
    border-bottom: none;
}

.denda-item .denda-label {
    font-size: 14px;
    color: #343a40;
}

.denda-item .denda-value {
    font-size: 14px;
    font-weight: 500;
    color: #343a40;
}

.denda-total {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
    margin-top: 10px;
    border-top: 2px solid #e9ecef;
}

.denda-total .denda-label {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
}

.denda-total .denda-value {
    font-size: 16px;
    font-weight: bold;
    color: #e74a3b;
}

/* Badge Styles */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-badge.success {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .riwayat-header {
        padding: 15px;
    }

    .filter-section {
        padding: 12px;
    }

    .presensi-card {
        padding: 12px;
    }
}

@media (min-width: 768px) {
    .presensi-card .presensi-info {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.action-button {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 12px 8px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-button .action-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    color: white;
    font-size: 18px;
}

.action-button .action-icon.primary {
    background-color: #4e73df;
}

.action-button .action-icon.success {
    background-color: #1cc88a;
}

.action-button .action-icon.warning {
    background-color: #f6c23e;
}

.action-button .action-icon.info {
    background-color: #36b9cc;
}

.action-button .action-icon.danger {
    background-color: #e74a3b;
}

.action-button .action-text {
    font-size: 14px;
    color: #343a40;
    font-weight: 500;
}

@media (min-width: 768px) {
    .quick-actions {
        grid-template-columns: repeat(4, 1fr);
    }
}
