<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses hapus sesi
if (isset($_GET['action']) && $_GET['action'] == 'clear' && isset($_GET['id'])) {
    $session_id = clean($_GET['id']);
    
    // Update sesi menjadi cleared
    $query = "UPDATE user_sessions SET cleared = 1 WHERE id = '$session_id'";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Riwayat login berhasil dihapus!');
    } else {
        setMessage('danger', 'Gagal menghapus riwayat login: ' . mysqli_error($conn));
    }
    
    // Redirect kembali ke halaman manage_sessions
    redirect('admin/manage_sessions.php');
}

// Proses hapus semua sesi untuk satu user
if (isset($_GET['action']) && $_GET['action'] == 'clear_all' && isset($_GET['user_id'])) {
    $user_id = clean($_GET['user_id']);
    
    // Update semua sesi user menjadi cleared
    $query = "UPDATE user_sessions SET cleared = 1 WHERE user_id = '$user_id'";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Semua riwayat login berhasil dihapus!');
    } else {
        setMessage('danger', 'Gagal menghapus semua riwayat login: ' . mysqli_error($conn));
    }
    
    // Redirect kembali ke halaman manage_sessions
    redirect('admin/manage_sessions.php');
}

// Ambil data sesi yang belum dihapus
$query = "SELECT us.*, u.nik, u.nama 
          FROM user_sessions us
          JOIN users u ON us.user_id = u.id
          WHERE us.cleared = 0 AND u.role = 'karyawan'
          ORDER BY us.login_time DESC";
$result = mysqli_query($conn, $query);

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Manajemen Sesi Login</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Manajemen Sesi Login</li>
    </ol>
    
    <?php
    // Tampilkan pesan jika ada
    $message = getMessage();
    if ($message) {
        echo '<div class="alert alert-' . $message['type'] . ' alert-dismissible fade show" role="alert">
                ' . $message['text'] . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              </div>';
    }
    ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-history me-1"></i>
            Riwayat Login Karyawan
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> Karyawan tidak dapat login dari perangkat baru sampai riwayat login sebelumnya dihapus.
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="sessionsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>No.</th>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Device ID</th>
                            <th>User Agent</th>
                            <th>IP Address</th>
                            <th>Login Time</th>
                            <th>Last Activity</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $no = 1;
                        $current_user_id = 0;
                        
                        if (mysqli_num_rows($result) > 0) {
                            while ($row = mysqli_fetch_assoc($result)) {
                                // Jika user_id berbeda dengan sebelumnya, tambahkan tombol hapus semua
                                $clear_all_button = '';
                                if ($current_user_id != $row['user_id']) {
                                    $current_user_id = $row['user_id'];
                                    $clear_all_button = '<a href="?action=clear_all&user_id=' . $row['user_id'] . '" class="btn btn-warning btn-sm ms-2" onclick="return confirm(\'Hapus semua riwayat login untuk karyawan ini?\')">
                                                            <i class="fas fa-trash-alt"></i> Hapus Semua
                                                        </a>';
                                }
                                
                                echo '<tr>
                                        <td>' . $no++ . '</td>
                                        <td>' . $row['nik'] . '</td>
                                        <td>' . $row['nama'] . $clear_all_button . '</td>
                                        <td><small>' . substr($row['device_id'], 0, 10) . '...</small></td>
                                        <td><small>' . substr($row['user_agent'], 0, 30) . '...</small></td>
                                        <td>' . $row['ip_address'] . '</td>
                                        <td>' . date('d/m/Y H:i:s', strtotime($row['login_time'])) . '</td>
                                        <td>' . date('d/m/Y H:i:s', strtotime($row['last_activity'])) . '</td>
                                        <td>' . ($row['active'] ? '<span class="badge bg-success">Aktif</span>' : '<span class="badge bg-secondary">Tidak Aktif</span>') . '</td>
                                        <td>
                                            <a href="?action=clear&id=' . $row['id'] . '" class="btn btn-danger btn-sm" onclick="return confirm(\'Hapus riwayat login ini?\')">
                                                <i class="fas fa-trash-alt"></i> Hapus
                                            </a>
                                        </td>
                                    </tr>';
                            }
                        } else {
                            echo '<tr><td colspan="10" class="text-center">Tidak ada data</td></tr>';
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<script>
    $(document).ready(function() {
        $('#sessionsTable').DataTable({
            responsive: true,
            order: [[6, 'desc']] // Urutkan berdasarkan login time (descending)
        });
    });
</script>
