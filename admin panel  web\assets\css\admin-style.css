/* Admin Style - Fresh and Modern Look */

/* Base Styles */
body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
}

/* Sidebar Styles */
.sidebar {
    min-height: 100vh;
    background: linear-gradient(135deg, #3a6186 0%, #89253e 100%);
    color: #fff;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
}

.sidebar .position-sticky {
    height: 100vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.sidebar .position-sticky::-webkit-scrollbar {
    width: 5px;
}

.sidebar .position-sticky::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar .position-sticky::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
}

@media (max-width: 767.98px) {
    .sidebar {
        position: fixed;
        top: 0;
        bottom: 0;
        left: -100%;
        z-index: 1000;
        width: 250px !important;
        transition: all 0.3s;
    }
    .sidebar.show {
        left: 0;
    }
    .content {
        width: 100% !important;
        margin-left: 0 !important;
    }
    .overlay {
        display: none;
        position: fixed;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.7);
        z-index: 998;
        opacity: 0;
        transition: all 0.5s ease-in-out;
        top: 0;
        left: 0;
    }
    .overlay.show {
        display: block;
        opacity: 1;
    }
}

.sidebar h4 {
    font-weight: 600;
    letter-spacing: 1px;
    color: #fff;
    font-size: 1.2rem;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.8rem 1.2rem;
    margin: 0.2rem 0.5rem;
    border-radius: 8px;
    transition: all 0.3s;
    font-weight: 500;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: #fff;
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.sidebar .dropdown-menu {
    background: #2c3136;
    border: none;
    margin-left: 2.5rem;
    padding: 0.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.sidebar .dropdown-item {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.7rem 1rem;
    border-radius: 5px;
    margin-bottom: 0.2rem;
    transition: all 0.3s;
}

.sidebar .dropdown-item:hover {
    color: #fff;
    background-color: #3a6186;
}

.sidebar .dropdown-item.active {
    color: #fff;
    background-color: #3a6186;
}

/* Sidebar Close Button */
.sidebar .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
    opacity: 0.7;
    transition: all 0.3s;
}

.sidebar .btn-close:hover {
    opacity: 1;
    transform: rotate(90deg);
}

.sidebar hr {
    margin: 1.5rem 1rem;
    border-color: rgba(255, 255, 255, 0.2);
}

/* Content Styles */
.content {
    padding: 20px;
    margin-left: 0;
    transition: all 0.3s;
}

@media (min-width: 768px) {
    .content {
        margin-left: 16.666667%;
    }
}

@media (min-width: 992px) {
    .content {
        margin-left: 16.666667%;
    }
}

/* Navbar Styles */
.navbar {
    background: #fff;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    margin-bottom: 25px;
    padding: 0.8rem 1.5rem;
}

.navbar .navbar-brand {
    font-weight: 600;
    color: #3a6186;
}

.navbar .nav-link {
    color: #555;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s;
}

.navbar .nav-link:hover {
    color: #3a6186;
    background-color: rgba(58, 97, 134, 0.05);
}

.navbar .dropdown-menu {
    background-color: #ffffff;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 0.5rem;
}

.navbar .dropdown-item {
    padding: 0.7rem 1rem;
    border-radius: 5px;
    transition: all 0.3s;
    color: #495057;
}

.navbar .dropdown-item:hover {
    background-color: #f0f5ff;
    color: #3a6186;
}

.navbar .dropdown-item i {
    width: 20px;
    text-align: center;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    overflow: hidden;
    margin-bottom: 25px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(to right, #f8f9fa, #ffffff);
    border-bottom: 1px solid #f0f0f0;
    padding: 1.2rem 1.5rem;
    font-weight: 600;
    color: #3a6186;
}

.card-body {
    padding: 1.5rem;
}

/* Dashboard Cards */
.border-left-primary {
    border-left: 4px solid #4e73df;
}

.border-left-success {
    border-left: 4px solid #1cc88a;
}

.border-left-warning {
    border-left: 4px solid #f6c23e;
}

.border-left-danger {
    border-left: 4px solid #e74a3b;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-primary {
    background: linear-gradient(135deg, #3a6186 0%, #89253e 100%);
    border: none;
    box-shadow: 0 4px 6px rgba(58, 97, 134, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #325679 0%, #7a1f36 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(58, 97, 134, 0.3);
}

.btn-secondary {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #6c757d;
}

.btn-secondary:hover {
    background-color: #e9ecef;
    color: #495057;
}

/* Table Styles */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #e9ecef;
}

.table-hover tbody tr:hover {
    background-color: rgba(58, 97, 134, 0.03);
}

/* Badge Styles */
.badge {
    padding: 0.5em 0.8em;
    font-weight: 500;
    border-radius: 5px;
}

.bg-success {
    background-color: #1cc88a !important;
}

.bg-warning {
    background-color: #f6c23e !important;
}

.bg-danger {
    background-color: #e74a3b !important;
}

.bg-info {
    background-color: #36b9cc !important;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.alert-dismissible .btn-close {
    padding: 1rem;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s;
}

.form-control:focus, .form-select:focus {
    border-color: #3a6186;
    box-shadow: 0 0 0 0.25rem rgba(58, 97, 134, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Pagination Styles */
.pagination .page-link {
    border-radius: 5px;
    margin: 0 2px;
    color: #3a6186;
    border: 1px solid #e9ecef;
}

.pagination .page-item.active .page-link {
    background-color: #3a6186;
    border-color: #3a6186;
}

/* Modal Styles */
.modal-backdrop {
    opacity: 0.5 !important;
    z-index: 1040 !important;
}

.modal {
    z-index: 1050 !important;
}

.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 1.2rem 1.5rem;
    background: linear-gradient(to right, #f8f9fa, #ffffff);
}

.modal-header .btn-close {
    opacity: 0.7;
    transition: all 0.3s;
}

.modal-header .btn-close:hover {
    opacity: 1;
    transform: rotate(90deg);
}

.modal-title {
    font-weight: 600;
    color: #3a6186;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 1.2rem 1.5rem;
}

/* Fix untuk modal berkedip */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out !important;
    transform: translate(0, -50px) !important;
}

.modal.show .modal-dialog {
    transform: none !important;
}

.modal-backdrop.fade {
    opacity: 0 !important;
    transition: opacity 0.3s linear !important;
}

.modal-backdrop.show {
    opacity: 0.5 !important;
}

/* Fix untuk modal edit karyawan */
.edit-modal {
    z-index: 1060 !important;
}

.reset-modal {
    z-index: 1070 !important;
}

.add-modal {
    z-index: 1050 !important;
}

/* Pastikan setiap modal memiliki z-index yang berbeda */
.edit-modal .modal-backdrop {
    z-index: 1059 !important;
}

.reset-modal .modal-backdrop {
    z-index: 1069 !important;
}

.add-modal .modal-backdrop {
    z-index: 1049 !important;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }

    .navbar {
        padding: 0.5rem 1rem;
    }

    .modal-content {
        border-radius: 10px;
    }

    .modal-header, .modal-footer {
        padding: 1rem;
    }

    .modal-body {
        padding: 1.2rem;
    }
}
