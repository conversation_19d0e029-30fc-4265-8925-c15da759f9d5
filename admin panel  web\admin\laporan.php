<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Filter bulan dan tahun
$bulan = isset($_GET['bulan']) ? clean($_GET['bulan']) : date('m');
$tahun = isset($_GET['tahun']) ? clean($_GET['tahun']) : date('Y');

// Ambil data karyawan
$query = "SELECT * FROM users WHERE role = 'karyawan' ORDER BY nama ASC";
$result = mysqli_query($conn, $query);

$karyawan = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $karyawan[] = $row;
    }
}

// Ambil data denda
$denda = getDenda();

// Hitung jumlah hari dalam bulan
$jumlah_hari = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);

// Ambil data hari libur dalam bulan ini
$query = "SELECT tanggal FROM hari_libur WHERE MONTH(tanggal) = '$bulan' AND YEAR(tanggal) = '$tahun'";
$result = mysqli_query($conn, $query);

$hari_libur = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $hari_libur[] = $row['tanggal'];
    }
}

// Hitung jumlah hari kerja (tidak termasuk Sabtu, Minggu, dan hari libur)
$hari_kerja = 0;
for ($i = 1; $i <= $jumlah_hari; $i++) {
    $tanggal = sprintf('%04d-%02d-%02d', $tahun, $bulan, $i);
    $hari = date('N', strtotime($tanggal)); // 1 (Senin) sampai 7 (Minggu)

    // Cek apakah hari kerja (Senin-Jumat) dan bukan hari libur
    if ($hari >= 1 && $hari <= 5 && !in_array($tanggal, $hari_libur)) {
        $hari_kerja++;
    }
}

// Data laporan
$laporan = [];

foreach ($karyawan as $k) {
    // Hitung jumlah kehadiran
    $query = "SELECT COUNT(*) as total FROM presensi
              WHERE user_id = '{$k['id']}'
              AND MONTH(tanggal) = '$bulan'
              AND YEAR(tanggal) = '$tahun'";
    $result = mysqli_query($conn, $query);
    $row = mysqli_fetch_assoc($result);
    $jumlah_kehadiran = $row['total'];

    // Hitung jumlah keterlambatan
    $query = "SELECT COUNT(*) as total FROM presensi
              WHERE user_id = '{$k['id']}'
              AND status = 'Terlambat'
              AND MONTH(tanggal) = '$bulan'
              AND YEAR(tanggal) = '$tahun'";
    $result = mysqli_query($conn, $query);
    $row = mysqli_fetch_assoc($result);
    $jumlah_terlambat = $row['total'];

    // Hitung jumlah pulang awal
    $query = "SELECT COUNT(*) as total FROM presensi
              WHERE user_id = '{$k['id']}'
              AND status = 'Pulang Awal'
              AND MONTH(tanggal) = '$bulan'
              AND YEAR(tanggal) = '$tahun'";
    $result = mysqli_query($conn, $query);
    $row = mysqli_fetch_assoc($result);
    $jumlah_pulang_awal = $row['total'];

    // Hitung jumlah tidak absen pulang (absen masuk tapi tidak absen pulang)
    $query = "SELECT COUNT(*) as total FROM presensi
              WHERE user_id = '{$k['id']}'
              AND jam_pulang IS NULL
              AND jam_masuk IS NOT NULL
              AND MONTH(tanggal) = '$bulan'
              AND YEAR(tanggal) = '$tahun'";
    $result = mysqli_query($conn, $query);
    $row = mysqli_fetch_assoc($result);
    $jumlah_tidak_absen_pulang = $row['total'];

    // Hitung jumlah tidak hadir
    $jumlah_tidak_hadir = $hari_kerja - $jumlah_kehadiran;
    if ($jumlah_tidak_hadir < 0) $jumlah_tidak_hadir = 0;

    // Hitung total denda
    $total_denda_terlambat = $jumlah_terlambat * $denda['denda_masuk'];
    $total_denda_pulang_awal = $jumlah_pulang_awal * $denda['denda_pulang'];
    $total_denda_tidak_hadir = $jumlah_tidak_hadir * $denda['denda_tidak_absen'];
    $total_denda_tidak_absen_pulang = $jumlah_tidak_absen_pulang * $denda['denda_tidak_absen_pulang'];
    $total_denda = $total_denda_terlambat + $total_denda_pulang_awal + $total_denda_tidak_hadir + $total_denda_tidak_absen_pulang;

    // Tambahkan ke array laporan
    $laporan[] = [
        'id' => $k['id'],
        'nik' => $k['nik'],
        'nama' => $k['nama'],
        'bidang' => $k['bidang'],
        'hari_kerja' => $hari_kerja,
        'jumlah_kehadiran' => $jumlah_kehadiran,
        'jumlah_terlambat' => $jumlah_terlambat,
        'jumlah_pulang_awal' => $jumlah_pulang_awal,
        'jumlah_tidak_absen_pulang' => $jumlah_tidak_absen_pulang,
        'jumlah_tidak_hadir' => $jumlah_tidak_hadir,
        'total_denda' => $total_denda
    ];
}

// Proses export ke Excel
if (isset($_GET['export']) && $_GET['export'] == 'excel') {
    // Set header untuk download file Excel
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="Laporan_Absensi_' . $bulan . '_' . $tahun . '.xls"');
    header('Cache-Control: max-age=0');

    // Output tabel Excel
    echo '<table border="1">';
    echo '<tr>';
    echo '<th colspan="11">LAPORAN ABSENSI KARYAWAN</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th colspan="11">Periode: ' . date('F Y', strtotime($tahun . '-' . $bulan . '-01')) . '</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th>No</th>';
    echo '<th>NIK</th>';
    echo '<th>Nama Karyawan</th>';
    echo '<th>Bidang</th>';
    echo '<th>Hari Kerja</th>';
    echo '<th>Jumlah Kehadiran</th>';
    echo '<th>Jumlah Terlambat</th>';
    echo '<th>Jumlah Pulang Awal</th>';
    echo '<th>Tidak Absen Pulang</th>';
    echo '<th>Jumlah Tidak Hadir</th>';
    echo '<th>Total Denda</th>';
    echo '</tr>';

    $no = 1;
    foreach ($laporan as $l) {
        echo '<tr>';
        echo '<td>' . $no++ . '</td>';
        echo '<td>' . $l['nik'] . '</td>';
        echo '<td>' . $l['nama'] . '</td>';
        echo '<td>' . $l['bidang'] . '</td>';
        echo '<td>' . $l['hari_kerja'] . '</td>';
        echo '<td>' . $l['jumlah_kehadiran'] . '</td>';
        echo '<td>' . $l['jumlah_terlambat'] . '</td>';
        echo '<td>' . $l['jumlah_pulang_awal'] . '</td>';
        echo '<td>' . $l['jumlah_tidak_absen_pulang'] . '</td>';
        echo '<td>' . $l['jumlah_tidak_hadir'] . '</td>';
        echo '<td>Rp ' . number_format($l['total_denda'], 0, ',', '.') . '</td>';
        echo '</tr>';
    }

    echo '</table>';
    exit;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Laporan Absensi Karyawan</h1>
        <a href="laporan.php?export=excel&bulan=<?php echo $bulan; ?>&tahun=<?php echo $tahun; ?>" class="btn btn-success">
            <i class="fas fa-file-excel"></i> Export Excel
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">Laporan Periode: <?php echo date('F Y', strtotime($tahun . '-' . $bulan . '-01')); ?></h6>
            <form method="get" class="form-inline">
                <div class="input-group">
                    <select class="form-select" name="bulan">
                        <?php for ($i = 1; $i <= 12; $i++): ?>
                            <option value="<?php echo sprintf('%02d', $i); ?>" <?php echo ($bulan == sprintf('%02d', $i)) ? 'selected' : ''; ?>>
                                <?php echo date('F', strtotime('2023-' . sprintf('%02d', $i) . '-01')); ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                    <select class="form-select" name="tahun">
                        <?php for ($i = date('Y') - 5; $i <= date('Y'); $i++): ?>
                            <option value="<?php echo $i; ?>" <?php echo ($tahun == $i) ? 'selected' : ''; ?>>
                                <?php echo $i; ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                    <button type="submit" class="btn btn-primary">Filter</button>
                </div>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>NIK</th>
                            <th>Nama Karyawan</th>
                            <th>Bidang</th>
                            <th>Hari Kerja</th>
                            <th>Jumlah Kehadiran</th>
                            <th>Jumlah Terlambat</th>
                            <th>Jumlah Pulang Awal</th>
                            <th>Tidak Absen Pulang</th>
                            <th>Jumlah Tidak Hadir</th>
                            <th>Total Denda</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($laporan)): ?>
                            <tr>
                                <td colspan="11" class="text-center">Tidak ada data laporan</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($laporan as $l): ?>
                                <tr>
                                    <td><?php echo $l['nik']; ?></td>
                                    <td><?php echo $l['nama']; ?></td>
                                    <td><?php echo $l['bidang']; ?></td>
                                    <td><?php echo $l['hari_kerja']; ?></td>
                                    <td><?php echo $l['jumlah_kehadiran']; ?></td>
                                    <td><?php echo $l['jumlah_terlambat']; ?></td>
                                    <td><?php echo $l['jumlah_pulang_awal']; ?></td>
                                    <td><?php echo $l['jumlah_tidak_absen_pulang']; ?></td>
                                    <td><?php echo $l['jumlah_tidak_hadir']; ?></td>
                                    <td>Rp <?php echo number_format($l['total_denda'], 0, ',', '.'); ?></td>
                                    <td>
                                        <a href="detail_laporan.php?id=<?php echo $l['id']; ?>&bulan=<?php echo $bulan; ?>&tahun=<?php echo $tahun; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> Detail
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Informasi Denda</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info mb-4">
                <i class="fas fa-info-circle"></i> Denda dihitung berdasarkan:
                <ul class="mb-0 mt-2">
                    <li>Keterlambatan: dihitung saat karyawan terlambat masuk</li>
                    <li>Pulang Awal: dihitung saat karyawan pulang sebelum waktunya</li>
                    <li>Tidak Absen Pulang: dihitung saat karyawan melakukan absen masuk tetapi tidak melakukan absen pulang</li>
                    <li>Tidak Hadir: dihitung untuk hari kerja yang sudah lewat dan karyawan tidak melakukan absensi</li>
                </ul>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Denda Keterlambatan</h5>
                            <p class="card-text">Rp <?php echo number_format($denda['denda_masuk'], 0, ',', '.'); ?> per keterlambatan</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Denda Pulang Awal</h5>
                            <p class="card-text">Rp <?php echo number_format($denda['denda_pulang'], 0, ',', '.'); ?> per pulang awal</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Denda Tidak Absen Pulang</h5>
                            <p class="card-text">Rp <?php echo number_format($denda['denda_tidak_absen_pulang'], 0, ',', '.'); ?> per tidak absen pulang</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Denda Tidak Hadir</h5>
                            <p class="card-text">Rp <?php echo number_format($denda['denda_tidak_absen'], 0, ',', '.'); ?> per tidak hadir</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
