<?php
// Include file konfigurasi
require_once 'config/database.php';

// Query untuk menghitung jumlah data aktivitas karyawan
$query = "SELECT COUNT(*) as total FROM aktivitas_karyawan";
$result = mysqli_query($conn, $query);
$row = mysqli_fetch_assoc($result);
echo "Total data aktivitas karyawan: " . $row['total'] . "\n";

// Query untuk memeriksa data latitude dan longitude
$query = "SELECT latitude, longitude FROM aktivitas_karyawan LIMIT 5";
$result = mysqli_query($conn, $query);
echo "Contoh data koordinat:\n";
while ($row = mysqli_fetch_assoc($result)) {
    echo "Latitude: " . $row['latitude'] . ", Longitude: " . $row['longitude'] . "\n";
}
?>
