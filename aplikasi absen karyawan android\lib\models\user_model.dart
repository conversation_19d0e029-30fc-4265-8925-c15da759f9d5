class UserModel {
  final int id;
  final String nik;
  final String nama;
  final int? bidangId;
  final String? bidangNama;
  final String? jabatan;
  final int? lokasiId;
  final String? lokasiNama;
  final String? fotoProfil;
  final bool allowBarcode;
  final bool allowFace;

  UserModel({
    required this.id,
    required this.nik,
    required this.nama,
    this.bidangId,
    this.bidangNama,
    this.jabatan,
    this.lokasiId,
    this.lokasiNama,
    this.fotoProfil,
    this.allowBarcode = false,
    this.allowFace = false,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? 0,
      nik: json['nik'] ?? '',
      nama: json['nama'] ?? '',
      bidangId: json['bidang_id'],
      bidangNama: json['bidang_nama'],
      jabatan: json['jabatan'],
      lokasiId: json['lokasi_id'],
      lokasiNama: json['lokasi_nama'],
      fotoProfil: json['foto_profil'],
      allowBarcode: json['allow_barcode'] ?? false,
      allowFace: json['allow_face'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nik': nik,
      'nama': nama,
      'bidang_id': bidangId,
      'bidang_nama': bidangNama,
      'jabatan': jabatan,
      'lokasi_id': lokasiId,
      'lokasi_nama': lokasiNama,
      'foto_profil': fotoProfil,
      'allow_barcode': allowBarcode,
      'allow_face': allowFace,
    };
  }

  UserModel copyWith({
    int? id,
    String? nik,
    String? nama,
    int? bidangId,
    String? bidangNama,
    String? jabatan,
    int? lokasiId,
    String? lokasiNama,
    String? fotoProfil,
    bool? allowBarcode,
    bool? allowFace,
  }) {
    return UserModel(
      id: id ?? this.id,
      nik: nik ?? this.nik,
      nama: nama ?? this.nama,
      bidangId: bidangId ?? this.bidangId,
      bidangNama: bidangNama ?? this.bidangNama,
      jabatan: jabatan ?? this.jabatan,
      lokasiId: lokasiId ?? this.lokasiId,
      lokasiNama: lokasiNama ?? this.lokasiNama,
      fotoProfil: fotoProfil ?? this.fotoProfil,
      allowBarcode: allowBarcode ?? this.allowBarcode,
      allowFace: allowFace ?? this.allowFace,
    );
  }
}
