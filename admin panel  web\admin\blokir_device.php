<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Filter data
$filter_user_id = isset($_GET['user_id']) ? clean($_GET['user_id']) : '';
$filter_status = isset($_GET['status']) ? clean($_GET['status']) : '';

// Ambil data karyawan untuk filter
$query_karyawan = "SELECT id, nik, nama FROM users WHERE role = 'karyawan' ORDER BY nama";
$result_karyawan = mysqli_query($conn, $query_karyawan);
$karyawan = [];
while ($row = mysqli_fetch_assoc($result_karyawan)) {
    $karyawan[] = $row;
}

// Proses tambah data
if (isset($_POST['action']) && $_POST['action'] == 'add') {
    $nik = clean($_POST['nik']);
    $device_id = clean($_POST['device_id']);
    $alasan = clean($_POST['alasan']);

    // Dapatkan user_id berdasarkan NIK
    $query_user = "SELECT id FROM users WHERE nik = '$nik' AND role = 'karyawan'";
    $result_user = mysqli_query($conn, $query_user);

    if (mysqli_num_rows($result_user) > 0) {
        $user_id = mysqli_fetch_assoc($result_user)['id'];

        // Cek apakah device_id sudah ada
        $query_check = "SELECT id FROM blokir_device WHERE device_id = '$device_id'";
        $result_check = mysqli_query($conn, $query_check);

        if (mysqli_num_rows($result_check) > 0) {
            // Update data yang sudah ada
            $query = "UPDATE blokir_device SET
                      user_id = '$user_id',
                      nik = '$nik',
                      alasan = '$alasan',
                      status = 'active',
                      updated_at = NOW()
                      WHERE device_id = '$device_id'";
            $message = "Device ID sudah ada dan berhasil diupdate";
        } else {
            // Insert data baru
            $query = "INSERT INTO blokir_device (user_id, nik, device_id, alasan, status)
                      VALUES ('$user_id', '$nik', '$device_id', '$alasan', 'active')";
            $message = "Device berhasil diblokir";
        }

        if (mysqli_query($conn, $query)) {
            $_SESSION['success'] = $message;
        } else {
            $_SESSION['error'] = "Gagal memblokir device: " . mysqli_error($conn);
        }
    } else {
        $_SESSION['error'] = "NIK tidak valid";
    }

    // Redirect untuk menghindari resubmission
    header("Location: blokir_device.php");
    exit;
}

// Proses ubah status
if (isset($_POST['action']) && $_POST['action'] == 'change_status') {
    $id = clean($_POST['id']);
    $status = clean($_POST['status']);

    $query = "UPDATE blokir_device SET status = '$status', updated_at = NOW() WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        $_SESSION['success'] = "Status device berhasil diubah";
    } else {
        $_SESSION['error'] = "Gagal mengubah status device: " . mysqli_error($conn);
    }

    // Redirect untuk menghindari resubmission
    header("Location: blokir_device.php");
    exit;
}

// Query untuk mengambil data blokir device
$query = "SELECT bd.*, u.nama
          FROM blokir_device bd
          LEFT JOIN users u ON bd.user_id = u.id
          WHERE 1=1";

// Tambahkan filter
if (!empty($filter_user_id)) {
    $query .= " AND bd.user_id = '$filter_user_id'";
}

if (!empty($filter_status)) {
    $query .= " AND bd.status = '$filter_status'";
}

$query .= " ORDER BY bd.updated_at DESC";
$result = mysqli_query($conn, $query);
$blokir_device = [];
while ($row = mysqli_fetch_assoc($result)) {
    $blokir_device[] = $row;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Blokir Device</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Blokir Device</li>
    </ol>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php
            echo $_SESSION['success'];
            unset($_SESSION['success']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Informasi
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5><i class="fas fa-lightbulb me-2"></i>Fitur Blokir Device</h5>
                <p>Fitur ini memungkinkan Anda untuk memblokir device tertentu agar tidak dapat digunakan untuk login ke aplikasi. Ketika device atau NIK diblokir:</p>
                <ul>
                    <li>Karyawan dengan NIK yang diblokir tidak dapat login ke aplikasi</li>
                    <li>Device yang diblokir tidak dapat digunakan untuk login oleh karyawan manapun</li>
                    <li>Blokir berlaku untuk web dan aplikasi mobile</li>
                </ul>
                <p>Untuk memblokir device, Anda perlu memasukkan NIK karyawan dan Device ID. Alasan pemblokiran bersifat opsional tetapi sangat disarankan untuk dokumentasi.</p>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus me-1"></i>
            Tambah Device yang Diblokir
        </div>
        <div class="card-body">
            <form method="post" action="" class="row g-3">
                <input type="hidden" name="action" value="add">
                <div class="col-md-4">
                    <label for="nik" class="form-label">NIK Karyawan</label>
                    <input type="text" class="form-control" id="nik" name="nik" required>
                </div>
                <div class="col-md-4">
                    <label for="device_id" class="form-label">Device ID</label>
                    <input type="text" class="form-control" id="device_id" name="device_id" required>
                </div>
                <div class="col-md-4">
                    <label for="alasan" class="form-label">Alasan</label>
                    <input type="text" class="form-control" id="alasan" name="alasan">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Data
        </div>
        <div class="card-body">
            <form method="get" action="" class="row g-3">
                <div class="col-md-5">
                    <label for="user_id" class="form-label">Karyawan</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">Semua Karyawan</option>
                        <?php foreach ($karyawan as $k): ?>
                            <option value="<?php echo $k['id']; ?>" <?php echo $filter_user_id == $k['id'] ? 'selected' : ''; ?>>
                                <?php echo $k['nik'] . ' - ' . $k['nama']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-5">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Semua Status</option>
                        <option value="active" <?php echo $filter_status == 'active' ? 'selected' : ''; ?>>Aktif</option>
                        <option value="inactive" <?php echo $filter_status == 'inactive' ? 'selected' : ''; ?>>Tidak Aktif</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <a href="blokir_device.php" class="btn btn-secondary">
                        <i class="fas fa-sync-alt me-1"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Data Device yang Diblokir
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-datatable" id="blokirDeviceTable">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Device ID</th>
                            <th>Alasan</th>
                            <th>Status</th>
                            <th>Tanggal Update</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $no = 1; foreach ($blokir_device as $bd): ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $bd['nik']; ?></td>
                                <td><?php echo $bd['nama']; ?></td>
                                <td><?php echo $bd['device_id']; ?></td>
                                <td><?php echo $bd['alasan'] ?? '-'; ?></td>
                                <td>
                                    <?php if ($bd['status'] == 'active'): ?>
                                        <span class="badge bg-danger">Diblokir</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">Diizinkan</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('d/m/Y H:i:s', strtotime($bd['updated_at'])); ?></td>
                                <td>
                                    <form method="post" action="" class="d-inline">
                                        <input type="hidden" name="action" value="change_status">
                                        <input type="hidden" name="id" value="<?php echo $bd['id']; ?>">
                                        <?php if ($bd['status'] == 'active'): ?>
                                            <input type="hidden" name="status" value="inactive">
                                            <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Apakah Anda yakin ingin mengizinkan device ini?')">
                                                <i class="fas fa-check"></i> Izinkan
                                            </button>
                                        <?php else: ?>
                                            <input type="hidden" name="status" value="active">
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin memblokir device ini?')">
                                                <i class="fas fa-ban"></i> Blokir
                                            </button>
                                        <?php endif; ?>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Inisialisasi DataTable
        $('#blokirDeviceTable').DataTable({
            responsive: true,
            order: [[6, 'desc']]
        });
    });
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
