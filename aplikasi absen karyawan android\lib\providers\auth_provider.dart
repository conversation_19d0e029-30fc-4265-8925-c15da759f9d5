import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../services/api_service.dart';
import '../utils/constants.dart';

class AuthProvider with ChangeNotifier {
  UserModel? _user;
  bool _isLoading = false;
  bool _isLoggedIn = false;

  UserModel? get user => _user;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _isLoggedIn;

  AuthProvider() {
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    final prefs = await SharedPreferences.getInstance();
    _isLoggedIn = prefs.getBool(Constants.keyIsLoggedIn) ?? false;
    
    if (_isLoggedIn) {
      // Load user data from SharedPreferences
      final userId = prefs.getInt(Constants.keyUserId);
      final userNik = prefs.getString(Constants.keyUserNik);
      final userName = prefs.getString(Constants.keyUserName);
      final userBidang = prefs.getString(Constants.keyUserBidang);
      final userJabatan = prefs.getString(Constants.keyUserJabatan);
      final userLokasi = prefs.getString(Constants.keyUserLokasi);
      final userFoto = prefs.getString(Constants.keyUserFoto);

      if (userId != null && userNik != null && userName != null) {
        _user = UserModel(
          id: userId,
          nik: userNik,
          nama: userName,
          bidangNama: userBidang,
          jabatan: userJabatan,
          lokasiNama: userLokasi,
          fotoProfil: userFoto,
        );
      }
    }
    
    notifyListeners();
  }

  Future<Map<String, dynamic>> login(String nik, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await ApiService.login(nik, password);
      
      if (response['status'] == 'success') {
        final userData = response['data'];
        _user = UserModel.fromJson(userData);
        _isLoggedIn = true;
        
        // Save to SharedPreferences
        await _saveUserData(userData);
        
        _isLoading = false;
        notifyListeners();
        
        return {
          'success': true,
          'message': Constants.msgLoginSuccess,
        };
      } else {
        _isLoading = false;
        notifyListeners();
        
        return {
          'success': false,
          'message': response['message'] ?? Constants.msgLoginFailed,
        };
      }
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      
      return {
        'success': false,
        'message': 'Terjadi kesalahan: ${e.toString()}',
      };
    }
  }

  Future<void> _saveUserData(Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setBool(Constants.keyIsLoggedIn, true);
    await prefs.setInt(Constants.keyUserId, userData['id']);
    await prefs.setString(Constants.keyUserNik, userData['nik']);
    await prefs.setString(Constants.keyUserName, userData['nama']);
    
    if (userData['bidang_nama'] != null) {
      await prefs.setString(Constants.keyUserBidang, userData['bidang_nama']);
    }
    if (userData['jabatan'] != null) {
      await prefs.setString(Constants.keyUserJabatan, userData['jabatan']);
    }
    if (userData['lokasi_nama'] != null) {
      await prefs.setString(Constants.keyUserLokasi, userData['lokasi_nama']);
    }
    if (userData['foto_profil'] != null) {
      await prefs.setString(Constants.keyUserFoto, userData['foto_profil']);
    }
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    final prefs = await SharedPreferences.getInstance();
    
    // Clear all user data
    await prefs.clear();
    
    _user = null;
    _isLoggedIn = false;
    _isLoading = false;
    
    notifyListeners();
  }

  Future<void> updateUserProfile(UserModel updatedUser) async {
    _user = updatedUser;
    
    // Update SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(Constants.keyUserName, updatedUser.nama);
    if (updatedUser.bidangNama != null) {
      await prefs.setString(Constants.keyUserBidang, updatedUser.bidangNama!);
    }
    if (updatedUser.jabatan != null) {
      await prefs.setString(Constants.keyUserJabatan, updatedUser.jabatan!);
    }
    if (updatedUser.lokasiNama != null) {
      await prefs.setString(Constants.keyUserLokasi, updatedUser.lokasiNama!);
    }
    if (updatedUser.fotoProfil != null) {
      await prefs.setString(Constants.keyUserFoto, updatedUser.fotoProfil!);
    }
    
    notifyListeners();
  }
}
