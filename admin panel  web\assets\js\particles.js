// Particles animation for login page
document.addEventListener('DOMContentLoaded', function() {
    const particlesContainer = document.querySelector('.particles-container');
    
    if (!particlesContainer) return;
    
    // Configuration
    const particleCount = 50;
    const particleColors = ['#ffffff', '#f0f0f0', '#e0e0e0', '#d0d0d0'];
    const minSize = 5;
    const maxSize = 20;
    const minDuration = 15;
    const maxDuration = 30;
    
    // Create particles
    for (let i = 0; i < particleCount; i++) {
        createParticle();
    }
    
    function createParticle() {
        const particle = document.createElement('div');
        particle.classList.add('particle');
        
        // Random properties
        const size = Math.random() * (maxSize - minSize) + minSize;
        const posX = Math.random() * 100; // percentage
        const posY = Math.random() * 100 + 100; // start from below the viewport
        const duration = Math.random() * (maxDuration - minDuration) + minDuration;
        const delay = Math.random() * 5;
        const color = particleColors[Math.floor(Math.random() * particleColors.length)];
        const opacity = Math.random() * 0.6 + 0.2;
        
        // Apply styles
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.left = `${posX}%`;
        particle.style.top = `${posY}%`;
        particle.style.backgroundColor = color;
        particle.style.opacity = opacity;
        particle.style.animation = `float-up ${duration}s linear ${delay}s infinite`;
        
        // Add to container
        particlesContainer.appendChild(particle);
    }
});

// Form validation and effects
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('android-login-form');
    
    if (!loginForm) return;
    
    const inputs = loginForm.querySelectorAll('.android-form-control');
    
    // Add focus effects
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value.trim() !== '') {
                this.parentElement.classList.add('has-value');
            } else {
                this.parentElement.classList.remove('has-value');
            }
        });
        
        // Check initial state
        if (input.value.trim() !== '') {
            input.parentElement.classList.add('has-value');
        }
    });
    
    // Add form submission animation
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('.android-btn');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Logging in...';
                submitBtn.disabled = true;
            }
        });
    }
});
