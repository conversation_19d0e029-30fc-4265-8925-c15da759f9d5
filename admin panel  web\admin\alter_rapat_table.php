<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek apakah kolom penanggung_jawab_id sudah ada di tabel rapat
$query = "SHOW COLUMNS FROM `rapat` LIKE 'penanggung_jawab_id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    // Tambahkan kolom penanggung_jawab_id
    $query = "ALTER TABLE `rapat` ADD COLUMN `penanggung_jawab_id` INT(11) NULL AFTER `created_by`";
    
    if (mysqli_query($conn, $query)) {
        // Tambahkan foreign key
        $query = "ALTER TABLE `rapat` ADD CONSTRAINT `rapat_ibfk_2` FOREIGN KEY (`penanggung_jawab_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE";
        
        if (mysqli_query($conn, $query)) {
            setMessage('success', 'Kolom penanggung jawab berhasil ditambahkan ke tabel rapat!');
        } else {
            setMessage('warning', 'Kolom penanggung jawab berhasil ditambahkan, tetapi foreign key gagal dibuat: ' . mysqli_error($conn));
        }
    } else {
        setMessage('danger', 'Gagal menambahkan kolom penanggung jawab: ' . mysqli_error($conn));
    }
} else {
    setMessage('info', 'Kolom penanggung jawab sudah ada di tabel rapat.');
}

// Redirect ke halaman rapat
redirect('admin/rapat.php');
?>
