<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil ID presensi
$id = isset($_GET['id']) ? clean($_GET['id']) : 0;

// Ambil data presensi
$query = "SELECT p.*, u.nik, u.nama, u.bidang, u.jabatan 
          FROM presensi p 
          JOIN users u ON p.user_id = u.id 
          WHERE p.id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Data presensi tidak ditemukan!');
    redirect('admin/monitoring.php');
}

$presensi = mysqli_fetch_assoc($result);

// Proses update keterangan
if (isset($_POST['update'])) {
    $keterangan = clean($_POST['keterangan']);
    
    $query = "UPDATE presensi SET keterangan = '$keterangan' WHERE id = '$id'";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Keterangan berhasil diperbarui!');
    } else {
        setMessage('danger', 'Gagal memperbarui keterangan!');
    }
    
    redirect('admin/detail_presensi.php?id=' . $id);
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Detail Presensi</h1>
        <a href="monitoring.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
    
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informasi Karyawan</h6>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">NIK</th>
                            <td><?php echo $presensi['nik']; ?></td>
                        </tr>
                        <tr>
                            <th>Nama</th>
                            <td><?php echo $presensi['nama']; ?></td>
                        </tr>
                        <tr>
                            <th>Bidang</th>
                            <td><?php echo $presensi['bidang']; ?></td>
                        </tr>
                        <tr>
                            <th>Jabatan</th>
                            <td><?php echo $presensi['jabatan']; ?></td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="card shadow h-100 mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informasi Presensi</h6>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Tanggal</th>
                            <td><?php echo date('d-m-Y', strtotime($presensi['tanggal'])); ?></td>
                        </tr>
                        <tr>
                            <th>Jam Masuk</th>
                            <td><?php echo $presensi['jam_masuk']; ?></td>
                        </tr>
                        <tr>
                            <th>Lokasi Masuk</th>
                            <td><?php echo $presensi['lokasi_masuk']; ?></td>
                        </tr>
                        <tr>
                            <th>Jam Pulang</th>
                            <td><?php echo $presensi['jam_pulang'] ?? '-'; ?></td>
                        </tr>
                        <tr>
                            <th>Lokasi Pulang</th>
                            <td><?php echo $presensi['lokasi_pulang'] ?? '-'; ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <?php if ($presensi['status'] == 'Tepat Waktu'): ?>
                                    <span class="badge bg-success">Tepat Waktu</span>
                                <?php elseif ($presensi['status'] == 'Terlambat'): ?>
                                    <span class="badge bg-warning">Terlambat</span>
                                <?php elseif ($presensi['status'] == 'Pulang Awal'): ?>
                                    <span class="badge bg-warning">Pulang Awal</span>
                                <?php elseif ($presensi['status'] == 'Lembur'): ?>
                                    <span class="badge bg-info">Lembur</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary"><?php echo $presensi['status']; ?></span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="card shadow h-100 mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Keterangan</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="mb-3">
                            <textarea class="form-control" name="keterangan" rows="3"><?php echo $presensi['keterangan'] ?? ''; ?></textarea>
                        </div>
                        <button type="submit" name="update" class="btn btn-primary">Simpan Keterangan</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold">Foto Masuk</h6>
                        </div>
                        <div class="card-body text-center">
                            <?php if (!empty($presensi['foto_masuk'])): ?>
                                <img src="<?php echo BASE_URL . 'uploads/' . $presensi['foto_masuk']; ?>" class="img-fluid" style="max-height: 300px;">
                            <?php else: ?>
                                <div class="alert alert-info">
                                    Tidak ada foto masuk
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold">Foto Pulang</h6>
                        </div>
                        <div class="card-body text-center">
                            <?php if (!empty($presensi['foto_pulang'])): ?>
                                <img src="<?php echo BASE_URL . 'uploads/' . $presensi['foto_pulang']; ?>" class="img-fluid" style="max-height: 300px;">
                            <?php else: ?>
                                <div class="alert alert-info">
                                    Tidak ada foto pulang
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
