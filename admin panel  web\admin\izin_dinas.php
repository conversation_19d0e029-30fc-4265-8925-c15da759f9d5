<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses approval izin dinas
if (isset($_POST['approve'])) {
    $id = clean($_POST['id']);
    $admin_id = $_SESSION['user_id'];

    // Update status izin dinas
    $query = "UPDATE izin_dinas SET status = 'Approved', approved_by = '$admin_id', approved_at = NOW() WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        // Ambil data izin dinas
        $query = "SELECT * FROM izin_dinas WHERE id = '$id'";
        $result = mysqli_query($conn, $query);
        $izin = mysqli_fetch_assoc($result);

        // Cek apakah ini perjalanan dinas yang diajukan setelah absen masuk
        // Perjalanan dinas hari ini bisa diidentifikasi dari tanggal mulai = tanggal selesai = hari ini
        // atau dari adanya presensi masuk pada hari yang sama
        $user_id = $izin['user_id'];
        $tanggal = $izin['tanggal_mulai'];
        $today = date('Y-m-d');

        // Cek apakah ini perjalanan dinas hari ini
        $is_today_trip = ($izin['tanggal_mulai'] == $izin['tanggal_selesai'] && $izin['tanggal_mulai'] == $today);

        // Cek apakah sudah ada presensi untuk tanggal ini
        $query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
        $result = mysqli_query($conn, $query);

        if (mysqli_num_rows($result) > 0) {
            $presensi = mysqli_fetch_assoc($result);

            // Jika sudah ada absen masuk tapi belum ada absen pulang
            if (!empty($presensi['jam_masuk']) && empty($presensi['jam_pulang'])) {
                // Ambil jam kerja untuk hari ini
                $hari_num = date('N', strtotime($tanggal)); // 1 (Senin) sampai 7 (Minggu)
                $hari_names = ['', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
                $hari_name = $hari_names[$hari_num];

                // Ambil bidang_id dari user
                $query = "SELECT bidang_id FROM users WHERE id = '$user_id'";
                $result = mysqli_query($conn, $query);
                $user = mysqli_fetch_assoc($result);
                $bidang_id = $user['bidang_id'];

                // Ambil jam kerja untuk hari ini
                $query = "SELECT jk.* FROM jam_kerja_bidang jkb
                          JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
                          WHERE jkb.bidang_id = '$bidang_id' AND jkb.hari = '$hari_name'";
                $result = mysqli_query($conn, $query);

                if ($result && mysqli_num_rows($result) > 0) {
                    $jam_kerja = mysqli_fetch_assoc($result);
                    $jam_pulang = $jam_kerja['jam_pulang'];

                    // Update presensi dengan absen pulang
                    $keterangan = 'Perjalanan Dinas: ' . $izin['tujuan'];
                    $query = "UPDATE presensi SET
                              jam_pulang = '$jam_pulang',
                              lokasi_pulang = 'Perjalanan Dinas',
                              status = 'Tepat Waktu',
                              keterangan = '$keterangan'
                              WHERE id = '{$presensi['id']}'";

                    if (mysqli_query($conn, $query)) {
                        // Log untuk debugging
                        error_log("Absen pulang otomatis berhasil diisi untuk user ID: $user_id pada tanggal: $tanggal");
                    } else {
                        // Log error
                        error_log("Gagal mengisi absen pulang otomatis: " . mysqli_error($conn));
                    }
                } else {
                    // Log jika jam kerja tidak ditemukan
                    error_log("Jam kerja tidak ditemukan untuk bidang ID: $bidang_id, hari: $hari_name");
                }
            } else {
                // Log status presensi
                if (!empty($presensi['jam_masuk']) && !empty($presensi['jam_pulang'])) {
                    error_log("Presensi sudah lengkap (masuk dan pulang) untuk user ID: $user_id pada tanggal: $tanggal");
                } else if (empty($presensi['jam_masuk'])) {
                    error_log("Presensi belum memiliki absen masuk untuk user ID: $user_id pada tanggal: $tanggal");
                }
            }
        } else {
            // Log jika presensi tidak ditemukan
            error_log("Presensi tidak ditemukan untuk user ID: $user_id pada tanggal: $tanggal");
        }

        // Ambil data jam kerja bidang untuk karyawan
        $user_id = $izin['user_id'];
        $query = "SELECT bidang_id FROM users WHERE id = '$user_id'";
        $result = mysqli_query($conn, $query);
        $user = mysqli_fetch_assoc($result);
        $bidang_id = $user['bidang_id'];

        // Hitung jumlah hari antara tanggal mulai dan selesai
        $tanggal_mulai = new DateTime($izin['tanggal_mulai']);
        $tanggal_selesai = new DateTime($izin['tanggal_selesai']);
        $tanggal_selesai->modify('+1 day'); // Tambahkan 1 hari agar tanggal selesai juga dihitung

        $interval = new DateInterval('P1D');
        $daterange = new DatePeriod($tanggal_mulai, $interval, $tanggal_selesai);

        // Loop untuk setiap hari dalam rentang tanggal
        foreach ($daterange as $date) {
            $tanggal = $date->format('Y-m-d');
            $hari_num = $date->format('N'); // 1 (Senin) sampai 7 (Minggu)
            $hari_names = ['', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
            $hari_name = $hari_names[$hari_num];

            // Cek apakah hari ini adalah hari kerja
            $query = "SELECT status FROM hari_kerja WHERE bidang_id = '$bidang_id' AND hari = '$hari_name'";
            $result = mysqli_query($conn, $query);

            if ($result && mysqli_num_rows($result) > 0) {
                $hari_kerja = mysqli_fetch_assoc($result);

                // Jika hari ini adalah hari kerja
                if ($hari_kerja['status']) {
                    // Cek apakah sudah ada presensi untuk tanggal ini
                    $query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
                    $result = mysqli_query($conn, $query);

                    if (mysqli_num_rows($result) == 0) {
                        // Ambil jam kerja untuk hari ini
                        $query = "SELECT jk.* FROM jam_kerja_bidang jkb
                                  JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
                                  WHERE jkb.bidang_id = '$bidang_id' AND jkb.hari = '$hari_name'";
                        $result = mysqli_query($conn, $query);

                        if ($result && mysqli_num_rows($result) > 0) {
                            $jam_kerja = mysqli_fetch_assoc($result);

                            // Buat presensi otomatis
                            $jam_masuk = $jam_kerja['jam_masuk'];
                            $jam_pulang = $jam_kerja['jam_pulang'];
                            $status = 'Hadir';
                            $keterangan = 'Perjalanan Dinas: ' . $izin['tujuan'];

                            $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, jam_pulang, status, keterangan)
                                      VALUES ('$user_id', '$tanggal', '$jam_masuk', '$jam_pulang', '$status', '$keterangan')";
                            mysqli_query($conn, $query);
                        }
                    }
                }
            }
        }

        // Cek apakah ada presensi yang diupdate dengan absen pulang
        if (isset($presensi) && !empty($presensi['jam_masuk']) && empty($presensi['jam_pulang'])) {
            setMessage('success', 'Izin dinas berhasil disetujui! Absen pulang otomatis telah diisi dan presensi otomatis telah dibuat untuk hari-hari berikutnya.');
        } else {
            setMessage('success', 'Izin dinas berhasil disetujui dan presensi otomatis telah dibuat!');
        }
    } else {
        setMessage('danger', 'Gagal menyetujui izin dinas!');
    }

    redirect('admin/izin_dinas.php');
}

// Proses reject izin dinas
if (isset($_POST['reject'])) {
    $id = clean($_POST['id']);
    $admin_id = $_SESSION['user_id'];

    // Update status izin dinas
    $query = "UPDATE izin_dinas SET status = 'Rejected', approved_by = '$admin_id', approved_at = NOW() WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Izin dinas berhasil ditolak!');
    } else {
        setMessage('danger', 'Gagal menolak izin dinas!');
    }

    redirect('admin/izin_dinas.php');
}

// Proses hapus izin dinas
if (isset($_POST['delete'])) {
    $id = clean($_POST['id']);

    // Ambil data foto sebelum dihapus
    $query = "SELECT foto_surat_tugas, foto_wajah FROM izin_dinas WHERE id = '$id'";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);

        // Hapus file foto jika ada
        if (!empty($row['foto_surat_tugas']) && !isBase64Image($row['foto_surat_tugas'])) {
            $file_path = dirname(dirname(__FILE__)) . '/uploads/' . $row['foto_surat_tugas'];
            if (file_exists($file_path)) {
                @unlink($file_path);
            }
        }

        if (!empty($row['foto_wajah']) && !isBase64Image($row['foto_wajah'])) {
            $file_path = dirname(dirname(__FILE__)) . '/uploads/' . $row['foto_wajah'];
            if (file_exists($file_path)) {
                @unlink($file_path);
            }
        }

        // Hapus data dari database
        $delete_query = "DELETE FROM izin_dinas WHERE id = '$id'";

        if (mysqli_query($conn, $delete_query)) {
            setMessage('success', 'Izin dinas berhasil dihapus!');
        } else {
            setMessage('danger', 'Gagal menghapus izin dinas: ' . mysqli_error($conn));
        }
    } else {
        setMessage('danger', 'Data izin dinas tidak ditemukan!');
    }

    redirect('admin/izin_dinas.php');
}

// Fungsi untuk memeriksa keberadaan file
function fileExists($path) {
    $fullPath = dirname(dirname(__FILE__)) . '/' . $path;
    return file_exists($fullPath);
}

// Fungsi untuk memeriksa apakah string adalah data base64
function isBase64Image($string) {
    return (
        is_string($string) &&
        strlen($string) > 100 &&
        strpos($string, 'data:image') === 0 &&
        strpos($string, 'base64') !== false
    );
}

// Fungsi untuk menyimpan gambar base64 ke file
function saveBase64Image($base64String, $prefix = 'img_') {
    // Ekstrak data gambar dari string base64
    $matches = [];
    preg_match('/data:image\/([a-zA-Z]+);base64,([^\'"]+)/', $base64String, $matches);

    if (count($matches) < 3) {
        return false;
    }

    $imageType = $matches[1];
    $imageData = base64_decode($matches[2]);

    // Buat nama file unik
    $filename = $prefix . uniqid() . '.' . $imageType;
    $uploadPath = dirname(dirname(__FILE__)) . '/uploads/' . $filename;

    // Simpan file
    if (file_put_contents($uploadPath, $imageData)) {
        return $filename;
    }

    return false;
}

// Ambil data izin dinas
$query = "SELECT id.*, u.nik, u.nama, a.nama as approved_by_name
          FROM izin_dinas id
          JOIN users u ON id.user_id = u.id
          LEFT JOIN users a ON id.approved_by = a.id
          ORDER BY id.created_at DESC";
$result = mysqli_query($conn, $query);

$izin_dinas = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        // Periksa dan tangani foto surat tugas
        if (!empty($row['foto_surat_tugas'])) {
            // Cek apakah foto adalah data base64
            if (isBase64Image($row['foto_surat_tugas'])) {
                // Simpan gambar base64 ke file
                $filename = saveBase64Image($row['foto_surat_tugas'], 'surat_');
                if ($filename) {
                    // Update database dengan nama file baru
                    $updateQuery = "UPDATE izin_dinas SET foto_surat_tugas = '$filename' WHERE id = '{$row['id']}'";
                    mysqli_query($conn, $updateQuery);

                    // Update data row
                    $row['foto_surat_tugas'] = $filename;
                    $row['foto_surat_exists'] = true;
                    $row['foto_surat_converted'] = true;
                } else {
                    $row['foto_surat_exists'] = false;
                    $row['foto_surat_error'] = 'Gagal menyimpan gambar base64';
                }
            } else {
                // Cek keberadaan file
                $row['foto_surat_exists'] = fileExists('uploads/' . $row['foto_surat_tugas']);
            }
        } else {
            $row['foto_surat_exists'] = false;
        }

        // Periksa dan tangani foto wajah
        if (!empty($row['foto_wajah'])) {
            // Cek apakah foto adalah data base64
            if (isBase64Image($row['foto_wajah'])) {
                // Simpan gambar base64 ke file
                $filename = saveBase64Image($row['foto_wajah'], 'wajah_');
                if ($filename) {
                    // Update database dengan nama file baru
                    $updateQuery = "UPDATE izin_dinas SET foto_wajah = '$filename' WHERE id = '{$row['id']}'";
                    mysqli_query($conn, $updateQuery);

                    // Update data row
                    $row['foto_wajah'] = $filename;
                    $row['foto_wajah_exists'] = true;
                    $row['foto_wajah_converted'] = true;
                } else {
                    $row['foto_wajah_exists'] = false;
                    $row['foto_wajah_error'] = 'Gagal menyimpan gambar base64';
                }
            } else {
                // Cek keberadaan file
                $row['foto_wajah_exists'] = fileExists('uploads/' . $row['foto_wajah']);
            }
        } else {
            $row['foto_wajah_exists'] = false;
        }

        $izin_dinas[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Manajemen Izin Perjalanan Dinas</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Data Izin Perjalanan Dinas</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>NIK</th>
                            <th>Nama Karyawan</th>
                            <th>Tanggal Mulai</th>
                            <th>Tanggal Selesai</th>
                            <th>Tujuan</th>
                            <th>Keterangan</th>
                            <th>Foto Surat</th>
                            <th>Foto Wajah</th>
                            <th>Status</th>
                            <th>Disetujui Oleh</th>
                            <th>Tanggal Disetujui</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($izin_dinas)): ?>
                            <tr>
                                <td colspan="12" class="text-center">Tidak ada data izin perjalanan dinas</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($izin_dinas as $izin): ?>
                                <tr>
                                    <td><?php echo $izin['nik']; ?></td>
                                    <td><?php echo $izin['nama']; ?></td>
                                    <td><?php echo date('d-m-Y', strtotime($izin['tanggal_mulai'])); ?></td>
                                    <td><?php echo date('d-m-Y', strtotime($izin['tanggal_selesai'])); ?></td>
                                    <td><?php echo $izin['tujuan']; ?></td>
                                    <td><?php echo $izin['keterangan']; ?></td>
                                    <td>
                                        <?php if (!empty($izin['foto_surat_tugas'])): ?>
                                            <?php if ($izin['foto_surat_exists']): ?>
                                                <a href="#" class="view-image" data-bs-toggle="modal" data-bs-target="#imageModal" data-src="<?php echo BASE_URL . 'uploads/' . $izin['foto_surat_tugas']; ?>" data-title="Surat Tugas - <?php echo $izin['nama']; ?>">
                                                    <img src="<?php echo BASE_URL . 'uploads/' . $izin['foto_surat_tugas']; ?>" class="img-thumbnail" style="max-height: 50px; max-width: 50px;" alt="Surat Tugas">
                                                </a>
                                                <small class="d-block text-success mt-1">
                                                    File ada
                                                    <?php if (isset($izin['foto_surat_converted']) && $izin['foto_surat_converted']): ?>
                                                        <span class="badge bg-info">Dikonversi</span>
                                                    <?php endif; ?>
                                                </small>
                                            <?php else: ?>
                                                <div class="text-danger">
                                                    <i class="fas fa-exclamation-triangle"></i> File tidak ditemukan
                                                    <small class="d-block mt-1">
                                                        <?php
                                                        if (isBase64Image($izin['foto_surat_tugas'])) {
                                                            echo "Data Base64";
                                                            if (isset($izin['foto_surat_error'])) {
                                                                echo " - " . $izin['foto_surat_error'];
                                                            }
                                                        } else {
                                                            echo substr($izin['foto_surat_tugas'], 0, 15) . '...';
                                                        }
                                                        ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">Tidak ada</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($izin['foto_wajah'])): ?>
                                            <?php if ($izin['foto_wajah_exists']): ?>
                                                <a href="#" class="view-image" data-bs-toggle="modal" data-bs-target="#imageModal" data-src="<?php echo BASE_URL . 'uploads/' . $izin['foto_wajah']; ?>" data-title="Foto Wajah - <?php echo $izin['nama']; ?>">
                                                    <img src="<?php echo BASE_URL . 'uploads/' . $izin['foto_wajah']; ?>" class="img-thumbnail" style="max-height: 50px; max-width: 50px;" alt="Foto Wajah">
                                                </a>
                                                <small class="d-block text-success mt-1">
                                                    File ada
                                                    <?php if (isset($izin['foto_wajah_converted']) && $izin['foto_wajah_converted']): ?>
                                                        <span class="badge bg-info">Dikonversi</span>
                                                    <?php endif; ?>
                                                </small>
                                            <?php else: ?>
                                                <div class="text-danger">
                                                    <i class="fas fa-exclamation-triangle"></i> File tidak ditemukan
                                                    <small class="d-block mt-1">
                                                        <?php
                                                        if (isBase64Image($izin['foto_wajah'])) {
                                                            echo "Data Base64";
                                                            if (isset($izin['foto_wajah_error'])) {
                                                                echo " - " . $izin['foto_wajah_error'];
                                                            }
                                                        } else {
                                                            echo substr($izin['foto_wajah'], 0, 15) . '...';
                                                        }
                                                        ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">Tidak ada</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($izin['status'] == 'Pending'): ?>
                                            <span class="badge bg-warning">Pending</span>
                                        <?php elseif ($izin['status'] == 'Approved'): ?>
                                            <span class="badge bg-success">Disetujui</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Ditolak</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $izin['approved_by_name'] ?? '-'; ?></td>
                                    <td><?php echo $izin['approved_at'] ? date('d-m-Y H:i', strtotime($izin['approved_at'])) : '-'; ?></td>
                                    <td>
                                        <?php if ($izin['status'] == 'Pending'): ?>
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="id" value="<?php echo $izin['id']; ?>">
                                                <button type="submit" name="approve" class="btn btn-sm btn-success" onclick="return confirm('Apakah Anda yakin ingin menyetujui izin dinas ini?')">
                                                    <i class="fas fa-check"></i> Setujui
                                                </button>
                                            </form>
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="id" value="<?php echo $izin['id']; ?>">
                                                <button type="submit" name="reject" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menolak izin dinas ini?')">
                                                    <i class="fas fa-times"></i> Tolak
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <button class="btn btn-sm btn-secondary" disabled>
                                                <i class="fas fa-lock"></i> Sudah Diproses
                                            </button>
                                        <?php endif; ?>

                                        <!-- Tombol Delete untuk semua status -->
                                        <form method="post" class="d-inline mt-1">
                                            <input type="hidden" name="id" value="<?php echo $izin['id']; ?>">
                                            <button type="submit" name="delete" class="btn btn-sm btn-outline-danger" onclick="return confirm('PERHATIAN! Apakah Anda yakin ingin menghapus izin dinas ini? Data yang dihapus tidak dapat dikembalikan.')">
                                                <i class="fas fa-trash"></i> Hapus
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Informasi Izin Perjalanan Dinas</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5 class="alert-heading">Ketentuan Izin Perjalanan Dinas:</h5>
                <ul>
                    <li>Izin perjalanan dinas diajukan oleh karyawan melalui halaman karyawan.</li>
                    <li>Karyawan dapat mengajukan perjalanan dinas pada hari yang sama setelah melakukan absen masuk.</li>
                    <li>Admin dapat menyetujui atau menolak izin perjalanan dinas.</li>
                    <li>Jika izin disetujui, sistem akan otomatis membuat presensi untuk karyawan selama periode perjalanan dinas.</li>
                    <li>Untuk perjalanan dinas yang diajukan setelah absen masuk, sistem akan otomatis mengisi absen pulang.</li>
                    <li>Presensi otomatis hanya dibuat untuk hari kerja sesuai dengan pengaturan hari kerja bidang karyawan.</li>
                    <li>Jam masuk dan jam pulang akan diisi sesuai dengan jam kerja yang berlaku pada hari tersebut.</li>
                </ul>
                <hr>
                <p class="mb-0">Pastikan untuk memeriksa detail izin perjalanan dinas sebelum menyetujui atau menolaknya.</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk menampilkan gambar -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Gambar</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <img id="modalImage" src="" class="img-fluid" alt="Gambar" style="max-height: 70vh;">
                </div>
                <!-- Debug info -->
                <div class="alert alert-info mt-3">
                    <small>Path gambar: <span id="imagePath"></span></small>
                    <div id="imageError" class="text-danger mt-2" style="display: none;">
                        <strong>Error:</strong> Gambar tidak dapat dimuat. Pastikan file ada di direktori uploads.
                    </div>
                </div>
                <div class="mt-3">
                    <a id="directImageLink" href="#" target="_blank" class="btn btn-sm btn-primary">
                        <i class="fas fa-external-link-alt me-1"></i> Buka di Tab Baru
                    </a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Event listener untuk melihat gambar
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.view-image').forEach(function(element) {
            element.addEventListener('click', function() {
                const src = this.getAttribute('data-src');
                const title = this.getAttribute('data-title');

                // Set judul dan path
                document.getElementById('imageModalLabel').textContent = title;
                document.getElementById('imagePath').textContent = src;
                document.getElementById('directImageLink').href = src;

                // Reset error message
                document.getElementById('imageError').style.display = 'none';

                // Set image source
                const modalImage = document.getElementById('modalImage');
                modalImage.src = src;

                // Handle image load error
                modalImage.onerror = function() {
                    document.getElementById('imageError').style.display = 'block';
                    console.error('Failed to load image:', src);
                };

                // Handle image load success
                modalImage.onload = function() {
                    document.getElementById('imageError').style.display = 'none';
                    console.log('Image loaded successfully:', src);
                };
            });
        });
    });
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
