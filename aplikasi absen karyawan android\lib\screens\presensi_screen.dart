import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../providers/auth_provider.dart';
import '../providers/presensi_provider.dart';
import '../providers/dashboard_provider.dart';
import '../utils/app_colors.dart';
import '../widgets/bottom_navigation.dart';

class PresensiScreen extends StatefulWidget {
  const PresensiScreen({super.key});

  @override
  State<PresensiScreen> createState() => _PresensiScreenState();
}

class _PresensiScreenState extends State<PresensiScreen> {
  String? jenisAbsen;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments as String?;
      if (args != null) {
        setState(() {
          jenisAbsen = args;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Presensi ${jenisAbsen?.toUpperCase() ?? ''}'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer3<AuthProvider, PresensiProvider, DashboardProvider>(
        builder: (context, authProvider, presensiProvider, dashboardProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Info Card
                _buildInfoCard(),
                
                const SizedBox(height: 20),
                
                // Camera Section
                _buildCameraSection(presensiProvider),
                
                const SizedBox(height: 20),
                
                // Location Section
                _buildLocationSection(presensiProvider),
                
                const SizedBox(height: 30),
                
                // Submit Button
                _buildSubmitButton(authProvider, presensiProvider, dashboardProvider),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: const BottomNavigation(currentIndex: 1),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                jenisAbsen == 'masuk' ? Icons.login : Icons.logout,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Absen ${jenisAbsen?.toUpperCase() ?? ''}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            jenisAbsen == 'masuk' 
                ? 'Silakan ambil foto selfie dan pastikan lokasi Anda sudah benar untuk melakukan absen masuk.'
                : 'Silakan ambil foto selfie dan pastikan lokasi Anda sudah benar untuk melakukan absen pulang.',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraSection(PresensiProvider presensiProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Foto Selfie',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: presensiProvider.selectedImage != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.file(
                    presensiProvider.selectedImage!,
                    fit: BoxFit.cover,
                  ),
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.camera_alt,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Belum ada foto',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: presensiProvider.isLoading ? null : () async {
              final success = await presensiProvider.takePicture();
              if (!success && presensiProvider.errorMessage != null) {
                Fluttertoast.showToast(
                  msg: presensiProvider.errorMessage!,
                  backgroundColor: AppColors.error,
                  textColor: Colors.white,
                );
              }
            },
            icon: const Icon(Icons.camera_alt),
            label: Text(presensiProvider.selectedImage != null ? 'Ambil Ulang Foto' : 'Ambil Foto'),
          ),
        ),
      ],
    );
  }

  Widget _buildLocationSection(PresensiProvider presensiProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Lokasi',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    color: presensiProvider.currentPosition != null 
                        ? AppColors.success 
                        : Colors.grey[400],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      presensiProvider.currentPosition != null
                          ? presensiProvider.getLocationString()
                          : 'Lokasi belum dideteksi',
                      style: TextStyle(
                        fontSize: 14,
                        color: presensiProvider.currentPosition != null
                            ? AppColors.textPrimary
                            : Colors.grey[600],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: presensiProvider.isLoading ? null : () async {
                    final success = await presensiProvider.getCurrentLocation();
                    if (!success && presensiProvider.errorMessage != null) {
                      Fluttertoast.showToast(
                        msg: presensiProvider.errorMessage!,
                        backgroundColor: AppColors.error,
                        textColor: Colors.white,
                      );
                    }
                  },
                  icon: presensiProvider.isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.my_location),
                  label: Text(presensiProvider.currentPosition != null ? 'Perbarui Lokasi' : 'Dapatkan Lokasi'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(AuthProvider authProvider, PresensiProvider presensiProvider, DashboardProvider dashboardProvider) {
    final canSubmit = presensiProvider.selectedImage != null && 
                     presensiProvider.currentPosition != null &&
                     jenisAbsen != null &&
                     !presensiProvider.isLoading;

    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: canSubmit ? () async {
          final result = await presensiProvider.submitPresensi(
            userId: authProvider.user!.id,
            jenisAbsen: jenisAbsen!,
          );

          if (result['success']) {
            Fluttertoast.showToast(
              msg: result['message'],
              backgroundColor: AppColors.success,
              textColor: Colors.white,
            );
            
            // Refresh dashboard data
            await dashboardProvider.loadDashboardData(authProvider.user!.id);
            
            // Navigate back to dashboard
            Navigator.pushNamedAndRemoveUntil(
              context, 
              '/dashboard', 
              (route) => false,
            );
          } else {
            Fluttertoast.showToast(
              msg: result['message'],
              backgroundColor: AppColors.error,
              textColor: Colors.white,
            );
          }
        } : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: jenisAbsen == 'masuk' ? AppColors.success : AppColors.warning,
        ),
        child: presensiProvider.isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                'Submit Absen ${jenisAbsen?.toUpperCase() ?? ''}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }
}
