<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';

// Cek akses
if (!isset($_SESSION['role']) || $_SESSION['role'] != 'admin') {
    header("Location: " . BASE_URL . "index.php");
    exit();
}

// Baca file SQL
$sql_file = '../db/aktivitas_karyawan.sql';

if (file_exists($sql_file)) {
    $sql = file_get_contents($sql_file);
    
    // Eksekusi query
    if (mysqli_query($conn, $sql)) {
        echo "<div class='alert alert-success'>Tabel aktivitas_karyawan berhasil dibuat!</div>";
    } else {
        echo "<div class='alert alert-danger'>Gagal membuat tabel aktivitas_karyawan: " . mysqli_error($conn) . "</div>";
    }
} else {
    echo "<div class='alert alert-danger'>File SQL tidak ditemukan!</div>";
}

// Redirect setelah 3 detik
header("refresh:3;url=" . BASE_URL . "admin/aktivitas_karyawan.php");
?>
