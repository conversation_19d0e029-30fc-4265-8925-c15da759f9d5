import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/presensi_model.dart';
import '../services/api_service.dart';

class RiwayatProvider with ChangeNotifier {
  UserModel? _karyawan;
  List<PresensiModel> _presensiList = [];
  StatistikModel? _statistik;
  Map<String, dynamic>? _periode;
  bool _isLoading = false;
  String? _errorMessage;
  int _selectedMonth = DateTime.now().month;
  int _selectedYear = DateTime.now().year;

  // Getters
  UserModel? get karyawan => _karyawan;
  List<PresensiModel> get presensiList => _presensiList;
  StatistikModel? get statistik => _statistik;
  Map<String, dynamic>? get periode => _periode;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  int get selectedMonth => _selectedMonth;
  int get selectedYear => _selectedYear;

  // Month names
  static const List<String> monthNames = [
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'September', 'Oktober', 'November', 'Desember'
  ];

  String get selectedMonthName => monthNames[_selectedMonth - 1];

  Future<void> loadRiwayatData(int userId, {int? bulan, int? tahun}) async {
    _isLoading = true;
    _errorMessage = null;
    
    if (bulan != null) _selectedMonth = bulan;
    if (tahun != null) _selectedYear = tahun;
    
    notifyListeners();

    try {
      final response = await ApiService.getRiwayat(
        userId: userId,
        bulan: _selectedMonth,
        tahun: _selectedYear,
      );
      
      if (response['status'] == 'success') {
        final data = response['data'];
        
        // Parse karyawan data
        if (data['karyawan'] != null) {
          _karyawan = UserModel(
            id: userId,
            nik: data['karyawan']['nik'] ?? '',
            nama: data['karyawan']['nama'] ?? '',
            bidangNama: data['karyawan']['bidang_nama'],
          );
        }
        
        // Parse periode
        _periode = data['periode'];
        
        // Parse statistik
        if (data['statistik'] != null) {
          _statistik = StatistikModel.fromJson(data['statistik']);
        }
        
        // Parse presensi list
        _presensiList = [];
        if (data['presensi'] != null && data['presensi'] is List) {
          for (var item in data['presensi']) {
            _presensiList.add(PresensiModel.fromJson(item));
          }
        }
        
        _isLoading = false;
        notifyListeners();
      } else {
        _errorMessage = response['message'] ?? 'Gagal memuat data riwayat';
        _isLoading = false;
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'Terjadi kesalahan: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
    }
  }

  void changeMonth(int month) {
    if (month >= 1 && month <= 12 && month != _selectedMonth) {
      _selectedMonth = month;
      notifyListeners();
    }
  }

  void changeYear(int year) {
    if (year >= 2020 && year <= 2030 && year != _selectedYear) {
      _selectedYear = year;
      notifyListeners();
    }
  }

  void previousMonth() {
    if (_selectedMonth > 1) {
      _selectedMonth--;
    } else {
      _selectedMonth = 12;
      _selectedYear--;
    }
    notifyListeners();
  }

  void nextMonth() {
    if (_selectedMonth < 12) {
      _selectedMonth++;
    } else {
      _selectedMonth = 1;
      _selectedYear++;
    }
    notifyListeners();
  }

  void clearData() {
    _karyawan = null;
    _presensiList = [];
    _statistik = null;
    _periode = null;
    _errorMessage = null;
    notifyListeners();
  }

  // Helper methods
  List<PresensiModel> getPresensiByStatus(String status) {
    return _presensiList.where((p) => p.status == status).toList();
  }

  List<PresensiModel> getPresensiTerlambat() {
    return getPresensiByStatus('Terlambat');
  }

  List<PresensiModel> getPresensiTepatWaktu() {
    return getPresensiByStatus('Tepat Waktu');
  }

  bool hasDataForSelectedPeriod() {
    return _presensiList.isNotEmpty;
  }

  String formatTanggal(String tanggal) {
    try {
      final date = DateTime.parse(tanggal);
      final dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
      final dayName = dayNames[date.weekday % 7];
      return '$dayName, ${date.day} ${monthNames[date.month - 1]} ${date.year}';
    } catch (e) {
      return tanggal;
    }
  }

  String formatJam(String? jam) {
    if (jam == null) return '-';
    try {
      final time = DateTime.parse('2000-01-01 $jam');
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return jam;
    }
  }

  Color getStatusColor(String status) {
    switch (status) {
      case 'Tepat Waktu':
        return Colors.green;
      case 'Terlambat':
        return Colors.red;
      case 'Pulang Awal':
        return Colors.orange;
      case 'Lembur':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData getStatusIcon(String status) {
    switch (status) {
      case 'Tepat Waktu':
        return Icons.check_circle;
      case 'Terlambat':
        return Icons.access_time;
      case 'Pulang Awal':
        return Icons.exit_to_app;
      case 'Lembur':
        return Icons.work;
      default:
        return Icons.help;
    }
  }
}
