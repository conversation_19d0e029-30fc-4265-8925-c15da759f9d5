// Service Worker untuk Aplikasi Absensi - Versi Webhosting
const CACHE_NAME = 'absensiku-cache-v4';
const OFFLINE_URL = '/offline.html';

// Aset-aset yang akan di-cache saat instalasi
const ASSETS_TO_CACHE = [
  OFFLINE_URL,
  '/assets/css/android-login.css',
  '/assets/js/particles.js',
  '/assets/images/offline.svg',
  '/assets/images/icon-192x192.png',
  '/assets/images/icon-512x512.png',
  '/manifest.json',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
  'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js',
  'https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css',
  'https://cdn.jsdelivr.net/npm/sweetalert2@11'
];

// Fungsi untuk mendapatkan path relatif yang benar
function getCorrectPath(url) {
  // Untuk URL absolut (dimulai dengan http atau https), gunakan apa adanya
  if (url.startsWith('http')) {
    return url;
  }

  // Dapatkan base path dari URL saat ini
  const basePath = self.location.pathname.split('/').slice(0, -1).join('/');

  // Jika URL dimulai dengan '/', sesuaikan dengan domain
  if (url.startsWith('/') && !url.startsWith('//')) {
    // Hapus '/' di awal
    const cleanUrl = url.substring(1);

    // Jika basePath kosong, kembalikan url asli
    if (basePath === '') {
      return cleanUrl;
    }

    // Gabungkan basePath dengan url
    return `${basePath}/${cleanUrl}`;
  }

  return url;
}

// Event saat Service Worker diinstal
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Install');

  // Tunggu hingga cache dibuat dan aset-aset disimpan
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] Caching all assets');
        // Ubah path untuk webhosting
        const assetsToCache = ASSETS_TO_CACHE.map(url => {
          // Jika URL adalah URL eksternal (dimulai dengan http atau https), gunakan apa adanya
          if (url.startsWith('http')) {
            return url;
          }
          // Jika tidak, dapatkan path yang benar
          return getCorrectPath(url);
        });

        // Pastikan offline.html selalu di-cache terlebih dahulu
        return cache.add(getCorrectPath(OFFLINE_URL))
          .then(() => {
            console.log('[Service Worker] Offline page cached');
            return cache.addAll(assetsToCache);
          });
      })
      .then(() => {
        console.log('[Service Worker] Cached all assets');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[Service Worker] Cache failed:', error);
      })
  );
});

// Event saat Service Worker diaktifkan
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activate');

  // Hapus cache lama
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('[Service Worker] Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('[Service Worker] Claiming clients');
      return self.clients.claim();
    })
  );
});

// Event saat ada permintaan fetch
self.addEventListener('fetch', (event) => {
  // Jangan intercept request ke API atau endpoint dinamis
  if (event.request.url.includes('/api/') ||
      event.request.url.includes('chrome-extension://') ||
      event.request.method !== 'GET') {
    return;
  }

  // Jika request adalah navigasi (halaman HTML) dan pengguna offline, langsung tampilkan halaman offline
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // Jika response OK, clone dan simpan di cache
          if (response && response.status === 200) {
            const responseToCache = response.clone();
            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache);
              });
          }
          return response;
        })
        .catch(error => {
          console.log('[Service Worker] Navigation fetch failed; showing offline page', error);

          // Langsung kembalikan halaman offline untuk navigasi yang gagal
          return caches.match(getCorrectPath(OFFLINE_URL))
            .then(offlineResponse => {
              if (offlineResponse) {
                return offlineResponse;
              }

              // Jika halaman offline tidak ditemukan di cache, coba ambil dari jaringan
              return fetch(getCorrectPath(OFFLINE_URL))
                .catch(() => {
                  // Jika semua gagal, kembalikan respons sederhana
                  return new Response('Anda sedang offline. Halaman offline tidak tersedia.', {
                    status: 503,
                    headers: { 'Content-Type': 'text/plain' }
                  });
                });
            });
        })
    );
    return;
  }

  // Untuk request non-navigasi (aset statis, dll)
  event.respondWith(
    fetch(event.request)
      .then(response => {
        // Jika response OK, clone dan simpan di cache
        if (response && response.status === 200) {
          const responseToCache = response.clone();
          caches.open(CACHE_NAME)
            .then(cache => {
              cache.put(event.request, responseToCache);
            });
        }
        return response;
      })
      .catch(error => {
        console.log('[Service Worker] Fetch failed; returning from cache instead.', error);

        return caches.match(event.request)
          .then(cachedResponse => {
            // Jika ada di cache, kembalikan dari cache
            if (cachedResponse) {
              return cachedResponse;
            }

            // Untuk request lain yang gagal, kembalikan error
            return new Response('Network error happened', {
              status: 408,
              headers: { 'Content-Type': 'text/plain' }
            });
          });
      })
  );
});
