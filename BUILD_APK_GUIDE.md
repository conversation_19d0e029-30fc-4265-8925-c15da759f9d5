# 📱 Panduan Build APK Aplikasi Absensi Karyawan

## 🔧 Persiapan Environment

### 1. Install Flutter SDK

#### Windows:
1. Download Flutter SDK dari: https://docs.flutter.dev/get-started/install/windows
2. Extract ke folder (misal: `C:\flutter`)
3. Tambahkan `C:\flutter\bin` ke PATH environment variable
4. Buka Command Prompt dan jalankan: `flutter doctor`

#### macOS:
```bash
# Install menggunakan Homebrew
brew install flutter

# Atau download manual dari:
# https://docs.flutter.dev/get-started/install/macos
```

#### Linux:
```bash
# Download dan extract Flutter
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.16.0-stable.tar.xz
tar xf flutter_linux_3.16.0-stable.tar.xz

# Tambahkan ke PATH
export PATH="$PATH:`pwd`/flutter/bin"
```

### 2. Install Android Studio
1. Download dari: https://developer.android.com/studio
2. Install Android SDK dan Android SDK Command-line Tools
3. Setup Android emulator (opsional)

### 3. Verifikasi Installation
```bash
flutter doctor
```
Pastikan semua checklist hijau atau minimal Android toolchain tersedia.

## 🏗 Build Process

### 1. Navigate ke Project Directory
```bash
cd "aplikasi absen karyawan android"
```

### 2. Install Dependencies
```bash
flutter pub get
```

### 3. Update Konfigurasi API
Edit file `lib/utils/constants.dart` dan ganti URL dengan server Anda:
```dart
static const String baseUrl = 'http://YOUR_SERVER_IP/absenku/admin%20panel%20%20web/api';
```

### 4. Build APK
```bash
# Build debug APK (untuk testing)
flutter build apk --debug

# Build release APK (untuk production)
flutter build apk --release
```

### 5. Lokasi File APK
Setelah build selesai, file APK akan tersedia di:
```
aplikasi absen karyawan android/build/app/outputs/flutter-apk/
├── app-debug.apk      (untuk debug)
└── app-release.apk    (untuk release)
```

## 📱 Install APK di Android

### 1. Enable Unknown Sources
Di perangkat Android:
1. Buka **Settings** > **Security** > **Unknown Sources**
2. Atau **Settings** > **Apps** > **Special Access** > **Install Unknown Apps**
3. Enable untuk browser atau file manager yang akan digunakan

### 2. Transfer APK
- Copy file APK ke perangkat Android via USB, email, atau cloud storage
- Atau download langsung di perangkat jika APK di-host online

### 3. Install APK
1. Buka file manager di Android
2. Navigate ke lokasi file APK
3. Tap file APK
4. Tap **Install**
5. Tunggu proses instalasi selesai

## 🔧 Troubleshooting

### Error: "Flutter not found"
```bash
# Pastikan Flutter ada di PATH
echo $PATH  # Linux/macOS
echo %PATH% # Windows

# Atau jalankan dengan full path
/path/to/flutter/bin/flutter build apk --release
```

### Error: "Android SDK not found"
```bash
# Set ANDROID_HOME environment variable
export ANDROID_HOME=/path/to/android/sdk  # Linux/macOS
set ANDROID_HOME=C:\path\to\android\sdk   # Windows

# Atau install via Android Studio
flutter doctor --android-licenses
```

### Error: "Gradle build failed"
```bash
# Clean project
flutter clean
flutter pub get

# Rebuild
flutter build apk --release
```

### Error: "Insufficient storage"
- Pastikan ada minimal 2GB free space
- Clean project: `flutter clean`
- Clear Gradle cache: `./gradlew clean` (di folder android)

## 🚀 Optimasi APK

### 1. Reduce APK Size
```bash
# Build dengan obfuscation
flutter build apk --release --obfuscate --split-debug-info=build/debug-info

# Build App Bundle (lebih kecil)
flutter build appbundle --release
```

### 2. Split APK by Architecture
```bash
# Build terpisah untuk setiap arsitektur
flutter build apk --release --split-per-abi
```
Ini akan menghasilkan:
- `app-arm64-v8a-release.apk` (untuk device 64-bit modern)
- `app-armeabi-v7a-release.apk` (untuk device 32-bit)
- `app-x86_64-release.apk` (untuk emulator/device x86)

## 📋 Checklist Sebelum Build

- [ ] Flutter SDK terinstall dan di PATH
- [ ] Android SDK terinstall
- [ ] `flutter doctor` menunjukkan status OK
- [ ] Dependencies ter-install (`flutter pub get`)
- [ ] URL API sudah dikonfigurasi dengan benar
- [ ] Permissions di AndroidManifest.xml sudah sesuai
- [ ] App name dan package name sudah benar

## 🔐 Signing APK (Opsional)

Untuk production, sebaiknya sign APK dengan keystore:

### 1. Generate Keystore
```bash
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

### 2. Configure Signing
Buat file `android/key.properties`:
```properties
storePassword=<password>
keyPassword=<password>
keyAlias=upload
storeFile=<path-to-keystore>
```

### 3. Update build.gradle
File sudah dikonfigurasi untuk signing, tinggal uncomment bagian signing config.

## 📱 Testing APK

### 1. Install di Test Device
- Install APK di beberapa device dengan Android version berbeda
- Test semua fitur: login, camera, GPS, dll

### 2. Check Permissions
- Pastikan app meminta permission camera dan location
- Test dengan permission denied/granted

### 3. Network Testing
- Test dengan WiFi dan mobile data
- Test dengan koneksi lambat
- Test error handling saat offline

## 🎯 Quick Build Commands

```bash
# Complete build process
cd "aplikasi absen karyawan android"
flutter clean
flutter pub get
flutter build apk --release

# File APK akan tersedia di:
# build/app/outputs/flutter-apk/app-release.apk
```

## 📞 Support

Jika mengalami masalah saat build:
1. Cek `flutter doctor` untuk memastikan environment setup
2. Cek log error untuk detail masalah
3. Pastikan semua dependencies ter-install dengan benar
4. Coba clean project dan rebuild

---

**Catatan Penting**: 
- Pastikan server backend sudah running sebelum test APK
- Update URL API sesuai dengan server Anda
- Test APK di device fisik untuk hasil terbaik
