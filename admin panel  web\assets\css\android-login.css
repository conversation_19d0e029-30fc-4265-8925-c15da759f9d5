/* Android-style Login CSS */

body {
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    height: 100vh;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    overflow-x: hidden;
}

.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.particle {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    pointer-events: none;
    z-index: -1;
}

.android-login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
    position: relative;
    z-index: 1;
}

.android-login-card {
    width: 100%;
    max-width: 400px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.android-login-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.android-login-header {
    padding: 30px 20px;
    text-align: center;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.android-login-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
    transform: rotate(30deg);
    z-index: 0;
}

.android-login-header h3 {
    font-size: 28px;
    margin: 0;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.android-login-header p {
    margin: 10px 0 0;
    opacity: 0.8;
    font-size: 16px;
    position: relative;
    z-index: 1;
}

.android-login-body {
    padding: 30px;
}

.android-form-group {
    margin-bottom: 25px;
    position: relative;
}

.android-form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
    font-weight: 500;
    transition: all 0.3s ease;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.android-form-control {
    width: 100%;
    padding: 15px;
    padding-right: 45px;
    border: none;
    border-radius: 12px;
    background-color: #f5f5f5;
    font-size: 16px;
    color: #333;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.android-form-control:focus {
    outline: none;
    background-color: #fff;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(106, 17, 203, 0.1);
}

.android-form-icon {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    color: #6a11cb;
    font-size: 18px;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    width: 40px;
}

.password-toggle {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6a11cb;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.password-toggle:hover {
    color: #2575fc;
}

.password-toggle:focus {
    outline: none;
}

.password-toggle.active i {
    color: #2575fc;
}

/* Remember Me Checkbox */
.remember-me {
    margin-top: 5px;
    margin-bottom: 20px;
}

.remember-label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    user-select: none;
    font-weight: normal;
}

.remember-label input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: relative;
    height: 20px;
    width: 20px;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-right: 10px;
    transition: all 0.3s ease;
    border: 1px solid #ddd;
}

.remember-label:hover input ~ .checkmark {
    background-color: #e9ecef;
}

.remember-label input:checked ~ .checkmark {
    background-color: #6a11cb;
    border-color: #6a11cb;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.remember-label input:checked ~ .checkmark:after {
    display: block;
}

.remember-label .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.android-btn {
    display: block;
    width: 100%;
    padding: 16px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(106, 17, 203, 0.3);
}

.android-btn:hover {
    background: linear-gradient(135deg, #5a0cb1 0%, #1a65e6 100%);
    box-shadow: 0 6px 15px rgba(106, 17, 203, 0.4);
    transform: translateY(-2px);
}

.android-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(106, 17, 203, 0.4);
}

.android-login-footer {
    text-align: center;
    margin-top: 25px;
}

.android-login-footer a {
    display: inline-block;
    margin: 10px;
    color: #6a11cb;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.android-login-footer a:hover {
    color: #2575fc;
    transform: translateY(-2px);
}

.android-login-footer a i {
    margin-right: 5px;
}

.android-login-footer .admin-link {
    display: inline-block;
    margin-top: 20px;
    padding: 10px 20px;
    background-color: rgba(106, 17, 203, 0.1);
    border-radius: 50px;
    color: #6a11cb;
    font-weight: 500;
    transition: all 0.3s ease;
}

.android-login-footer .admin-link:hover {
    background-color: rgba(106, 17, 203, 0.2);
    transform: translateY(-2px);
}

.android-login-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
    background-color: white;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.android-login-logo i {
    font-size: 40px;
    color: #6a11cb;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .android-login-card {
        max-width: 100%;
    }

    .android-login-header {
        padding: 20px;
    }

    .android-login-body {
        padding: 20px;
    }
}

/* Alert styling */
.android-alert {
    padding: 15px;
    border-radius: 12px;
    margin-bottom: 20px;
    position: relative;
    background-color: #fff;
    border-left: 5px solid;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    line-height: 1.5;
}

.android-alert.danger {
    border-color: #e74a3b;
    background-color: rgba(231, 74, 59, 0.1);
}

.android-alert.success {
    border-color: #1cc88a;
    background-color: rgba(28, 200, 138, 0.1);
}

.android-alert.warning {
    border-color: #f6c23e;
    background-color: rgba(246, 194, 62, 0.1);
}

.android-alert.info {
    border-color: #36b9cc;
    background-color: rgba(54, 185, 204, 0.1);
}

.android-alert .btn-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 16px;
    color: #666;
    cursor: pointer;
}

/* Styling untuk pesan blokir */
.android-alert strong {
    font-weight: 600;
    color: #333;
}

.android-alert br {
    margin-bottom: 5px;
    display: block;
    content: "";
}

.android-alert.danger strong {
    color: #e74a3b;
}

/* Animation for particles */
@keyframes float-up {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(-1000px) rotate(720deg);
        opacity: 0;
    }
}
