import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/presensi_model.dart';
import '../services/api_service.dart';

class DashboardProvider with ChangeNotifier {
  UserModel? _karyawan;
  PresensiModel? _presensiHariIni;
  JamKerjaModel? _jamKerja;
  StatistikModel? _statistik;
  bool _isHoliday = false;
  Map<String, dynamic>? _holidayInfo;
  String? _tanggal;
  String? _waktuSekarang;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  UserModel? get karyawan => _karyawan;
  PresensiModel? get presensiHariIni => _presensiHariIni;
  JamKerjaModel? get jamKerja => _jamKerja;
  StatistikModel? get statistik => _statistik;
  bool get isHoliday => _isHoliday;
  Map<String, dynamic>? get holidayInfo => _holidayInfo;
  String? get tanggal => _tanggal;
  String? get waktuSekarang => _waktuSekarang;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Status presensi
  bool get sudahAbsenMasuk => _presensiHariIni?.sudahMasuk ?? false;
  bool get sudahAbsenPulang => _presensiHariIni?.sudahPulang ?? false;
  bool get bisaAbsenMasuk => !sudahAbsenMasuk && !_isHoliday;
  bool get bisaAbsenPulang => sudahAbsenMasuk && !sudahAbsenPulang && !_isHoliday;

  Future<void> loadDashboardData(int userId) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await ApiService.getDashboard(userId);
      
      if (response['status'] == 'success') {
        final data = response['data'];
        
        // Parse karyawan data
        if (data['karyawan'] != null) {
          _karyawan = UserModel.fromJson(data['karyawan']);
        }
        
        // Parse presensi hari ini
        if (data['presensi_hari_ini'] != null) {
          _presensiHariIni = PresensiModel.fromJson(data['presensi_hari_ini']);
        } else {
          _presensiHariIni = null;
        }
        
        // Parse jam kerja
        if (data['jam_kerja'] != null) {
          _jamKerja = JamKerjaModel.fromJson(data['jam_kerja']);
        }
        
        // Parse statistik
        if (data['statistik'] != null) {
          _statistik = StatistikModel.fromJson(data['statistik']);
        }
        
        // Parse holiday info
        _isHoliday = data['is_holiday'] ?? false;
        _holidayInfo = data['holiday_info'];
        
        // Parse tanggal dan waktu
        _tanggal = data['tanggal'];
        _waktuSekarang = data['waktu_sekarang'];
        
        _isLoading = false;
        notifyListeners();
      } else {
        _errorMessage = response['message'] ?? 'Gagal memuat data dashboard';
        _isLoading = false;
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'Terjadi kesalahan: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
    }
  }

  void updatePresensiHariIni(PresensiModel presensi) {
    _presensiHariIni = presensi;
    notifyListeners();
  }

  void clearData() {
    _karyawan = null;
    _presensiHariIni = null;
    _jamKerja = null;
    _statistik = null;
    _isHoliday = false;
    _holidayInfo = null;
    _tanggal = null;
    _waktuSekarang = null;
    _errorMessage = null;
    notifyListeners();
  }

  String getGreeting() {
    final now = DateTime.now();
    final hour = now.hour;
    
    if (hour < 12) {
      return 'Selamat Pagi';
    } else if (hour < 15) {
      return 'Selamat Siang';
    } else if (hour < 18) {
      return 'Selamat Sore';
    } else {
      return 'Selamat Malam';
    }
  }

  String getStatusMessage() {
    if (_isHoliday) {
      return 'Hari ini adalah hari libur';
    }
    
    if (_presensiHariIni == null) {
      return 'Anda belum melakukan absensi hari ini';
    }
    
    if (_presensiHariIni!.sudahMasuk && !_presensiHariIni!.sudahPulang) {
      return 'Anda sudah absen masuk, jangan lupa absen pulang';
    }
    
    if (_presensiHariIni!.sudahMasuk && _presensiHariIni!.sudahPulang) {
      return 'Anda sudah menyelesaikan absensi hari ini';
    }
    
    return 'Silakan lakukan absensi';
  }

  Color getStatusColor() {
    if (_isHoliday) {
      return Colors.orange;
    }
    
    if (_presensiHariIni == null) {
      return Colors.red;
    }
    
    if (_presensiHariIni!.sudahMasuk && _presensiHariIni!.sudahPulang) {
      return Colors.green;
    }
    
    if (_presensiHariIni!.sudahMasuk) {
      return Colors.blue;
    }
    
    return Colors.red;
  }
}
