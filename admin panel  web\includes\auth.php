<?php
/**
 * Auth
 * File ini berisi fungsi-fungsi untuk autentikasi
 */

// Include file konfigurasi
require_once dirname(dirname(__FILE__)) . '/config/database.php';
require_once dirname(dirname(__FILE__)) . '/config/config.php';

/**
 * Fungsi untuk memeriksa apakah device diblokir
 * @return array|false Array dengan informasi blokir jika diblokir, false jika tidak
 */
function isDeviceBlocked($nik, $device_id = null) {
    global $conn;

    $nik = clean($nik);

    // Jika device_id tidak disediakan, gunakan device_id dari session atau cookie
    if (!$device_id) {
        $device_id = $_SESSION['device_id'] ?? ($_COOKIE['device_id'] ?? null);
    }

    // Jika device_id masih null, gunakan user agent sebagai fallback
    if (!$device_id) {
        $device_id = md5($_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);
    }

    // Cek apakah device diblokir
    $query = "SELECT bd.*, u.nama
              FROM blokir_device bd
              LEFT JOIN users u ON bd.user_id = u.id
              WHERE (bd.nik = '$nik' OR bd.device_id = '$device_id') AND bd.status = 'active'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        $data = mysqli_fetch_assoc($result);
        return [
            'nik' => $data['nik'],
            'nama' => $data['nama'],
            'device_id' => $data['device_id'],
            'alasan' => $data['alasan'] ?? 'Tidak ada alasan yang diberikan',
            'tanggal_blokir' => date('d/m/Y H:i:s', strtotime($data['created_at']))
        ];
    }

    return false;
}

/**
 * Fungsi untuk memeriksa apakah NIK sudah login di perangkat lain
 * @return array|false Array dengan informasi perangkat jika sudah login, false jika tidak
 */
function isNikLoggedInOtherDevice($nik, $current_device_id = null) {
    global $conn;

    $nik = clean($nik);

    // Jika tidak ada device_id, gunakan device_id dari cookie atau session
    if (!$current_device_id) {
        $current_device_id = $_SESSION['device_id'] ?? ($_COOKIE['device_id'] ?? null);
        if (!$current_device_id) {
            $current_device_id = md5($_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);
        }
    }

    // Cek apakah NIK ada di database
    $query = "SELECT id FROM users WHERE nik = '$nik' AND role = 'karyawan'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        $user_id = $user['id'];

        // Cek apakah ada sesi yang belum dihapus (cleared = 0)
        $query = "SELECT * FROM user_sessions WHERE user_id = '$user_id' AND cleared = 0";
        $result_sessions = mysqli_query($conn, $query);

        if ($result_sessions && mysqli_num_rows($result_sessions) > 0) {
            // Ambil data sesi terakhir
            $query = "SELECT * FROM user_sessions WHERE user_id = '$user_id' AND cleared = 0 ORDER BY login_time DESC LIMIT 1";
            $result_last_session = mysqli_query($conn, $query);
            $last_session = mysqli_fetch_assoc($result_last_session);

            // Jika device_id berbeda, berarti sudah login di perangkat lain
            if ($current_device_id != $last_session['device_id']) {
                // Ambil informasi perangkat
                $device_info = [
                    'user_agent' => $last_session['user_agent'],
                    'ip_address' => $last_session['ip_address'],
                    'login_time' => $last_session['login_time'],
                    'last_activity' => $last_session['last_activity']
                ];

                return $device_info;
            }
        }
    }

    return false;
}

/**
 * Fungsi untuk login
 */
function login($nik, $password) {
    global $conn;

    $nik = clean($nik);
    $password = clean($password);

    // Cek apakah user ingin diingat
    $remember = isset($_POST['remember']) ? true : false;

    // Dapatkan device_id
    $device_id = $_SESSION['device_id'] ?? ($_COOKIE['device_id'] ?? null);
    if (!$device_id) {
        $device_id = md5($_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);
        // Simpan device_id di cookie selama 1 tahun
        setcookie('device_id', $device_id, time() + 60 * 60 * 24 * 365, '/');
    }

    // Cek role yang dipilih dari URL
    $selected_role = isset($_GET['role']) ? clean($_GET['role']) : null;

    // Query dengan filter role jika dipilih
    if ($selected_role) {
        $query = "SELECT * FROM users WHERE nik = '$nik' AND role = '$selected_role'";
    } else {
        $query = "SELECT * FROM users WHERE nik = '$nik'";
    }

    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);

        // Cek apakah device diblokir (kecuali untuk admin)
        // Pengecekan sudah dilakukan di halaman login, jadi hanya perlu cek untuk admin
        if ($user['role'] != 'admin') {
            // Cek lagi untuk memastikan, tapi hanya jika belum dicek di halaman login
            if (!isset($_SESSION['device_checked'])) {
                $blocked = isDeviceBlocked($nik, $device_id);
                if ($blocked) {
                    // Buat pesan error dengan alasan pemblokiran
                    $pesan = "Device atau NIK Anda diblokir.<br>";
                    $pesan .= "<strong>Alasan:</strong> " . $blocked['alasan'] . "<br>";
                    $pesan .= "<strong>Tanggal Blokir:</strong> " . $blocked['tanggal_blokir'] . "<br>";
                    $pesan .= "Silakan hubungi administrator untuk informasi lebih lanjut.";

                    // Set pesan error
                    setMessage('danger', $pesan);
                    return false;
                }
            }
        }

        // Verifikasi password
        if (password_verify($password, $user['password'])) {
            // Regenerasi ID session untuk keamanan
            session_regenerate_id(true);

            // Buat token sesi baru
            $session_token = md5(uniqid() . time() . $device_id);

            // Cek apakah user sudah login di perangkat lain (hanya untuk karyawan)
            if ($user['role'] == 'karyawan') {
                // Jika admin, selalu izinkan login
                if ($user['role'] != 'admin') {
                    // Cek apakah device_id sama dengan yang tersimpan di cookie
                    $current_device_id = $_SESSION['device_id'] ?? ($_COOKIE['device_id'] ?? null);

                    // Cek apakah ada sesi yang belum dihapus (cleared = 0)
                    $query = "SELECT * FROM user_sessions WHERE user_id = " . $user['id'] . " AND cleared = 0";
                    $result_sessions = mysqli_query($conn, $query);

                    if ($result_sessions && mysqli_num_rows($result_sessions) > 0) {
                        // Ambil data sesi terakhir
                        $query = "SELECT * FROM user_sessions WHERE user_id = " . $user['id'] . " AND cleared = 0 ORDER BY login_time DESC LIMIT 1";
                        $result_last_session = mysqli_query($conn, $query);
                        $last_session = mysqli_fetch_assoc($result_last_session);

                        // Jika device_id berbeda, tolak login
                        if ($current_device_id != $last_session['device_id']) {
                            // Set pesan error
                            setMessage('danger', 'Anda memiliki riwayat login yang belum dihapus. Silakan hubungi administrator untuk menghapus riwayat login Anda.');
                            return false;
                        }
                    }
                }
            }

            // Jika sampai di sini, berarti login diizinkan
            // Simpan token sesi baru
            $query = "UPDATE users SET session_token = '$session_token', last_login = NOW() WHERE id = " . $user['id'];
            mysqli_query($conn, $query);

            // Simpan informasi sesi di tabel user_sessions
            // Hapus dulu sesi aktif yang ada
            $query = "UPDATE user_sessions SET active = 0 WHERE user_id = " . $user['id'] . " AND active = 1";
            mysqli_query($conn, $query);

            // Tambahkan sesi baru
            $query = "INSERT INTO user_sessions (user_id, session_token, device_id, user_agent, ip_address, login_time, active)
                      VALUES (" . $user['id'] . ", '$session_token', '$device_id', '" . mysqli_real_escape_string($conn, $_SERVER['HTTP_USER_AGENT']) . "',
                      '" . mysqli_real_escape_string($conn, $_SERVER['REMOTE_ADDR']) . "', NOW(), 1)";
            mysqli_query($conn, $query);

            // Set session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['nik'] = $user['nik'];
            $_SESSION['nama'] = $user['nama'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['remember_me'] = true; // Selalu set remember_me ke true
            $_SESSION['device_id'] = $device_id;
            $_SESSION['session_token'] = $session_token;

            // Set waktu login
            $_SESSION['login_time'] = time();

            // Redirect berdasarkan role
            if ($user['role'] == 'admin') {
                redirect('admin/index.php');
            } else {
                redirect('karyawan/index.php');
            }

            return true;
        }
    }

    return false;
}

/**
 * Fungsi untuk logout
 */
function logout() {
    global $conn;

    // Simpan role dan device_id sebelum menghapus session
    $role = $_SESSION['role'] ?? '';
    $user_id = $_SESSION['user_id'] ?? 0;
    $session_token = $_SESSION['session_token'] ?? '';
    $device_id = $_SESSION['device_id'] ?? ($_COOKIE['device_id'] ?? null);

    // Nonaktifkan sesi di database, tapi jangan tandai sebagai cleared
    if ($user_id && $session_token) {
        $query = "UPDATE user_sessions SET active = 0 WHERE user_id = '$user_id' AND session_token = '$session_token'";
        mysqli_query($conn, $query);
    }

    // Hapus semua session
    session_unset();
    session_destroy();

    // Pastikan device_id tetap ada untuk identifikasi perangkat
    if ($device_id) {
        setcookie('device_id', $device_id, time() + 60 * 60 * 24 * 365 * 10, '/', $_SERVER['HTTP_HOST'], false, true);
    }

    // Redirect berdasarkan role sebelumnya
    if ($role == 'admin') {
        redirect('admin/login.php');
    } else {
        redirect('index.php');
    }
}

/**
 * Fungsi untuk mendaftarkan admin baru
 */
function registerAdmin($nik, $nama, $password) {
    global $conn;

    $nik = clean($nik);
    $nama = clean($nama);
    $password = password_hash(clean($password), PASSWORD_DEFAULT);

    // Cek apakah NIK sudah terdaftar
    $query = "SELECT * FROM users WHERE nik = '$nik'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        return false;
    }

    // Insert data admin baru
    $query = "INSERT INTO users (nik, nama, password, role) VALUES ('$nik', '$nama', '$password', 'admin')";

    if (mysqli_query($conn, $query)) {
        return true;
    }

    return false;
}

/**
 * Fungsi untuk mendaftarkan karyawan baru
 */
function registerKaryawan($nik, $nama, $bidang_id, $jabatan, $lokasi_id, $password, $foto_profil = null) {
    global $conn;

    $nik = clean($nik);
    $nama = clean($nama);
    $bidang_id = clean($bidang_id);
    $jabatan = clean($jabatan);
    $lokasi_id = clean($lokasi_id);
    $password = password_hash(clean($password), PASSWORD_DEFAULT);
    $foto_profil = $foto_profil ? clean($foto_profil) : null;

    // Cek apakah NIK sudah terdaftar
    $query = "SELECT * FROM users WHERE nik = '$nik'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        return false;
    }

    // Cek apakah bidang_id valid dan ambil nama bidang
    $nama_bidang = null;
    if (!empty($bidang_id)) {
        $query = "SELECT * FROM bidang WHERE id = '$bidang_id'";
        $result = mysqli_query($conn, $query);

        if (mysqli_num_rows($result) == 0) {
            // Bidang tidak ditemukan
            return false;
        } else {
            // Ambil nama bidang
            $bidang_data = mysqli_fetch_assoc($result);
            $nama_bidang = $bidang_data['nama_bidang'];
        }
    }

    // Insert data karyawan baru
    $query = "INSERT INTO users (nik, nama, bidang_id, bidang, jabatan, lokasi_id, password, foto_profil, role)
              VALUES ('$nik', '$nama', '$bidang_id', " . ($nama_bidang ? "'$nama_bidang'" : "NULL") . ", '$jabatan', '$lokasi_id', '$password', '$foto_profil', 'karyawan')";

    if (mysqli_query($conn, $query)) {
        return true;
    } else {
        // Log error untuk debugging
        error_log("Error registerKaryawan: " . mysqli_error($conn) . " Query: " . $query);
        return false;
    }
}

/**
 * Fungsi untuk mengubah password
 */
function changePassword($user_id, $old_password, $new_password) {
    global $conn;

    $user_id = clean($user_id);
    $old_password = clean($old_password);
    $new_password = clean($new_password);

    // Cek password lama
    $query = "SELECT * FROM users WHERE id = '$user_id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);

        // Verifikasi password lama
        if (password_verify($old_password, $user['password'])) {
            // Update password baru
            $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $query = "UPDATE users SET password = '$new_password_hash' WHERE id = '$user_id'";

            if (mysqli_query($conn, $query)) {
                return true;
            }
        }
    }

    return false;
}
