class PresensiModel {
  final int? id;
  final String tanggal;
  final String? jamMasuk;
  final String? jamPulang;
  final String? fotoMasuk;
  final String? fotoPulang;
  final String? lokasiMasuk;
  final String? lokasiPulang;
  final String status;
  final String? keterangan;

  PresensiModel({
    this.id,
    required this.tanggal,
    this.jamMasuk,
    this.jamPulang,
    this.fotoMasuk,
    this.fotoPulang,
    this.lokasiMasuk,
    this.lokasiPulang,
    required this.status,
    this.keterangan,
  });

  factory PresensiModel.fromJson(Map<String, dynamic> json) {
    return PresensiModel(
      id: json['id'],
      tanggal: json['tanggal'] ?? '',
      jamMasuk: json['jam_masuk'],
      jamPulang: json['jam_pulang'],
      fotoMasuk: json['foto_masuk'],
      fotoPulang: json['foto_pulang'],
      lokasiMasuk: json['lokasi_masuk'],
      lokasiPulang: json['lokasi_pulang'],
      status: json['status'] ?? '',
      keterangan: json['keterangan'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tanggal': tanggal,
      'jam_masuk': jamMasuk,
      'jam_pulang': jamPulang,
      'foto_masuk': fotoMasuk,
      'foto_pulang': fotoPulang,
      'lokasi_masuk': lokasiMasuk,
      'lokasi_pulang': lokasiPulang,
      'status': status,
      'keterangan': keterangan,
    };
  }

  bool get sudahMasuk => jamMasuk != null;
  bool get sudahPulang => jamPulang != null;
  bool get isTerlambat => status == 'Terlambat';
  bool get isTepatWaktu => status == 'Tepat Waktu';
}

class JamKerjaModel {
  final int id;
  final int bidangId;
  final String namaJamKerja;
  final String awalJamMasuk;
  final String jamMasuk;
  final String jamPulang;
  final String? akhirJamMasuk;
  final String? akhirJamPulang;

  JamKerjaModel({
    required this.id,
    required this.bidangId,
    required this.namaJamKerja,
    required this.awalJamMasuk,
    required this.jamMasuk,
    required this.jamPulang,
    this.akhirJamMasuk,
    this.akhirJamPulang,
  });

  factory JamKerjaModel.fromJson(Map<String, dynamic> json) {
    return JamKerjaModel(
      id: json['id'] ?? 0,
      bidangId: json['bidang_id'] ?? 0,
      namaJamKerja: json['nama_jam_kerja'] ?? '',
      awalJamMasuk: json['awal_jam_masuk'] ?? '',
      jamMasuk: json['jam_masuk'] ?? '',
      jamPulang: json['jam_pulang'] ?? '',
      akhirJamMasuk: json['akhir_jam_masuk'],
      akhirJamPulang: json['akhir_jam_pulang'],
    );
  }
}

class StatistikModel {
  final int totalHadir;
  final int totalTerlambat;
  final int totalTepatWaktu;
  final int totalAlpha;
  final int hariKerjaEfektif;
  final double persentaseKehadiran;

  StatistikModel({
    required this.totalHadir,
    required this.totalTerlambat,
    required this.totalTepatWaktu,
    this.totalAlpha = 0,
    this.hariKerjaEfektif = 0,
    this.persentaseKehadiran = 0.0,
  });

  factory StatistikModel.fromJson(Map<String, dynamic> json) {
    return StatistikModel(
      totalHadir: json['total_hadir'] ?? 0,
      totalTerlambat: json['total_terlambat'] ?? 0,
      totalTepatWaktu: json['total_tepat_waktu'] ?? 0,
      totalAlpha: json['total_alpha'] ?? 0,
      hariKerjaEfektif: json['hari_kerja_efektif'] ?? 0,
      persentaseKehadiran: (json['persentase_kehadiran'] ?? 0.0).toDouble(),
    );
  }
}
