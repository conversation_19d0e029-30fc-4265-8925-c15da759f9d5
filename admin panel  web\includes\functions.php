<?php
/**
 * Functions
 * File ini berisi fungsi-fungsi umum yang digunakan di aplikasi
 */

// Include file konfigurasi
require_once dirname(dirname(__FILE__)) . '/config/database.php';
require_once dirname(dirname(__FILE__)) . '/config/config.php';

/**
 * Fungsi untuk mendapatkan data karyawan berdasarkan ID
 */
function getKaryawanById($id) {
    global $conn;
    $id = clean($id);

    $query = "SELECT * FROM users WHERE id = '$id' AND role = 'karyawan'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

/**
 * Fungsi untuk mendapatkan semua data karyawan
 */
function getAllKaryawan() {
    global $conn;

    $query = "SELECT u.*, l.nama_lokasi FROM users u
              LEFT JOIN lokasi l ON u.lokasi_id = l.id
              WHERE u.role = 'karyawan'";
    $result = mysqli_query($conn, $query);

    $karyawan = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $karyawan[] = $row;
    }

    return $karyawan;
}

/**
 * Fungsi untuk mendapatkan data lokasi berdasarkan ID
 */
function getLokasiById($id) {
    global $conn;
    $id = clean($id);

    $query = "SELECT * FROM lokasi WHERE id = '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

/**
 * Fungsi untuk mendapatkan semua data lokasi
 */
function getAllLokasi() {
    global $conn;

    $query = "SELECT * FROM lokasi";
    $result = mysqli_query($conn, $query);

    $lokasi = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $lokasi[] = $row;
    }

    return $lokasi;
}

/**
 * Fungsi untuk mendapatkan data jam kerja berdasarkan ID
 */
function getJamKerjaById($id) {
    global $conn;
    $id = clean($id);

    $query = "SELECT * FROM jam_kerja WHERE id = '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

/**
 * Fungsi untuk mendapatkan semua data jam kerja
 */
function getAllJamKerja() {
    global $conn;

    $query = "SELECT * FROM jam_kerja";
    $result = mysqli_query($conn, $query);

    $jam_kerja = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $jam_kerja[] = $row;
    }

    return $jam_kerja;
}

/**
 * Fungsi untuk mendapatkan data denda
 */
function getDenda() {
    global $conn;

    $query = "SELECT * FROM denda LIMIT 1";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

/**
 * Fungsi untuk mendapatkan data hari libur berdasarkan ID
 */
function getHariLiburById($id) {
    global $conn;
    $id = clean($id);

    $query = "SELECT * FROM hari_libur WHERE id = '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

/**
 * Fungsi untuk mendapatkan semua data hari libur
 */
function getAllHariLibur() {
    global $conn;

    $query = "SELECT * FROM hari_libur ORDER BY tanggal";
    $result = mysqli_query($conn, $query);

    $hari_libur = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $hari_libur[] = $row;
    }

    return $hari_libur;
}

/**
 * Fungsi untuk mendapatkan data presensi berdasarkan ID
 */
function getPresensiById($id) {
    global $conn;
    $id = clean($id);

    $query = "SELECT p.*, u.nik, u.nama FROM presensi p
              JOIN users u ON p.user_id = u.id
              WHERE p.id = '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

/**
 * Fungsi untuk mendapatkan semua data presensi
 */
function getAllPresensi() {
    global $conn;

    $query = "SELECT p.*, u.nik, u.nama FROM presensi p
              JOIN users u ON p.user_id = u.id
              ORDER BY p.tanggal DESC, p.jam_masuk DESC";
    $result = mysqli_query($conn, $query);

    $presensi = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $presensi[] = $row;
    }

    return $presensi;
}

/**
 * Fungsi untuk menghitung jarak antara dua koordinat GPS (dalam meter)
 */
function hitungJarak($lat1, $lon1, $lat2, $lon2) {
    $theta = $lon1 - $lon2;
    $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
    $dist = acos($dist);
    $dist = rad2deg($dist);
    $miles = $dist * 60 * 1.1515;
    $meters = $miles * 1609.344;

    return $meters;
}

/**
 * Fungsi untuk mengecek apakah hari ini adalah hari libur
 */
function cekHariLibur($tanggal = null) {
    global $conn;

    // Jika tanggal tidak disediakan, gunakan tanggal hari ini
    if ($tanggal === null) {
        $tanggal = date('Y-m-d');
    }

    $query = "SELECT * FROM hari_libur WHERE tanggal = '$tanggal'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return null;
}

/**
 * Fungsi untuk mendapatkan jam kerja berdasarkan user_id
 */
function getJamKerjaByUserId($user_id) {
    global $conn;

    // Ambil data bidang karyawan
    $query = "SELECT bidang_id FROM users WHERE id = '$user_id'";
    $result = mysqli_query($conn, $query);
    $user = mysqli_fetch_assoc($result);

    if (!$user) {
        return null;
    }

    $bidang_id = $user['bidang_id'];

    // Konversi hari dari bahasa Inggris ke bahasa Indonesia
    $hari_map = [
        'Monday' => 'Senin',
        'Tuesday' => 'Selasa',
        'Wednesday' => 'Rabu',
        'Thursday' => 'Kamis',
        'Friday' => 'Jumat',
        'Saturday' => 'Sabtu',
        'Sunday' => 'Minggu'
    ];

    $hari_en = date('l'); // Mendapatkan nama hari dalam bahasa Inggris
    $hari_id = $hari_map[$hari_en]; // Konversi ke bahasa Indonesia

    // Log untuk debugging
    error_log("Mengambil jam kerja untuk user_id: $user_id, bidang_id: $bidang_id, hari: $hari_id");

    // Cek apakah ada jam kerja khusus untuk bidang pada hari ini
    $query = "SELECT jk.* FROM jam_kerja_bidang jkb
              JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
              WHERE jkb.bidang_id = '$bidang_id' AND jkb.hari = '$hari_id'";

    $result = mysqli_query($conn, $query);

    // Log query untuk debugging
    error_log("Query jam kerja: $query");

    if ($result && mysqli_num_rows($result) > 0) {
        $jam_kerja = mysqli_fetch_assoc($result);
        error_log("Jam kerja ditemukan: " . json_encode($jam_kerja));
        return $jam_kerja;
    } else {
        error_log("Jam kerja tidak ditemukan untuk bidang_id: $bidang_id, hari: $hari_id");
    }

    // Jika tidak ada jam kerja khusus, coba ambil jam kerja default untuk bidang
    $query = "SELECT * FROM jam_kerja WHERE bidang_id = '$bidang_id' LIMIT 1";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $jam_kerja = mysqli_fetch_assoc($result);
        error_log("Menggunakan jam kerja default bidang: " . json_encode($jam_kerja));
        return $jam_kerja;
    }

    // Jika tidak ada jam kerja untuk bidang, ambil jam kerja default sistem
    $query = "SELECT * FROM jam_kerja WHERE is_default = 1 LIMIT 1";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $jam_kerja = mysqli_fetch_assoc($result);
        error_log("Menggunakan jam kerja default sistem: " . json_encode($jam_kerja));
        return $jam_kerja;
    }

    // Jika tidak ada jam kerja default, buat jam kerja default sendiri
    $default_jam_kerja = [
        'awal_jam_masuk' => '07:00:00',
        'jam_masuk' => '08:00:00',
        'akhir_jam_masuk' => '09:00:00',
        'jam_pulang' => '17:00:00',
        'akhir_jam_pulang' => '18:00:00'
    ];

    error_log("Menggunakan jam kerja hardcoded default: " . json_encode($default_jam_kerja));
    return $default_jam_kerja;
}

// Fungsi clean() sudah dideklarasikan di database.php

/**
 * Fungsi untuk memeriksa apakah karyawan memiliki jadwal rapat hari ini
 */
function hasRapatToday($user_id) {
    global $conn;
    $user_id = clean($user_id);
    $today = date('Y-m-d');

    // Periksa apakah karyawan terdaftar sebagai peserta rapat hari ini
    $query = "SELECT r.* FROM rapat r
              JOIN rapat_peserta rp ON r.id = rp.rapat_id
              WHERE rp.user_id = '$user_id'
              AND r.tanggal = '$today'
              AND (rp.status IS NULL OR rp.status != 'hadir')";

    $result = mysqli_query($conn, $query);

    // Jika ada rapat hari ini dan karyawan belum hadir, return true
    if ($result && mysqli_num_rows($result) > 0) {
        return true;
    }

    return false;
}

/**
 * Fungsi untuk mendapatkan daftar rapat hari ini untuk karyawan
 */
function getRapatTodayForUser($user_id) {
    global $conn;
    $user_id = clean($user_id);
    $today = date('Y-m-d');

    // Ambil daftar rapat hari ini untuk karyawan
    $query = "SELECT r.*, rp.status FROM rapat r
              JOIN rapat_peserta rp ON r.id = rp.rapat_id
              WHERE rp.user_id = '$user_id'
              AND r.tanggal = '$today'
              ORDER BY r.waktu_mulai ASC";

    $result = mysqli_query($conn, $query);

    $rapat_list = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $rapat_list[] = $row;
        }
    }

    return $rapat_list;
}
