<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Fungsi untuk menjalankan query dan menampilkan hasilnya
function runQuery($conn, $query, $description) {
    echo "<p><strong>$description:</strong> ";
    if (mysqli_query($conn, $query)) {
        echo "<span class='text-success'>Berhasil</span></p>";
        return true;
    } else {
        echo "<span class='text-danger'>Gagal: " . mysqli_error($conn) . "</span></p>";
        return false;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Perbaikan Struktur Tabel Presensi</h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Proses Perbaikan Tabel Presensi</h6>
        </div>
        <div class="card-body">
            <?php
            // Cek struktur tabel presensi
            $query = "DESCRIBE presensi";
            $result = mysqli_query($conn, $query);
            
            if (!$result) {
                echo "<p class='text-danger'>Tabel presensi tidak ditemukan. Membuat tabel baru...</p>";
                
                // Buat tabel presensi baru
                $query = "CREATE TABLE IF NOT EXISTS presensi (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    tanggal DATE NOT NULL,
                    jam_masuk TIME NOT NULL,
                    foto_masuk VARCHAR(255) DEFAULT NULL,
                    lokasi_masuk VARCHAR(255) NOT NULL,
                    jam_pulang TIME DEFAULT NULL,
                    foto_pulang VARCHAR(255) DEFAULT NULL,
                    lokasi_pulang VARCHAR(255) DEFAULT NULL,
                    status ENUM('Tepat Waktu', 'Terlambat', 'Pulang Awal', 'Lembur') NOT NULL,
                    keterangan TEXT DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )";
                
                if (runQuery($conn, $query, "Pembuatan tabel presensi")) {
                    echo "<p class='text-success'>Tabel presensi berhasil dibuat.</p>";
                } else {
                    echo "<p class='text-danger'>Gagal membuat tabel presensi. Proses dihentikan.</p>";
                    exit;
                }
            } else {
                echo "<p class='text-success'>Tabel presensi sudah ada. Memeriksa struktur...</p>";
                
                // Ambil struktur tabel saat ini
                $columns = [];
                while ($row = mysqli_fetch_assoc($result)) {
                    $columns[$row['Field']] = $row;
                }
                
                // Cek kolom yang diperlukan
                $required_columns = [
                    'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
                    'user_id' => 'INT NOT NULL',
                    'tanggal' => 'DATE NOT NULL',
                    'jam_masuk' => 'TIME NOT NULL',
                    'foto_masuk' => 'VARCHAR(255) DEFAULT NULL',
                    'lokasi_masuk' => 'VARCHAR(255) NOT NULL',
                    'jam_pulang' => 'TIME DEFAULT NULL',
                    'foto_pulang' => 'VARCHAR(255) DEFAULT NULL',
                    'lokasi_pulang' => 'VARCHAR(255) DEFAULT NULL',
                    'status' => "ENUM('Tepat Waktu', 'Terlambat', 'Pulang Awal', 'Lembur') NOT NULL",
                    'keterangan' => 'TEXT DEFAULT NULL',
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
                ];
                
                // Cek dan tambahkan kolom yang hilang
                foreach ($required_columns as $column => $definition) {
                    if (!isset($columns[$column])) {
                        $query = "ALTER TABLE presensi ADD COLUMN $column $definition";
                        runQuery($conn, $query, "Menambahkan kolom $column");
                    }
                }
                
                // Cek kolom yang perlu diubah
                if (isset($columns['status']) && strpos($columns['status']['Type'], "enum('Tepat Waktu','Terlambat','Pulang Awal','Lembur')") === false) {
                    $query = "ALTER TABLE presensi MODIFY COLUMN status ENUM('Tepat Waktu', 'Terlambat', 'Pulang Awal', 'Lembur') NOT NULL";
                    runQuery($conn, $query, "Mengubah tipe kolom status");
                }
                
                // Cek kolom yang tidak diperlukan lagi
                $extra_columns = [
                    'latitude_masuk', 'longitude_masuk', 'accuracy_masuk',
                    'latitude_pulang', 'longitude_pulang', 'accuracy_pulang'
                ];
                
                foreach ($extra_columns as $column) {
                    if (isset($columns[$column])) {
                        // Jika kolom lokasi_masuk kosong, pindahkan data dari kolom lama
                        if ($column == 'latitude_masuk' && isset($columns['lokasi_masuk'])) {
                            $query = "UPDATE presensi SET lokasi_masuk = CONCAT('Lat: ', latitude_masuk, ', Long: ', longitude_masuk, ', Akurasi: ', accuracy_masuk, ' m') WHERE lokasi_masuk = '' OR lokasi_masuk IS NULL";
                            runQuery($conn, $query, "Memindahkan data lokasi masuk");
                        }
                        
                        // Jika kolom lokasi_pulang kosong, pindahkan data dari kolom lama
                        if ($column == 'latitude_pulang' && isset($columns['lokasi_pulang'])) {
                            $query = "UPDATE presensi SET lokasi_pulang = CONCAT('Lat: ', latitude_pulang, ', Long: ', longitude_pulang, ', Akurasi: ', accuracy_pulang, ' m') WHERE lokasi_pulang = '' OR lokasi_pulang IS NULL AND latitude_pulang IS NOT NULL";
                            runQuery($conn, $query, "Memindahkan data lokasi pulang");
                        }
                        
                        // Hapus kolom lama
                        $query = "ALTER TABLE presensi DROP COLUMN $column";
                        runQuery($conn, $query, "Menghapus kolom $column");
                    }
                }
                
                echo "<p class='text-success'>Struktur tabel presensi telah diperbaiki.</p>";
            }
            ?>
            
            <div class="mt-4">
                <a href="index.php" class="btn btn-primary">Kembali ke Dashboard</a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
