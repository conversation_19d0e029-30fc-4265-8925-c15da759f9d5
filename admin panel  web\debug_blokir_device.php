<?php
// Include file konfigurasi
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Cek apakah tabel blokir_device ada
$query_check_table = "SHOW TABLES LIKE 'blokir_device'";
$result_check_table = mysqli_query($conn, $query_check_table);
$table_exists = mysqli_num_rows($result_check_table) > 0;

// Jika tabel tidak ada, buat tabel
if (!$table_exists) {
    $sql = "CREATE TABLE IF NOT EXISTS `blokir_device` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `nik` varchar(20) NOT NULL,
        `device_id` varchar(255) NOT NULL,
        `alasan` text DEFAULT NULL,
        `status` enum('active','inactive') NOT NULL DEFAULT 'active',
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `device_id` (`device_id`),
        KEY `user_id` (`user_id`),
        CONSTRAINT `blokir_device_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    $table_created = mysqli_query($conn, $sql);
}

// Tambahkan data contoh jika tabel kosong
$query_check_data = "SELECT COUNT(*) as count FROM blokir_device";
$result_check_data = mysqli_query($conn, $query_check_data);
$row_check_data = mysqli_fetch_assoc($result_check_data);
$data_exists = $row_check_data['count'] > 0;

if (!$data_exists) {
    // Ambil user_id dari NIK
    $nik = '123456789'; // Ganti dengan NIK yang valid di database
    $query_user = "SELECT id FROM users WHERE nik = '$nik' AND role = 'karyawan'";
    $result_user = mysqli_query($conn, $query_user);
    
    if (mysqli_num_rows($result_user) > 0) {
        $user_id = mysqli_fetch_assoc($result_user)['id'];
        
        // Tambahkan data contoh
        $device_id = md5('test_device_' . time());
        $alasan = 'Device untuk pengujian';
        
        $query_insert = "INSERT INTO blokir_device (user_id, nik, device_id, alasan, status) 
                         VALUES ('$user_id', '$nik', '$device_id', '$alasan', 'active')";
        $data_inserted = mysqli_query($conn, $query_insert);
    }
}

// Ambil device_id dari browser saat ini
$current_device_id = $_COOKIE['device_id'] ?? md5($_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);

// Blokir device saat ini jika diminta
if (isset($_GET['block_current']) && $_GET['block_current'] == 1) {
    // Ambil user_id dari NIK
    $nik = '123456789'; // Ganti dengan NIK yang valid di database
    $query_user = "SELECT id FROM users WHERE nik = '$nik' AND role = 'karyawan'";
    $result_user = mysqli_query($conn, $query_user);
    
    if (mysqli_num_rows($result_user) > 0) {
        $user_id = mysqli_fetch_assoc($result_user)['id'];
        
        // Cek apakah device sudah diblokir
        $query_check = "SELECT id FROM blokir_device WHERE device_id = '$current_device_id'";
        $result_check = mysqli_query($conn, $query_check);
        
        if (mysqli_num_rows($result_check) > 0) {
            // Update data yang sudah ada
            $query = "UPDATE blokir_device SET 
                      user_id = '$user_id', 
                      nik = '$nik', 
                      alasan = 'Device browser saat ini diblokir untuk pengujian', 
                      status = 'active', 
                      updated_at = NOW() 
                      WHERE device_id = '$current_device_id'";
        } else {
            // Insert data baru
            $query = "INSERT INTO blokir_device (user_id, nik, device_id, alasan, status) 
                      VALUES ('$user_id', '$nik', '$current_device_id', 'Device browser saat ini diblokir untuk pengujian', 'active')";
        }
        
        $block_result = mysqli_query($conn, $query);
    }
    
    // Redirect ke halaman ini tanpa parameter
    header("Location: debug_blokir_device.php");
    exit;
}

// Izinkan device saat ini jika diminta
if (isset($_GET['allow_current']) && $_GET['allow_current'] == 1) {
    $query = "UPDATE blokir_device SET status = 'inactive', updated_at = NOW() WHERE device_id = '$current_device_id'";
    $allow_result = mysqli_query($conn, $query);
    
    // Redirect ke halaman ini tanpa parameter
    header("Location: debug_blokir_device.php");
    exit;
}

// Ambil semua data blokir device
$query = "SELECT bd.*, u.nama 
          FROM blokir_device bd
          LEFT JOIN users u ON bd.user_id = u.id
          ORDER BY bd.updated_at DESC";
$result = mysqli_query($conn, $query);
$blokir_device = [];
while ($row = mysqli_fetch_assoc($result)) {
    $blokir_device[] = $row;
}

// Cek apakah device saat ini diblokir
$query_current = "SELECT * FROM blokir_device WHERE device_id = '$current_device_id' AND status = 'active'";
$result_current = mysqli_query($conn, $query_current);
$current_blocked = mysqli_num_rows($result_current) > 0;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Blokir Device</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Debug Blokir Device</h1>
        
        <div class="alert <?php echo $table_exists ? 'alert-success' : 'alert-danger'; ?>">
            <strong>Status Tabel:</strong> <?php echo $table_exists ? 'Tabel blokir_device sudah ada' : 'Tabel blokir_device belum ada'; ?>
            <?php if (isset($table_created)): ?>
                <br><strong>Hasil Pembuatan Tabel:</strong> <?php echo $table_created ? 'Berhasil' : 'Gagal: ' . mysqli_error($conn); ?>
            <?php endif; ?>
        </div>
        
        <div class="alert <?php echo $data_exists ? 'alert-success' : 'alert-warning'; ?>">
            <strong>Status Data:</strong> <?php echo $data_exists ? 'Data sudah ada di tabel' : 'Tabel masih kosong'; ?>
            <?php if (isset($data_inserted)): ?>
                <br><strong>Hasil Penambahan Data:</strong> <?php echo $data_inserted ? 'Berhasil' : 'Gagal: ' . mysqli_error($conn); ?>
            <?php endif; ?>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Device Browser Saat Ini</h5>
            </div>
            <div class="card-body">
                <p><strong>Device ID:</strong> <?php echo $current_device_id; ?></p>
                <p><strong>Status:</strong> 
                    <?php if ($current_blocked): ?>
                        <span class="badge bg-danger">Diblokir</span>
                    <?php else: ?>
                        <span class="badge bg-success">Diizinkan</span>
                    <?php endif; ?>
                </p>
                
                <?php if ($current_blocked): ?>
                    <a href="debug_blokir_device.php?allow_current=1" class="btn btn-success">
                        <i class="fas fa-check"></i> Izinkan Device Ini
                    </a>
                <?php else: ?>
                    <a href="debug_blokir_device.php?block_current=1" class="btn btn-danger">
                        <i class="fas fa-ban"></i> Blokir Device Ini
                    </a>
                <?php endif; ?>
                
                <a href="index.php" class="btn btn-primary ms-2">
                    <i class="fas fa-sign-in-alt"></i> Coba Login
                </a>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Data Blokir Device</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>NIK</th>
                                <th>Nama</th>
                                <th>Device ID</th>
                                <th>Alasan</th>
                                <th>Status</th>
                                <th>Tanggal Update</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($blokir_device as $bd): ?>
                                <tr <?php echo $bd['device_id'] == $current_device_id ? 'class="table-warning"' : ''; ?>>
                                    <td><?php echo $bd['id']; ?></td>
                                    <td><?php echo $bd['nik']; ?></td>
                                    <td><?php echo $bd['nama']; ?></td>
                                    <td><?php echo $bd['device_id']; ?></td>
                                    <td><?php echo $bd['alasan'] ?? '-'; ?></td>
                                    <td>
                                        <?php if ($bd['status'] == 'active'): ?>
                                            <span class="badge bg-danger">Diblokir</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">Diizinkan</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i:s', strtotime($bd['updated_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
