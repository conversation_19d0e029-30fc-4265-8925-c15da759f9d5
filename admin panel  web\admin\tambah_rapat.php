<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses tambah rapat
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $judul = clean($_POST['judul']);
    $tanggal = clean($_POST['tanggal']);
    $waktu_mulai = clean($_POST['waktu_mulai']);
    $waktu_selesai = clean($_POST['waktu_selesai']);
    $lokasi = clean($_POST['lokasi']);
    $deskripsi = clean($_POST['deskripsi']);
    $penanggung_jawab_id = clean($_POST['penanggung_jawab_id']);
    $peserta = isset($_POST['peserta']) ? $_POST['peserta'] : [];

    // Generate barcode value
    $barcode_value = 'RAPAT_' . date('YmdHis') . '_' . substr(md5(uniqid()), 0, 8);

    // Validasi input
    if (empty($judul) || empty($tanggal) || empty($waktu_mulai) || empty($waktu_selesai) || empty($lokasi)) {
        setMessage('danger', 'Semua field wajib diisi kecuali deskripsi!');
        redirect('admin/tambah_rapat.php');
    }

    // Validasi waktu
    if ($waktu_mulai >= $waktu_selesai) {
        setMessage('danger', 'Waktu mulai harus lebih awal dari waktu selesai!');
        redirect('admin/tambah_rapat.php');
    }

    // Validasi peserta
    if (empty($peserta)) {
        setMessage('danger', 'Pilih minimal satu peserta rapat!');
        redirect('admin/tambah_rapat.php');
    }

    // Simpan data rapat
    $created_by = $_SESSION['user_id'];
    $query = "INSERT INTO rapat (judul, tanggal, waktu_mulai, waktu_selesai, lokasi, deskripsi, barcode_value, created_by, penanggung_jawab_id)
              VALUES ('$judul', '$tanggal', '$waktu_mulai', '$waktu_selesai', '$lokasi', '$deskripsi', '$barcode_value', '$created_by', '$penanggung_jawab_id')";

    if (mysqli_query($conn, $query)) {
        $rapat_id = mysqli_insert_id($conn);

        // Simpan data peserta
        $values = [];
        foreach ($peserta as $user_id) {
            $user_id = clean($user_id);
            $values[] = "($rapat_id, $user_id)";
        }

        if (!empty($values)) {
            $query = "INSERT INTO rapat_peserta (rapat_id, user_id) VALUES " . implode(', ', $values);

            if (mysqli_query($conn, $query)) {
                setMessage('success', 'Rapat berhasil ditambahkan!');
                redirect('admin/rapat.php');
            } else {
                setMessage('danger', 'Gagal menambahkan peserta rapat: ' . mysqli_error($conn));
                redirect('admin/tambah_rapat.php');
            }
        } else {
            setMessage('success', 'Rapat berhasil ditambahkan tanpa peserta!');
            redirect('admin/rapat.php');
        }
    } else {
        setMessage('danger', 'Gagal menambahkan rapat: ' . mysqli_error($conn));
        redirect('admin/tambah_rapat.php');
    }
}

// Ambil data karyawan untuk dropdown
$query = "SELECT id, nik, nama, bidang FROM users WHERE role = 'karyawan' ORDER BY nama ASC";
$result = mysqli_query($conn, $query);

$karyawan_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $karyawan_list[] = $row;
    }
}

// Ambil data semua pengguna (admin dan karyawan) untuk dropdown penanggung jawab
$query = "SELECT id, nik, nama, bidang, role FROM users ORDER BY role ASC, nama ASC";
$result = mysqli_query($conn, $query);

$users_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $users_list[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Tambah Rapat</h1>
        <a href="rapat.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if (isset($_SESSION['message'])): ?>
    <div class="alert alert-<?php echo $_SESSION['message']['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['message']['text']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php unset($_SESSION['message']); endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Form Tambah Rapat</h6>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="judul" class="form-label">Judul Rapat <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="judul" name="judul" required>
                    </div>
                    <div class="col-md-6">
                        <label for="tanggal" class="form-label">Tanggal <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" min="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="waktu_mulai" class="form-label">Waktu Mulai <span class="text-danger">*</span></label>
                        <input type="time" class="form-control" id="waktu_mulai" name="waktu_mulai" required>
                    </div>
                    <div class="col-md-6">
                        <label for="waktu_selesai" class="form-label">Waktu Selesai <span class="text-danger">*</span></label>
                        <input type="time" class="form-control" id="waktu_selesai" name="waktu_selesai" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="lokasi" class="form-label">Lokasi <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="lokasi" name="lokasi" required>
                </div>

                <div class="mb-3">
                    <label for="deskripsi" class="form-label">Deskripsi</label>
                    <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"></textarea>
                </div>

                <div class="mb-3">
                    <label for="penanggung_jawab_id" class="form-label">Penanggung Jawab <span class="text-danger">*</span></label>
                    <select class="form-select" id="penanggung_jawab_id" name="penanggung_jawab_id" required>
                        <option value="">-- Pilih Penanggung Jawab --</option>
                        <?php foreach ($users_list as $user): ?>
                            <option value="<?php echo $user['id']; ?>">
                                <?php echo $user['nama']; ?> (<?php echo $user['nik']; ?>)
                                <?php echo $user['role'] == 'admin' ? '- Admin' : ''; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="form-text">Pilih orang yang bertanggung jawab untuk rapat ini.</div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Peserta Rapat <span class="text-danger">*</span></label>
                    <div class="card">
                        <div class="card-header">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchPeserta" placeholder="Cari karyawan...">
                                <button class="btn btn-outline-secondary" type="button" id="selectAll">Pilih Semua</button>
                                <button class="btn btn-outline-secondary" type="button" id="deselectAll">Batal Pilih</button>
                            </div>
                        </div>
                        <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                            <?php if (empty($karyawan_list)): ?>
                                <p class="text-center">Tidak ada data karyawan</p>
                            <?php else: ?>
                                <div class="row">
                                    <?php foreach ($karyawan_list as $karyawan): ?>
                                        <div class="col-md-4 mb-2 peserta-item">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="peserta[]" value="<?php echo $karyawan['id']; ?>" id="peserta_<?php echo $karyawan['id']; ?>">
                                                <label class="form-check-label" for="peserta_<?php echo $karyawan['id']; ?>">
                                                    <?php echo $karyawan['nama']; ?> (<?php echo $karyawan['nik']; ?>)
                                                    <?php if (!empty($karyawan['bidang'])): ?>
                                                        <br><small class="text-muted"><?php echo $karyawan['bidang']; ?></small>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                    <a href="rapat.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pencarian peserta
    const searchInput = document.getElementById('searchPeserta');
    const pesertaItems = document.querySelectorAll('.peserta-item');

    searchInput.addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();

        pesertaItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(searchValue)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Pilih semua peserta
    document.getElementById('selectAll').addEventListener('click', function() {
        document.querySelectorAll('input[name="peserta[]"]').forEach(checkbox => {
            checkbox.checked = true;
        });
    });

    // Batal pilih semua peserta
    document.getElementById('deselectAll').addEventListener('click', function() {
        document.querySelectorAll('input[name="peserta[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });
    });
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
