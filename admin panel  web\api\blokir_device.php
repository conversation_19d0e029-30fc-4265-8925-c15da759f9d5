<?php
/**
 * API untuk menerima data blokir device
 * Endpoint ini menerima data blokir device dari aplikasi Android dan menyimpannya ke database
 *
 * Format data:
 * {
 *     "api_key": "absensiku_api_key_2023",
 *     "nik": "123456789",
 *     "device_id": "abc123xyz456",
 *     "alasan": "Device tidak dikenali"
 * }
 *
 * Atau format batch:
 * {
 *     "api_key": "absensiku_api_key_2023",
 *     "data": [
 *         {
 *             "nik": "123456789",
 *             "device_id": "abc123xyz456",
 *             "alasan": "Device tidak dikenali"
 *         },
 *         {
 *             "nik": "987654321",
 *             "device_id": "def456uvw789",
 *             "alasan": "Device mencurigakan"
 *         }
 *     ]
 * }
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header untuk JSON dan CORS
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Fungsi untuk validasi API key
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Fungsi untuk mendapatkan user_id berdasarkan NIK
function getUserIdByNik($nik) {
    global $conn;
    $nik = mysqli_real_escape_string($conn, $nik);
    $query = "SELECT id FROM users WHERE nik = '$nik' AND role = 'karyawan'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result)['id'];
    }
    return false;
}

// Fungsi untuk menyimpan data blokir device
function saveBlokirDevice($data) {
    global $conn;

    // Validasi data
    if (!isset($data['nik']) || !isset($data['device_id'])) {
        return [
            'status' => 'error',
            'message' => 'Data tidak lengkap'
        ];
    }

    // Dapatkan user_id berdasarkan NIK
    $user_id = getUserIdByNik($data['nik']);
    if (!$user_id) {
        return [
            'status' => 'error',
            'message' => 'NIK tidak valid'
        ];
    }

    // Escape data
    $user_id = mysqli_real_escape_string($conn, $user_id);
    $nik = mysqli_real_escape_string($conn, $data['nik']);
    $device_id = mysqli_real_escape_string($conn, $data['device_id']);
    $alasan = isset($data['alasan']) ? mysqli_real_escape_string($conn, $data['alasan']) : '';

    // Cek apakah device_id sudah ada
    $query_check = "SELECT id FROM blokir_device WHERE device_id = '$device_id'";
    $result_check = mysqli_query($conn, $query_check);

    $is_update = false;
    $blokir_id = 0;

    if (mysqli_num_rows($result_check) > 0) {
        // Update data yang sudah ada
        $is_update = true;
        $blokir_id = mysqli_fetch_assoc($result_check)['id'];
        $query = "UPDATE blokir_device SET
                  user_id = '$user_id',
                  nik = '$nik',
                  alasan = '$alasan',
                  status = 'active',
                  updated_at = NOW()
                  WHERE device_id = '$device_id'";
    } else {
        // Insert data baru
        $query = "INSERT INTO blokir_device (user_id, nik, device_id, alasan, status)
                  VALUES ('$user_id', '$nik', '$device_id', '$alasan', 'active')";
    }

    // Eksekusi query
    if (mysqli_query($conn, $query)) {
        // Jika insert baru, ambil ID yang baru dibuat
        if (!$is_update) {
            $blokir_id = mysqli_insert_id($conn);
        }

        // Ambil data lengkap untuk respons
        $query_data = "SELECT bd.*, u.nama
                      FROM blokir_device bd
                      LEFT JOIN users u ON bd.user_id = u.id
                      WHERE bd.id = '$blokir_id'";
        $result_data = mysqli_query($conn, $query_data);
        $blokir_data = mysqli_fetch_assoc($result_data);

        return [
            'status' => 'success',
            'message' => 'Data blokir device berhasil disimpan',
            'data' => [
                'id' => $blokir_data['id'],
                'nik' => $blokir_data['nik'],
                'nama' => $blokir_data['nama'],
                'device_id' => $blokir_data['device_id'],
                'alasan' => $blokir_data['alasan'],
                'status' => $blokir_data['status'],
                'tanggal_blokir' => date('d/m/Y H:i:s', strtotime($blokir_data['created_at'])),
                'tanggal_update' => date('d/m/Y H:i:s', strtotime($blokir_data['updated_at'])),
                'can_unblock' => strpos(strtolower($blokir_data['alasan']), 'sementara') !== false
            ]
        ];
    } else {
        return [
            'status' => 'error',
            'message' => 'Gagal menyimpan data blokir device: ' . mysqli_error($conn)
        ];
    }
}

// Ambil data dari request
$data = json_decode(file_get_contents('php://input'), true);

// Jika data tidak valid, coba ambil dari $_POST
if (!$data) {
    $data = $_POST;
}

// Validasi API key
if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'API key tidak valid'
    ]);
    exit;
}

// Cek apakah data adalah batch atau single
if (isset($data['data']) && is_array($data['data'])) {
    // Batch data
    $results = [];
    $success_count = 0;
    $error_count = 0;

    foreach ($data['data'] as $index => $item) {
        $result = saveBlokirDevice($item);
        $results[] = [
            'index' => $index,
            'status' => $result['status'],
            'message' => $result['message']
        ];

        if ($result['status'] === 'success') {
            $success_count++;
        } else {
            $error_count++;
        }
    }

    echo json_encode([
        'status' => $error_count === 0 ? 'success' : ($success_count === 0 ? 'error' : 'partial'),
        'message' => "Berhasil menyimpan $success_count data, gagal menyimpan $error_count data",
        'results' => $results
    ]);
} else {
    // Single data
    $result = saveBlokirDevice($data);
    echo json_encode($result);
}
?>
