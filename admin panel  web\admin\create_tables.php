<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Fungsi untuk menjalankan SQL dari file
function executeSQLFile($conn, $file) {
    $sql = file_get_contents($file);
    
    // Eksekusi SQL
    if (mysqli_multi_query($conn, $sql)) {
        $result = "SQL dari file $file berhasil dijalankan";
        
        // Bersihkan hasil query
        while (mysqli_next_result($conn)) {
            if (!mysqli_more_results($conn)) {
                break;
            }
        }
    } else {
        $result = "Error menjalankan SQL dari file $file: " . mysqli_error($conn);
    }
    
    return $result;
}

// Jalankan SQL jika tombol ditekan
if (isset($_POST['create_absensi_offline'])) {
    $result_absensi_offline = executeSQLFile($conn, '../sql/absensi_offline.sql');
    $_SESSION['result'] = $result_absensi_offline;
    header("Location: create_tables.php");
    exit;
}

if (isset($_POST['create_blokir_device'])) {
    $result_blokir_device = executeSQLFile($conn, '../sql/blokir_device.sql');
    $_SESSION['result'] = $result_blokir_device;
    header("Location: create_tables.php");
    exit;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Buat Tabel Database</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Buat Tabel Database</li>
    </ol>
    
    <?php if (isset($_SESSION['result'])): ?>
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <?php 
            echo $_SESSION['result'];
            unset($_SESSION['result']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-database me-1"></i>
            Buat Tabel Database
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Perhatian!</strong> Halaman ini digunakan untuk membuat tabel-tabel baru yang diperlukan untuk fitur tambahan. Pastikan Anda memiliki backup database sebelum menjalankan perintah ini.
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-wifi-slash me-1"></i>
                            Tabel Absensi Offline
                        </div>
                        <div class="card-body">
                            <p>Tabel ini digunakan untuk menyimpan data absensi offline yang diterima dari API.</p>
                            <form method="post" action="">
                                <button type="submit" name="create_absensi_offline" class="btn btn-primary" onclick="return confirm('Apakah Anda yakin ingin membuat tabel absensi_offline?')">
                                    <i class="fas fa-plus me-1"></i> Buat Tabel Absensi Offline
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-ban me-1"></i>
                            Tabel Blokir Device
                        </div>
                        <div class="card-body">
                            <p>Tabel ini digunakan untuk menyimpan data device yang diblokir.</p>
                            <form method="post" action="">
                                <button type="submit" name="create_blokir_device" class="btn btn-primary" onclick="return confirm('Apakah Anda yakin ingin membuat tabel blokir_device?')">
                                    <i class="fas fa-plus me-1"></i> Buat Tabel Blokir Device
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
