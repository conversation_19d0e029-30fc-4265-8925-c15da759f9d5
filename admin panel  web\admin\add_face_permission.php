<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek apakah kolom allow_face sudah ada di tabel users
$query = "SHOW COLUMNS FROM users LIKE 'allow_face'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    // Tambahkan kolom allow_face ke tabel users
    $query = "ALTER TABLE users ADD COLUMN allow_face TINYINT(1) NOT NULL DEFAULT 0";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Kolom allow_face berhasil ditambahkan ke tabel users!');
    } else {
        setMessage('danger', 'Gagal menambahkan kolom allow_face: ' . mysqli_error($conn));
    }
} else {
    setMessage('info', 'Kolom allow_face sudah ada di tabel users!');
}

// Redirect ke halaman karyawan
redirect('admin/karyawan.php');
?>
