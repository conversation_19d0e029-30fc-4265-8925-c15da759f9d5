<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Pesan untuk ditampilkan
$message = '';

// Cek apakah tabel aktivitas_karyawan sudah ada
$query = "SHOW TABLES LIKE 'aktivitas_karyawan'";
$result = mysqli_query($conn, $query);
if (mysqli_num_rows($result) == 0) {
    $message .= '<div class="alert alert-danger">Tabel aktivitas_karyawan tidak ditemukan!</div>';
} else {
    $message .= '<div class="alert alert-success">Tabel aktivitas_karyawan ditemukan.</div>';
    
    // Cek struktur tabel
    $query = "DESCRIBE aktivitas_karyawan";
    $result = mysqli_query($conn, $query);
    
    $message .= '<div class="alert alert-info">Struktur tabel aktivitas_karyawan:</div>';
    $message .= '<table class="table table-bordered">';
    $message .= '<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>';
    $message .= '<tbody>';
    
    while ($row = mysqli_fetch_assoc($result)) {
        $message .= '<tr>';
        $message .= '<td>' . $row['Field'] . '</td>';
        $message .= '<td>' . $row['Type'] . '</td>';
        $message .= '<td>' . $row['Null'] . '</td>';
        $message .= '<td>' . $row['Key'] . '</td>';
        $message .= '<td>' . $row['Default'] . '</td>';
        $message .= '<td>' . $row['Extra'] . '</td>';
        $message .= '</tr>';
    }
    
    $message .= '</tbody></table>';
    
    // Cek jumlah data
    $query = "SELECT COUNT(*) as total FROM aktivitas_karyawan";
    $result = mysqli_query($conn, $query);
    $row = mysqli_fetch_assoc($result);
    
    if ($row['total'] == 0) {
        $message .= '<div class="alert alert-warning">Tidak ada data aktivitas karyawan!</div>';
    } else {
        $message .= '<div class="alert alert-success">Jumlah data aktivitas karyawan: ' . $row['total'] . '</div>';
        
        // Ambil contoh data
        $query = "SELECT * FROM aktivitas_karyawan LIMIT 5";
        $result = mysqli_query($conn, $query);
        
        $message .= '<div class="alert alert-info">Contoh data aktivitas karyawan:</div>';
        $message .= '<table class="table table-bordered">';
        $message .= '<thead><tr><th>ID</th><th>User ID</th><th>Tanggal</th><th>Waktu</th><th>Longitude</th><th>Latitude</th><th>Status</th><th>Jarak</th></tr></thead>';
        $message .= '<tbody>';
        
        while ($row = mysqli_fetch_assoc($result)) {
            $message .= '<tr>';
            $message .= '<td>' . $row['id'] . '</td>';
            $message .= '<td>' . $row['user_id'] . '</td>';
            $message .= '<td>' . $row['tanggal'] . '</td>';
            $message .= '<td>' . $row['waktu'] . '</td>';
            $message .= '<td>' . $row['longitude'] . '</td>';
            $message .= '<td>' . $row['latitude'] . '</td>';
            $message .= '<td>' . $row['status'] . '</td>';
            $message .= '<td>' . $row['jarak_dari_kantor'] . '</td>';
            $message .= '</tr>';
        }
        
        $message .= '</tbody></table>';
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Cek Tabel Aktivitas Karyawan</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="aktivitas_karyawan.php">Aktivitas Karyawan</a></li>
        <li class="breadcrumb-item active">Cek Tabel</li>
    </ol>
    
    <?php echo $message; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-cogs me-1"></i>
            Tindakan
        </div>
        <div class="card-body">
            <a href="generate_sample_aktivitas.php" class="btn btn-primary">
                <i class="fas fa-database me-1"></i> Generate Data Sampel
            </a>
            <a href="aktivitas_karyawan.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Kembali
            </a>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
