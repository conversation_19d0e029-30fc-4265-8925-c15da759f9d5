<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID rapat tidak valid!');
    redirect('admin/rapat.php');
}

$id = clean($_GET['id']);

// Ambil data rapat
$query = "SELECT r.*,
          u1.nama as created_by_name,
          u2.nama as penanggung_jawab_name,
          u2.nik as penanggung_jawab_nik
          FROM rapat r
          JOIN users u1 ON r.created_by = u1.id
          LEFT JOIN users u2 ON r.penanggung_jawab_id = u2.id
          WHERE r.id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Data rapat tidak ditemukan!');
    redirect('admin/rapat.php');
}

$rapat = mysqli_fetch_assoc($result);

// Ambil data peserta rapat
$query = "SELECT rp.*, u.nik, u.nama, u.bidang
          FROM rapat_peserta rp
          JOIN users u ON rp.user_id = u.id
          WHERE rp.rapat_id = '$id'
          ORDER BY u.nama ASC";
$result = mysqli_query($conn, $query);

$peserta_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $peserta_list[] = $row;
    }
}

// Format tanggal
$tanggal_rapat = date('d F Y', strtotime($rapat['tanggal']));
$waktu_rapat = date('H:i', strtotime($rapat['waktu_mulai'])) . ' - ' . date('H:i', strtotime($rapat['waktu_selesai']));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cetak Kehadiran Rapat - <?php echo $rapat['judul']; ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12pt;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
        }
        .header h1 {
            margin: 0;
            font-size: 16pt;
            font-weight: bold;
            text-transform: uppercase;
        }
        .header h2 {
            margin: 10px 0;
            font-size: 14pt;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0;
            font-size: 12pt;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        table, th, td {
            border: 1px solid #000;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            margin-top: 50px;
            text-align: right;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        .signature {
            margin-top: 80px;
            border-bottom: 1px solid #000;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }
        @media print {
            body {
                padding: 0.5cm;
                margin: 0;
            }
            button {
                display: none;
            }
            @page {
                size: A4;
                margin: 1cm;
            }
            .header, table, .footer {
                page-break-inside: avoid;
            }
            th {
                background-color: #f2f2f2 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Perumda Air Minum Trunojoyo Sampang</h1>
        <h2><?php echo $rapat['judul']; ?></h2>
        <p><?php echo $rapat['lokasi']; ?></p>
        <p><?php echo $tanggal_rapat; ?> (<?php echo $waktu_rapat; ?>)</p>
    </div>

    <table>
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="20%">NIK</th>
                <th width="45%">Nama</th>
                <th width="30%">Status Absen</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($peserta_list)): ?>
                <tr>
                    <td colspan="4" style="text-align: center;">Tidak ada data peserta</td>
                </tr>
            <?php else: ?>
                <?php $no = 1; foreach ($peserta_list as $peserta): ?>
                    <tr>
                        <td><?php echo $no++; ?></td>
                        <td><?php echo $peserta['nik']; ?></td>
                        <td><?php echo $peserta['nama']; ?></td>
                        <td>
                            <?php if ($peserta['status'] == 'hadir'): ?>
                                Hadir
                            <?php elseif ($peserta['status'] == 'tidak hadir'): ?>
                                Tidak Hadir
                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>

    <div class="footer">
        <p>Yang bertanggung jawab,</p>
        <div class="signature">
            <?php if (!empty($rapat['penanggung_jawab_name'])): ?>
                <p><?php echo $rapat['penanggung_jawab_name']; ?></p>
            <?php else: ?>
                <p><?php echo $rapat['created_by_name']; ?></p>
            <?php endif; ?>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; background-color: #4e73df; color: white; border: none; border-radius: 5px; cursor: pointer;">
            <i class="fas fa-print"></i> Cetak
        </button>
        <button onclick="window.location.href='detail_rapat.php?id=<?php echo $id; ?>'" style="padding: 10px 20px; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            <i class="fas fa-arrow-left"></i> Kembali
        </button>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            // Automatically open print dialog when page loads
            window.print();
        }
    </script>
</body>
</html>
