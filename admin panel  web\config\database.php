<?php
/**
 * Konfigurasi Database
 * File ini berisi konfigurasi untuk koneksi ke database MySQL
 */

// Konfigurasi database
define('DB_HOST', 'localhost');     // Host database
define('DB_USER', 'u929005145_absensiku');          // Username database
define('DB_PASS', 'Sampang123!');              // Password database
define('DB_NAME', 'u929005145_absensiku');     // Nama database

// Membuat koneksi ke database
$conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);

// Cek koneksi
if (!$conn) {
    die("Koneksi database gagal: " . mysqli_connect_error());
}

// Set karakter encoding
mysqli_set_charset($conn, "utf8");

// Fungsi untuk membersihkan input dari SQL Injection dan karakter berbahaya
function clean($data) {
    global $conn;

    // Jika data adalah array, bersihkan setiap elemen
    if (is_array($data)) {
        $clean_data = [];
        foreach ($data as $key => $value) {
            $clean_data[$key] = clean($value);
        }
        return $clean_data;
    }

    // Jika data adalah numerik, kembalikan apa adanya
    if (is_numeric($data)) {
        return $data;
    }

    // Bersihkan string
    if ($conn) {
        $data = mysqli_real_escape_string($conn, $data);
    }
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);

    return $data;
}
