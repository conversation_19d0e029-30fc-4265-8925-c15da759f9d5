<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek apakah tabel barcode_config sudah ada
$query = "SHOW TABLES LIKE 'barcode_config'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    // Buat tabel barcode_config
    $query = "CREATE TABLE barcode_config (
        id INT(11) NOT NULL AUTO_INCREMENT,
        lokasi_id INT(11) NOT NULL,
        barcode_value VARCHAR(255) NOT NULL,
        radius INT(11) NOT NULL DEFAULT 100,
        is_active TINYINT(1) NOT NULL DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMAR<PERSON> KEY (id),
        FOREIGN KEY (lokasi_id) REFERENCES lokasi(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Tabel barcode_config berhasil dibuat!');
    } else {
        setMessage('danger', 'Gagal membuat tabel barcode_config: ' . mysqli_error($conn));
    }
} else {
    setMessage('info', 'Tabel barcode_config sudah ada!');
}

// Redirect ke halaman barcode_config
redirect('admin/barcode_config.php');
?>
