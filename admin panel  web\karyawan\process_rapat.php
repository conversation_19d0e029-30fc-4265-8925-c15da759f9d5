<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('karyawan');

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Ambil data dari request
$data = json_decode(file_get_contents('php://input'), true);

// Validasi data
if (!isset($data['barcode_value']) || empty($data['barcode_value'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Barcode tidak valid!'
    ]);
    exit;
}

// Ambil data barcode
$barcode_value = clean($data['barcode_value']);
$latitude = isset($data['latitude']) ? clean($data['latitude']) : null;
$longitude = isset($data['longitude']) ? clean($data['longitude']) : null;
$accuracy = isset($data['accuracy']) ? clean($data['accuracy']) : null;

// Cek apakah barcode valid
$query = "SELECT r.* FROM rapat r WHERE r.barcode_value = '$barcode_value'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Barcode tidak valid atau rapat tidak ditemukan!'
    ]);
    exit;
}

$rapat = mysqli_fetch_assoc($result);
$rapat_id = $rapat['id'];

// Cek apakah karyawan terdaftar sebagai peserta rapat
$query = "SELECT * FROM rapat_peserta WHERE rapat_id = '$rapat_id' AND user_id = '$user_id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Anda tidak terdaftar sebagai peserta rapat ini!'
    ]);
    exit;
}

$peserta = mysqli_fetch_assoc($result);

// Cek apakah karyawan sudah absen
if ($peserta['status'] == 'hadir') {
    echo json_encode([
        'status' => 'error',
        'message' => 'Anda sudah melakukan absensi untuk rapat ini!'
    ]);
    exit;
}

// Cek apakah rapat sudah berlalu
$today = date('Y-m-d');
$now = date('H:i:s');

if ($rapat['tanggal'] < $today) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Rapat ini sudah berlalu!'
    ]);
    exit;
}

// Cek apakah rapat hari ini tapi sudah selesai
if ($rapat['tanggal'] == $today && $now > $rapat['waktu_selesai']) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Rapat ini sudah selesai!'
    ]);
    exit;
}

// Update status kehadiran
$waktu_hadir = date('Y-m-d H:i:s');
$query = "UPDATE rapat_peserta SET status = 'hadir', waktu_hadir = '$waktu_hadir' WHERE rapat_id = '$rapat_id' AND user_id = '$user_id'";

if (mysqli_query($conn, $query)) {
    // Format waktu untuk ditampilkan
    $tanggal_rapat = date('d/m/Y', strtotime($rapat['tanggal']));
    $waktu_rapat = date('H:i', strtotime($rapat['waktu_mulai'])) . ' - ' . date('H:i', strtotime($rapat['waktu_selesai']));
    
    echo json_encode([
        'status' => 'success',
        'message' => "Absensi rapat berhasil!<br><br>Judul: {$rapat['judul']}<br>Tanggal: {$tanggal_rapat}<br>Waktu: {$waktu_rapat}<br>Lokasi: {$rapat['lokasi']}"
    ]);
} else {
    echo json_encode([
        'status' => 'error',
        'message' => 'Gagal melakukan absensi: ' . mysqli_error($conn)
    ]);
}
?>
