/* Mobile Profile Style */

/* Base Styles */
.mobile-profile-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

@media (min-width: 768px) {
    .mobile-profile-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-profile-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-profile-container {
        max-width: 840px;
    }
}

/* Header Styles */
.profile-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.profile-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.profile-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.profile-header .user-details {
    flex: 1;
}

.profile-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.profile-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.profile-header .date-info {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 14px;
}

.profile-header .date-info i {
    margin-right: 5px;
}

/* Profile Photo Section */
.profile-photo-section {
    text-align: center;
    margin-bottom: 20px;
}

.profile-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
}

.profile-photo-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    color: #adb5bd;
    margin: 0 auto 10px;
}

.change-photo-btn {
    background-color: #4e73df;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.change-photo-btn i {
    margin-right: 5px;
}

/* Profile Form */
.profile-form-section {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 15px;
}

.profile-form-title {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 10px 12px;
    font-size: 14px;
    width: 100%;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-control[readonly] {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

.form-select {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 10px 12px;
    font-size: 14px;
    width: 100%;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
}

.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    font-weight: 500;
    width: 100%;
    text-align: center;
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    font-weight: 500;
    width: 100%;
    text-align: center;
}

.btn-link {
    color: #4e73df;
    text-decoration: none;
    font-size: 14px;
    padding: 8px;
    text-align: center;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .profile-header {
        padding: 15px;
    }

    .profile-form-section {
        padding: 12px;
    }
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.action-button {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 12px 8px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-button .action-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    color: white;
    font-size: 18px;
}

.action-button .action-icon.primary {
    background-color: #4e73df;
}

.action-button .action-icon.success {
    background-color: #1cc88a;
}

.action-button .action-icon.warning {
    background-color: #f6c23e;
}

.action-button .action-icon.info {
    background-color: #36b9cc;
}

.action-button .action-icon.danger {
    background-color: #e74a3b;
}

.action-button .action-text {
    font-size: 14px;
    color: #343a40;
    font-weight: 500;
}

@media (min-width: 768px) {
    .quick-actions {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* File Input Styling */
.custom-file-input {
    position: absolute;
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    z-index: -1;
}

.custom-file-label {
    background-color: #4e73df;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.custom-file-label i {
    margin-right: 5px;
}

.file-name-display {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    word-break: break-all;
}
