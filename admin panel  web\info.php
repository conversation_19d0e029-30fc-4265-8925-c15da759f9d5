<?php
// Include file konfigurasi
require_once 'config/config.php';

// Cek login
if (!isLoggedIn() || !isAdmin()) {
    redirect('index.php');
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informasi Sistem - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header text-center">
                <h4>Informasi Sistem <?php echo APP_NAME; ?></h4>
            </div>
            <div class="card-body">
                <h5>Informasi PHP</h5>
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Versi PHP</th>
                        <td><?php echo phpversion(); ?></td>
                    </tr>
                    <tr>
                        <th>Sistem Operasi</th>
                        <td><?php echo php_uname(); ?></td>
                    </tr>
                    <tr>
                        <th>Server Web</th>
                        <td><?php echo $_SERVER['SERVER_SOFTWARE']; ?></td>
                    </tr>
                    <tr>
                        <th>Database</th>
                        <td>MySQL</td>
                    </tr>
                    <tr>
                        <th>Ekstensi PHP</th>
                        <td>
                            <?php
                            $required_extensions = ['mysqli', 'gd', 'json', 'session', 'mbstring'];
                            foreach ($required_extensions as $ext) {
                                echo $ext . ': ' . (extension_loaded($ext) ? '<span class="text-success">Tersedia</span>' : '<span class="text-danger">Tidak Tersedia</span>') . '<br>';
                            }
                            ?>
                        </td>
                    </tr>
                </table>
                
                <h5 class="mt-4">Informasi Direktori</h5>
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Root Path</th>
                        <td><?php echo ROOT_PATH; ?></td>
                    </tr>
                    <tr>
                        <th>Upload Path</th>
                        <td><?php echo UPLOAD_PATH; ?></td>
                    </tr>
                    <tr>
                        <th>Upload Path Writeable</th>
                        <td><?php echo is_writable(UPLOAD_PATH) ? '<span class="text-success">Ya</span>' : '<span class="text-danger">Tidak</span>'; ?></td>
                    </tr>
                </table>
                
                <h5 class="mt-4">Informasi Aplikasi</h5>
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">Nama Aplikasi</th>
                        <td><?php echo APP_NAME; ?></td>
                    </tr>
                    <tr>
                        <th>Versi Aplikasi</th>
                        <td><?php echo APP_VERSION; ?></td>
                    </tr>
                    <tr>
                        <th>Base URL</th>
                        <td><?php echo BASE_URL; ?></td>
                    </tr>
                    <tr>
                        <th>Zona Waktu</th>
                        <td><?php echo date_default_timezone_get(); ?></td>
                    </tr>
                </table>
                
                <div class="text-center mt-4">
                    <a href="<?php echo isAdmin() ? 'admin/index.php' : 'index.php'; ?>" class="btn btn-primary">Kembali ke Dashboard</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
