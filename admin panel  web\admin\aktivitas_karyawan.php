<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil data karyawan untuk filter
$karyawan = getAllKaryawan();

// Filter data
$filter_user_id = isset($_GET['user_id']) ? clean($_GET['user_id']) : '';
$filter_tanggal_awal = isset($_GET['tanggal_awal']) ? clean($_GET['tanggal_awal']) : date('Y-m-d', strtotime('-7 days'));
$filter_tanggal_akhir = isset($_GET['tanggal_akhir']) ? clean($_GET['tanggal_akhir']) : date('Y-m-d');
$filter_status = isset($_GET['status']) ? clean($_GET['status']) : '';

// Untuk kompatibilitas dengan kode lama
$filter_tanggal = isset($_GET['tanggal']) ? clean($_GET['tanggal']) : '';

// Query dasar
$query = "SELECT ak.*, u.nik, u.nama, l.nama_lokasi
          FROM aktivitas_karyawan ak
          JOIN users u ON ak.user_id = u.id
          LEFT JOIN lokasi l ON ak.lokasi_id = l.id
          WHERE 1=1";

// Tambahkan filter
if (!empty($filter_user_id)) {
    $query .= " AND ak.user_id = '$filter_user_id'";
}

// Jika filter tanggal lama digunakan
if (!empty($filter_tanggal)) {
    $query .= " AND ak.tanggal = '$filter_tanggal'";
} else {
    // Gunakan filter range tanggal
    if (!empty($filter_tanggal_awal)) {
        $query .= " AND ak.tanggal >= '$filter_tanggal_awal'";
    }

    if (!empty($filter_tanggal_akhir)) {
        $query .= " AND ak.tanggal <= '$filter_tanggal_akhir'";
    }
}

if (!empty($filter_status)) {
    $query .= " AND ak.status = '$filter_status'";
}

// Tambahkan pengurutan
$query .= " ORDER BY ak.tanggal DESC, ak.waktu DESC";

// Eksekusi query
$result = mysqli_query($conn, $query);

// Ambil data aktivitas
$aktivitas = [];
while ($row = mysqli_fetch_assoc($result)) {
    $aktivitas[] = $row;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Aktivitas Karyawan</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Aktivitas Karyawan</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Aktivitas
        </div>
        <div class="card-body">
            <form method="get" action="" class="row g-3">
                <!-- Baris pertama: Filter karyawan dan status -->
                <div class="col-md-4">
                    <label for="user_id" class="form-label">Karyawan</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">Semua Karyawan</option>
                        <?php foreach ($karyawan as $k): ?>
                            <option value="<?php echo $k['id']; ?>" <?php echo $filter_user_id == $k['id'] ? 'selected' : ''; ?>>
                                <?php echo $k['nik'] . ' - ' . $k['nama']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Semua Status</option>
                        <option value="di dalam radius" <?php echo $filter_status == 'di dalam radius' ? 'selected' : ''; ?>>Di Dalam Radius</option>
                        <option value="di luar radius" <?php echo $filter_status == 'di luar radius' ? 'selected' : ''; ?>>Di Luar Radius</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <a href="aktivitas_karyawan.php" class="btn btn-secondary">
                        <i class="fas fa-sync-alt me-1"></i> Reset
                    </a>
                </div>

                <!-- Baris kedua: Filter tanggal -->
                <div class="col-md-4">
                    <label for="tanggal_awal" class="form-label">Tanggal Awal</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                        <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?php echo $filter_tanggal_awal; ?>">
                    </div>
                </div>
                <div class="col-md-4">
                    <label for="tanggal_akhir" class="form-label">Tanggal Akhir</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                        <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?php echo $filter_tanggal_akhir; ?>">
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="button" class="btn btn-success me-2" id="applyDateRange">
                        <i class="fas fa-calendar-check me-1"></i> Terapkan Tanggal
                    </button>
                    <button type="button" class="btn btn-info" id="setDefaultDates">
                        <i class="fas fa-redo me-1"></i> 7 Hari Terakhir
                    </button>
                </div>

                <!-- Informasi rentang tanggal yang aktif -->
                <?php if (!empty($filter_tanggal_awal) && !empty($filter_tanggal_akhir)): ?>
                <div class="col-12 mt-2">
                    <div class="alert alert-info py-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Menampilkan data dari <strong><?php echo date('d/m/Y', strtotime($filter_tanggal_awal)); ?></strong>
                        sampai <strong><?php echo date('d/m/Y', strtotime($filter_tanggal_akhir)); ?></strong>
                    </div>
                </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Peta Aktivitas Karyawan -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-map-marked-alt me-1"></i>
                Peta Aktivitas Karyawan
                <?php if (!empty($filter_tanggal_awal) && !empty($filter_tanggal_akhir)): ?>
                <small class="text-muted ms-2">
                    (<?php echo date('d/m/Y', strtotime($filter_tanggal_awal)); ?> - <?php echo date('d/m/Y', strtotime($filter_tanggal_akhir)); ?>)
                </small>
                <?php endif; ?>
            </div>
            <div>
                <button type="button" class="btn btn-primary btn-sm" id="toggleMapView">
                    <i class="fas fa-expand-alt me-1"></i> Perbesar Peta
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="aktivitas-map" class="leaflet-container"></div>
            <div class="mt-3">
                <div class="row">
                    <div class="col-md-6">
                        <div class="map-legend">
                            <div class="d-flex align-items-center mb-2">
                                <span class="legend-marker in-radius me-2"></span>
                                <span>Di Dalam Radius</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="legend-marker out-radius me-2"></span>
                                <span>Di Luar Radius</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showAllMarkers" checked>
                            <label class="form-check-label" for="showAllMarkers">Tampilkan semua titik</label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="clusterMarkers" checked>
                            <label class="form-check-label" for="clusterMarkers">Kelompokkan titik yang berdekatan</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabel Aktivitas Karyawan -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-table me-1"></i>
                Data Aktivitas Karyawan
                <?php if (!empty($filter_tanggal_awal) && !empty($filter_tanggal_akhir)): ?>
                <small class="text-muted ms-2">
                    (<?php echo date('d/m/Y', strtotime($filter_tanggal_awal)); ?> - <?php echo date('d/m/Y', strtotime($filter_tanggal_akhir)); ?>)
                </small>
                <?php endif; ?>
            </div>
            <div>
                <a href="check_aktivitas_table.php" class="btn btn-info btn-sm me-2">
                    <i class="fas fa-table me-1"></i> Cek Tabel
                </a>
                <a href="generate_sample_aktivitas.php" class="btn btn-primary btn-sm me-2">
                    <i class="fas fa-database me-1"></i> Generate Data Sampel
                </a>
                <a href="export_aktivitas.php<?php
                    $params = [];
                    if (!empty($filter_user_id)) $params[] = "user_id=$filter_user_id";
                    if (!empty($filter_tanggal_awal)) $params[] = "tanggal_awal=$filter_tanggal_awal";
                    if (!empty($filter_tanggal_akhir)) $params[] = "tanggal_akhir=$filter_tanggal_akhir";
                    if (!empty($filter_status)) $params[] = "status=$filter_status";
                    echo !empty($params) ? '?' . implode('&', $params) : '';
                ?>" class="btn btn-success btn-sm">
                    <i class="fas fa-file-export me-1"></i> Export Data
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-datatable" id="aktivitasTable" data-order='[[3, "desc"], [4, "desc"]]'>
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Tanggal</th>
                            <th>Waktu</th>
                            <th>Lokasi Kantor</th>
                            <th>Status</th>
                            <th>Jarak (m)</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $no = 1; foreach ($aktivitas as $a): ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo $a['nik']; ?></td>
                                <td><?php echo $a['nama']; ?></td>
                                <td><?php echo date('d/m/Y', strtotime($a['tanggal'])); ?></td>
                                <td><?php echo date('H:i:s', strtotime($a['waktu'])); ?></td>
                                <td><?php echo $a['nama_lokasi'] ?? 'Tidak ada'; ?></td>
                                <td>
                                    <?php if ($a['status'] == 'di dalam radius'): ?>
                                        <span class="badge bg-success">Di Dalam Radius</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Di Luar Radius</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo number_format($a['jarak_dari_kantor'], 2); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info view-detail"
                                            data-lat="<?php echo $a['latitude']; ?>"
                                            data-lng="<?php echo $a['longitude']; ?>"
                                            data-nama="<?php echo $a['nama']; ?>"
                                            data-waktu="<?php echo date('d/m/Y H:i:s', strtotime($a['tanggal'] . ' ' . $a['waktu'])); ?>"
                                            data-status="<?php echo $a['status']; ?>"
                                            data-jarak="<?php echo number_format($a['jarak_dari_kantor'], 2); ?>"
                                            data-lokasi="<?php echo $a['nama_lokasi'] ?? 'Tidak ada'; ?>">
                                        <i class="fas fa-info-circle"></i> Detail
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk menampilkan detail -->
<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">Detail Aktivitas Karyawan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-5">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3 text-muted">Informasi Karyawan</h6>
                                <p><strong>Nama:</strong> <span id="detailNama"></span></p>
                                <p><strong>Waktu:</strong> <span id="detailWaktu"></span></p>
                                <p><strong>Lokasi Kantor:</strong> <span id="detailLokasi"></span></p>
                                <p><strong>Status:</strong> <span id="detailStatus"></span></p>
                                <p><strong>Jarak dari Kantor:</strong> <span id="detailJarak"></span> meter</p>
                                <p><strong>Koordinat:</strong> <span id="detailCoords"></span></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3 text-muted">Lokasi pada Peta</h6>
                                <div id="detail-map" class="leaflet-container" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<script>
    $(document).ready(function() {
        // Debug: Tampilkan jumlah data aktivitas
        console.log("Jumlah data aktivitas: <?php echo count($aktivitas); ?>");

        // Debug: Cek apakah tombol toggle range tanggal ada
        console.log("Tombol toggle range tanggal:", $('#toggleDateRange').length > 0 ? "ditemukan" : "tidak ditemukan");
        console.log("Container range tanggal:", $('#dateRangeContainer').length > 0 ? "ditemukan" : "tidak ditemukan");

        <?php if (count($aktivitas) > 0): ?>
        console.log("Contoh data aktivitas pertama:");
        console.log("Latitude: <?php echo isset($aktivitas[0]['latitude']) ? $aktivitas[0]['latitude'] : 'tidak ada'; ?>");
        console.log("Longitude: <?php echo isset($aktivitas[0]['longitude']) ? $aktivitas[0]['longitude'] : 'tidak ada'; ?>");
        console.log("Tipe data latitude: <?php echo isset($aktivitas[0]['latitude']) ? gettype($aktivitas[0]['latitude']) : 'tidak ada'; ?>");
        console.log("Tipe data longitude: <?php echo isset($aktivitas[0]['longitude']) ? gettype($aktivitas[0]['longitude']) : 'tidak ada'; ?>");
        <?php endif; ?>

        // Data aktivitas untuk peta
        const aktivitasData = [
            <?php
            // Debug: Tampilkan data aktivitas
            foreach ($aktivitas as $a):
                // Debug: Tampilkan nilai latitude dan longitude
                echo "// Debug: lat=" . $a['latitude'] . ", lng=" . $a['longitude'] . ", tipe lat=" . gettype($a['latitude']) . ", tipe lng=" . gettype($a['longitude']) . "\n            ";

                // Pastikan nilai latitude dan longitude adalah angka
                $lat = is_numeric($a['latitude']) ? $a['latitude'] : 0;
                $lng = is_numeric($a['longitude']) ? $a['longitude'] : 0;
            ?>
            {
                lat: "<?php echo $lat; ?>",
                lng: "<?php echo $lng; ?>",
                nama: "<?php echo addslashes($a['nama']); ?>",
                nik: "<?php echo $a['nik']; ?>",
                waktu: "<?php echo date('d/m/Y H:i:s', strtotime($a['tanggal'] . ' ' . $a['waktu'])); ?>",
                status: "<?php echo $a['status']; ?>",
                jarak: <?php echo is_numeric($a['jarak_dari_kantor']) ? $a['jarak_dari_kantor'] : 0; ?>,
                lokasi: "<?php echo addslashes($a['nama_lokasi'] ?? 'Tidak ada'); ?>"
            },
            <?php endforeach; ?>
        ];

        // Debug: Tampilkan pesan sebelum inisialisasi peta
        console.log("Inisialisasi peta utama...");

        // Periksa apakah plugin Leaflet.markercluster sudah dimuat
        if (typeof L.markerClusterGroup !== 'function') {
            console.error("Plugin Leaflet.markercluster tidak dimuat dengan benar");

            // Tambahkan script Leaflet.markercluster secara dinamis
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet.markercluster/1.5.3/leaflet.markercluster.js';
            script.onload = function() {
                console.log("Plugin Leaflet.markercluster berhasil dimuat secara dinamis");
                initMap(); // Panggil fungsi untuk inisialisasi peta
            };
            script.onerror = function() {
                console.error("Gagal memuat plugin Leaflet.markercluster secara dinamis");
                document.getElementById('aktivitas-map').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Terjadi kesalahan saat memuat plugin peta. Silakan refresh halaman atau hubungi administrator.
                    </div>
                `;
            };
            document.head.appendChild(script);
            return;
        }

        // Fungsi untuk inisialisasi peta
        function initMap() {

        try {
            // Inisialisasi peta utama
            window.aktivitasMap = L.map('aktivitas-map').setView([-6.2088, 106.8456], 12);
            const map = window.aktivitasMap;

            // Debug: Tampilkan pesan setelah inisialisasi peta
            console.log("Peta berhasil diinisialisasi");

            // Tambahkan tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            // Debug: Tampilkan pesan setelah menambahkan tile layer
            console.log("Tile layer berhasil ditambahkan");

            // Marker cluster group
            const markers = L.markerClusterGroup();

            // Debug: Tampilkan pesan sebelum menambahkan marker
            console.log("Menambahkan marker...");

            // Tambahkan marker untuk setiap aktivitas
            const allMarkers = [];

            // Cek apakah ada data aktivitas
            if (aktivitasData.length > 0) {
                console.log("Jumlah data aktivitas:", aktivitasData.length);

                aktivitasData.forEach(function(item, index) {
                    // Debug: Tampilkan data item
                    console.log(`Item ${index}:`, item);

                    // Validasi koordinat dan konversi ke angka jika perlu
                    const lat = parseFloat(item.lat);
                    const lng = parseFloat(item.lng);

                    console.log(`Koordinat ${index} setelah parsing:`, lat, lng);

                    if (!isNaN(lat) && !isNaN(lng)) {
                        // Tentukan ikon berdasarkan status
                        const icon = L.divIcon({
                            className: 'custom-div-icon',
                            html: `<div class="marker-pin ${item.status === 'di dalam radius' ? 'in-radius' : 'out-radius'}"></div>`,
                            iconSize: [30, 42],
                            iconAnchor: [15, 42]
                        });

                        // Buat marker dengan koordinat yang sudah dikonversi
                        const marker = L.marker([lat, lng], {icon: icon});

                        // Tambahkan popup
                        marker.bindPopup(`
                            <div class="popup-content">
                                <h6>${item.nama}</h6>
                                <p><strong>NIK:</strong> ${item.nik}</p>
                                <p><strong>Waktu:</strong> ${item.waktu}</p>
                                <p><strong>Status:</strong> ${item.status}</p>
                                <p><strong>Jarak:</strong> ${item.jarak.toFixed(2)} meter</p>
                            </div>
                        `);

                        // Tambahkan marker ke array dan cluster group
                        allMarkers.push(marker);
                        markers.addLayer(marker);

                        console.log(`Marker ${index} berhasil ditambahkan`);
                    } else {
                        console.warn("Koordinat tidak valid:", item);
                    }
                });

                // Debug: Tampilkan jumlah marker yang berhasil ditambahkan
                console.log("Jumlah marker yang berhasil ditambahkan:", allMarkers.length);

                // Tambahkan marker cluster ke peta
                map.addLayer(markers);
                console.log("Marker cluster berhasil ditambahkan ke peta");

                // Jika ada marker, atur tampilan peta untuk menampilkan semua marker
                if (allMarkers.length > 0) {
                    const group = new L.featureGroup(allMarkers);
                    map.fitBounds(group.getBounds().pad(0.1));
                    console.log("Tampilan peta disesuaikan dengan marker");

                    // Toggle tampilan semua marker
                    $('#showAllMarkers').on('change', function() {
                        try {
                            if ($(this).is(':checked')) {
                                map.addLayer(markers);
                                console.log("Semua marker ditampilkan");
                            } else {
                                map.removeLayer(markers);
                                console.log("Semua marker disembunyikan");
                            }
                        } catch (error) {
                            console.error("Error saat toggle tampilan marker:", error);
                        }
                    });

                    // Toggle clustering
                    $('#clusterMarkers').on('change', function() {
                        try {
                            map.removeLayer(markers);

                            if ($(this).is(':checked')) {
                                // Gunakan marker cluster
                                markers.clearLayers();
                                allMarkers.forEach(marker => markers.addLayer(marker));
                                map.addLayer(markers);
                                console.log("Clustering marker diaktifkan");
                            } else {
                                // Tampilkan marker individual
                                allMarkers.forEach(marker => map.addLayer(marker));
                                console.log("Marker individual ditampilkan");
                            }
                        } catch (error) {
                            console.error("Error saat toggle clustering:", error);
                        }
                    });

                } else {
                    console.warn("Tidak ada marker yang valid untuk ditampilkan");
                }
            } else {
                console.warn("Tidak ada data aktivitas");

                // Jika tidak ada data aktivitas, tampilkan pesan
                const noDataDiv = document.createElement('div');
                noDataDiv.className = 'no-data-message';
                noDataDiv.innerHTML = `
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                        <h5>Tidak ada data aktivitas karyawan</h5>
                        <p>Silakan generate data sampel atau tambahkan data aktivitas karyawan terlebih dahulu.</p>
                        <a href="generate_sample_aktivitas.php" class="btn btn-primary btn-sm mt-2">
                            <i class="fas fa-database me-1"></i> Generate Data Sampel
                        </a>
                    </div>
                `;

                // Tambahkan pesan ke dalam container peta
                document.getElementById('aktivitas-map').appendChild(noDataDiv);
            }

        } catch (error) {
            // Tangkap error dan tampilkan di console
            console.error("Error saat inisialisasi peta:", error);

            // Tampilkan pesan error di halaman
            const mapContainer = document.getElementById('aktivitas-map');
            mapContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Terjadi kesalahan saat memuat peta. Silakan refresh halaman atau hubungi administrator.
                    <p class="mt-2"><small>Detail error: ${error.message}</small></p>
                </div>
            `;
        }

        } // Akhir dari fungsi initMap()

        // Toggle tampilan peta (perbesar/perkecil)
        let isMapExpanded = false;
        $('#toggleMapView').on('click', function() {
            try {
                isMapExpanded = !isMapExpanded;
                if (isMapExpanded) {
                    $('#aktivitas-map').css('height', '600px');
                    $(this).html('<i class="fas fa-compress-alt me-1"></i> Perkecil Peta');
                } else {
                    $('#aktivitas-map').css('height', '400px');
                    $(this).html('<i class="fas fa-expand-alt me-1"></i> Perbesar Peta');
                }

                // Perbarui ukuran peta jika map sudah diinisialisasi
                if (window.aktivitasMap) {
                    window.aktivitasMap.invalidateSize();
                    console.log("Ukuran peta diperbarui");
                }
            } catch (error) {
                console.error("Error saat toggle tampilan peta:", error);
            }
        });

        // Panggil fungsi initMap jika plugin sudah dimuat
        if (typeof L.markerClusterGroup === 'function') {
            initMap();
        } else {
            console.warn("Plugin Leaflet.markercluster tidak tersedia, menunggu pemuatan dinamis...");
        }

        // Tampilkan detail saat tombol diklik
        $('.view-detail').on('click', function() {
            const lat = $(this).data('lat');
            const lng = $(this).data('lng');
            const nama = $(this).data('nama');
            const waktu = $(this).data('waktu');
            const status = $(this).data('status');
            const jarak = $(this).data('jarak');
            const lokasi = $(this).data('lokasi');

            // Set informasi di modal
            $('#detailNama').text(nama);
            $('#detailWaktu').text(waktu);
            $('#detailCoords').text(`${lat}, ${lng}`);
            $('#detailStatus').html(status === 'di dalam radius' ?
                '<span class="badge bg-success">Di Dalam Radius</span>' :
                '<span class="badge bg-danger">Di Luar Radius</span>');
            $('#detailJarak').text(jarak);
            $('#detailLokasi').text(lokasi);

            // Tampilkan modal
            $('#detailModal').modal('show');

            // Inisialisasi peta detail setelah modal ditampilkan
            $('#detailModal').on('shown.bs.modal', function() {
                try {
                    console.log("Inisialisasi peta detail...");
                    console.log("Koordinat:", lat, lng);

                    // Konversi koordinat ke angka
                    const detailLat = parseFloat(lat);
                    const detailLng = parseFloat(lng);

                    console.log("Koordinat setelah parsing:", detailLat, detailLng);

                    // Gunakan koordinat default jika nilai tidak valid
                    const validLat = !isNaN(detailLat) ? detailLat : -6.2088;
                    const validLng = !isNaN(detailLng) ? detailLng : 106.8456;

                    console.log("Koordinat yang digunakan:", validLat, validLng);

                    // Periksa apakah plugin Leaflet.markercluster sudah dimuat
                    if (typeof L.markerClusterGroup !== 'function') {
                        console.error("Plugin Leaflet.markercluster tidak dimuat dengan benar untuk peta detail");

                        // Tampilkan pesan error di peta detail
                        document.getElementById('detail-map').innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Plugin peta tidak dimuat dengan benar. Silakan refresh halaman.
                            </div>
                        `;
                        return;
                    }

                    // Buat peta
                    const detailMap = L.map('detail-map').setView([validLat, validLng], 15);
                    console.log("Peta detail berhasil diinisialisasi");

                    // Tambahkan tile layer
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(detailMap);
                    console.log("Tile layer berhasil ditambahkan ke peta detail");

                    // Tentukan ikon berdasarkan status
                    const icon = L.divIcon({
                        className: 'custom-div-icon',
                        html: `<div class="marker-pin ${status === 'di dalam radius' ? 'in-radius' : 'out-radius'}"></div>`,
                        iconSize: [30, 42],
                        iconAnchor: [15, 42]
                    });

                    if (!isNaN(detailLat) && !isNaN(detailLng)) {
                        // Tambahkan marker
                        L.marker([validLat, validLng], {icon: icon}).addTo(detailMap)
                        .bindPopup(`
                            <div class="popup-content">
                                <h6>${nama}</h6>
                                <p><strong>Waktu:</strong> ${waktu}</p>
                                <p><strong>Status:</strong> ${status}</p>
                                <p><strong>Jarak:</strong> ${jarak} meter</p>
                            </div>
                        `).openPopup();
                        console.log("Marker berhasil ditambahkan ke peta detail");
                    }

                    // Perbaiki ukuran peta
                    setTimeout(function() {
                        detailMap.invalidateSize();
                        console.log("Ukuran peta detail diperbaiki");
                    }, 100);

                } catch (error) {
                    console.error("Error saat inisialisasi peta detail:", error);

                    // Tampilkan pesan error di peta detail
                    document.getElementById('detail-map').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Terjadi kesalahan saat memuat peta detail.
                            <p class="mt-2"><small>Detail error: ${error.message}</small></p>
                        </div>
                    `;
                }
            });
        });

        // Reset peta detail saat modal ditutup
        $('#detailModal').on('hidden.bs.modal', function() {
            $('#detail-map').html('');
        });

        // Terapkan range tanggal
        $('#applyDateRange').on('click', function() {
            console.log("Tombol Terapkan Tanggal diklik");

            const tanggalAwal = $('#tanggal_awal').val();
            const tanggalAkhir = $('#tanggal_akhir').val();

            console.log("Tanggal Awal:", tanggalAwal);
            console.log("Tanggal Akhir:", tanggalAkhir);

            if (!tanggalAwal || !tanggalAkhir) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Perhatian',
                    text: 'Silakan isi tanggal awal dan tanggal akhir terlebih dahulu!'
                });
                return;
            }

            if (tanggalAwal > tanggalAkhir) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Tanggal awal tidak boleh lebih besar dari tanggal akhir!'
                });
                return;
            }

            // Submit form
            $('form').first().submit();
        });

        // Set tanggal default (7 hari terakhir)
        $('#setDefaultDates').on('click', function() {
            console.log("Tombol 7 Hari Terakhir diklik");

            // Set tanggal akhir ke hari ini
            const today = new Date();
            const tanggalAkhir = today.toISOString().split('T')[0]; // Format YYYY-MM-DD

            // Set tanggal awal ke 7 hari yang lalu
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(today.getDate() - 7);
            const tanggalAwal = sevenDaysAgo.toISOString().split('T')[0]; // Format YYYY-MM-DD

            console.log("Tanggal Awal (7 hari lalu):", tanggalAwal);
            console.log("Tanggal Akhir (hari ini):", tanggalAkhir);

            // Set nilai input
            $('#tanggal_awal').val(tanggalAwal);
            $('#tanggal_akhir').val(tanggalAkhir);

            // Submit form
            $('form').first().submit();
        });
    });
</script>

<style>
    /* Style untuk marker dan legend */
    .legend-marker {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
    }

    .legend-marker.in-radius {
        background-color: #28a745;
        border: 2px solid #fff;
    }

    .legend-marker.out-radius {
        background-color: #dc3545;
        border: 2px solid #fff;
    }

    /* Style untuk custom marker */
    .marker-pin {
        width: 30px;
        height: 30px;
        border-radius: 50% 50% 50% 0;
        background: #c30b82;
        position: absolute;
        transform: rotate(-45deg);
        left: 50%;
        top: 50%;
        margin: -15px 0 0 -15px;
    }

    .marker-pin::after {
        content: '';
        width: 24px;
        height: 24px;
        margin: 3px 0 0 3px;
        background: #fff;
        position: absolute;
        border-radius: 50%;
    }

    .marker-pin.in-radius {
        background: #28a745;
    }

    .marker-pin.out-radius {
        background: #dc3545;
    }

    /* Style untuk popup */
    .popup-content {
        padding: 5px;
    }

    .popup-content h6 {
        margin-top: 0;
        margin-bottom: 10px;
        font-weight: bold;
    }

    .popup-content p {
        margin: 5px 0;
        font-size: 13px;
    }

    /* Style untuk pesan tidak ada data */
    .no-data-message {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80%;
        z-index: 1000;
    }

    .no-data-message .alert {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Style untuk container peta */
    #aktivitas-map, #detail-map {
        width: 100%;
        height: 400px;
        position: relative;
        z-index: 1;
    }

    /* Style untuk leaflet container */
    .leaflet-container {
        width: 100%;
        height: 400px;
        border-radius: 5px;
        background: #f8f8f8;
        border: 1px solid #ddd;
    }

    /* Pastikan container peta memiliki tinggi yang cukup */
    .card-body {
        min-height: 450px;
    }

    /* Style untuk input tanggal */
    .input-group-text {
        background-color: #f8f9fa;
        border-right: none;
    }

    input[type="date"] {
        border-left: none;
    }

    input[type="date"]:focus {
        box-shadow: none;
        border-color: #ced4da;
        border-left: none;
    }

    /* Style untuk alert info tanggal */
    .alert-info {
        background-color: #e3f2fd;
        border-color: #b3e5fc;
        color: #0277bd;
    }

    /* Style untuk tombol filter tanggal */
    #applyDateRange, #setDefaultDates {
        transition: all 0.3s ease;
    }

    #applyDateRange:hover, #setDefaultDates:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
</style>
