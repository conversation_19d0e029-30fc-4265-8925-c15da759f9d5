<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil ID presensi
$id = isset($_GET['id']) ? clean($_GET['id']) : 0;

// Ambil data presensi
$query = "SELECT p.*, u.nik, u.nama, u.bidang, u.jabatan
          FROM presensi p
          JOIN users u ON p.user_id = u.id
          WHERE p.id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Data presensi tidak ditemukan!');
    redirect('admin/monitoring.php');
}

$presensi = mysqli_fetch_assoc($result);
$tanggal = $presensi['tanggal'];

// Proses form edit
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validasi input
    $jam_masuk = isset($_POST['jam_masuk']) ? clean($_POST['jam_masuk']) : '';
    $jam_pulang = isset($_POST['jam_pulang']) ? clean($_POST['jam_pulang']) : null;
    $status = isset($_POST['status']) ? clean($_POST['status']) : '';
    $keterangan = isset($_POST['keterangan']) ? clean($_POST['keterangan']) : '';
    $lokasi_masuk = isset($_POST['lokasi_masuk']) ? clean($_POST['lokasi_masuk']) : '';
    $lokasi_pulang = isset($_POST['lokasi_pulang']) ? clean($_POST['lokasi_pulang']) : '';

    // Validasi jam masuk
    if (empty($jam_masuk)) {
        setMessage('danger', 'Jam masuk tidak boleh kosong!');
    } else {
        // Update data presensi
        $query = "UPDATE presensi SET
                  jam_masuk = '$jam_masuk',
                  jam_pulang = " . ($jam_pulang ? "'$jam_pulang'" : "NULL") . ",
                  lokasi_masuk = '$lokasi_masuk',
                  lokasi_pulang = " . ($lokasi_pulang ? "'$lokasi_pulang'" : "NULL") . ",
                  status = '$status',
                  keterangan = '$keterangan',
                  updated_at = NOW()
                  WHERE id = '$id'";

        if (mysqli_query($conn, $query)) {
            setMessage('success', 'Data presensi berhasil diperbarui!');
            redirect('admin/monitoring.php?tanggal=' . $tanggal);
        } else {
            setMessage('danger', 'Gagal memperbarui data presensi: ' . mysqli_error($conn));
        }
    }
}

// Include header
include_once '../includes/header.php';

// Ambil pesan jika ada
$message = getMessage();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Edit Presensi</h1>
        <a href="monitoring.php?tanggal=<?php echo $tanggal; ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $message['text']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Edit Data Presensi</h6>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Informasi Karyawan</h5>
                    <table class="table table-borderless">
                        <tr>
                            <td width="150">NIK</td>
                            <td>: <?php echo $presensi['nik']; ?></td>
                        </tr>
                        <tr>
                            <td>Nama</td>
                            <td>: <?php echo $presensi['nama']; ?></td>
                        </tr>
                        <tr>
                            <td>Bidang</td>
                            <td>: <?php echo $presensi['bidang']; ?></td>
                        </tr>
                        <tr>
                            <td>Jabatan</td>
                            <td>: <?php echo $presensi['jabatan']; ?></td>
                        </tr>
                        <tr>
                            <td>Tanggal</td>
                            <td>: <?php echo date('d F Y', strtotime($presensi['tanggal'])); ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <form method="post" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="jam_masuk" class="form-label">Jam Masuk <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="jam_masuk" name="jam_masuk" value="<?php echo $presensi['jam_masuk']; ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="lokasi_masuk" class="form-label">Lokasi Masuk</label>
                            <input type="text" class="form-control" id="lokasi_masuk" name="lokasi_masuk" value="<?php echo $presensi['lokasi_masuk']; ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="jam_pulang" class="form-label">Jam Pulang</label>
                            <input type="time" class="form-control" id="jam_pulang" name="jam_pulang" value="<?php echo $presensi['jam_pulang']; ?>">
                        </div>
                        <div class="mb-3">
                            <label for="lokasi_pulang" class="form-label">Lokasi Pulang</label>
                            <input type="text" class="form-control" id="lokasi_pulang" name="lokasi_pulang" value="<?php echo $presensi['lokasi_pulang']; ?>">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="Tepat Waktu" <?php echo ($presensi['status'] == 'Tepat Waktu') ? 'selected' : ''; ?>>Tepat Waktu</option>
                                <option value="Terlambat" <?php echo ($presensi['status'] == 'Terlambat') ? 'selected' : ''; ?>>Terlambat</option>
                                <option value="Pulang Awal" <?php echo ($presensi['status'] == 'Pulang Awal') ? 'selected' : ''; ?>>Pulang Awal</option>
                                <option value="Lembur" <?php echo ($presensi['status'] == 'Lembur') ? 'selected' : ''; ?>>Lembur</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="keterangan" class="form-label">Keterangan</label>
                            <textarea class="form-control" id="keterangan" name="keterangan" rows="3"><?php echo $presensi['keterangan']; ?></textarea>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end">
                    <a href="monitoring.php?tanggal=<?php echo $tanggal; ?>" class="btn btn-secondary me-2">Batal</a>
                    <button type="button" id="btnConfirm" class="btn btn-primary">Simpan Perubahan</button>
                    <button type="submit" id="btnSubmit" class="d-none">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Konfirmasi sebelum menyimpan perubahan
    document.getElementById('btnConfirm').addEventListener('click', function() {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menyimpan perubahan data presensi ini?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Simpan',
            cancelButtonText: 'Batal',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById('btnSubmit').click();
            }
        });
    });
});
</script>
