<?php
/**
 * API untuk mendapatkan data dashboard karyawan
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Ambil data dari request
$user_id = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);
    $user_id = isset($data['user_id']) ? clean($data['user_id']) : null;
} else {
    $user_id = isset($_GET['user_id']) ? clean($_GET['user_id']) : null;
}

// Validasi user_id
if (!$user_id) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'User ID harus diisi'
    ]);
    exit;
}

// Ambil data karyawan
$query = "SELECT u.*, b.nama_bidang, l.nama_lokasi 
          FROM users u 
          LEFT JOIN bidang b ON u.bidang_id = b.id 
          LEFT JOIN lokasi l ON u.lokasi_id = l.id 
          WHERE u.id = '$user_id' AND u.role = 'karyawan'";
$result = mysqli_query($conn, $query);

if (!$result || mysqli_num_rows($result) == 0) {
    http_response_code(404);
    echo json_encode([
        'status' => 'error',
        'message' => 'Karyawan tidak ditemukan'
    ]);
    exit;
}

$karyawan = mysqli_fetch_assoc($result);

// Ambil data presensi hari ini
$today = date('Y-m-d');
$query_presensi = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result_presensi = mysqli_query($conn, $query_presensi);
$presensi_hari_ini = mysqli_num_rows($result_presensi) > 0 ? mysqli_fetch_assoc($result_presensi) : null;

// Ambil jam kerja
$jam_kerja = null;
if ($karyawan['bidang_id']) {
    $query_jam = "SELECT * FROM jam_kerja WHERE bidang_id = '{$karyawan['bidang_id']}'";
    $result_jam = mysqli_query($conn, $query_jam);
    if ($result_jam && mysqli_num_rows($result_jam) > 0) {
        $jam_kerja = mysqli_fetch_assoc($result_jam);
    }
}

// Hitung statistik bulan ini
$bulan_ini = date('Y-m');
$query_stats = "SELECT 
                    COUNT(*) as total_hadir,
                    SUM(CASE WHEN status = 'Terlambat' THEN 1 ELSE 0 END) as total_terlambat,
                    SUM(CASE WHEN status = 'Tepat Waktu' THEN 1 ELSE 0 END) as total_tepat_waktu
                FROM presensi 
                WHERE user_id = '$user_id' 
                AND DATE_FORMAT(tanggal, '%Y-%m') = '$bulan_ini'";
$result_stats = mysqli_query($conn, $query_stats);
$stats = mysqli_fetch_assoc($result_stats);

// Cek apakah hari ini libur
$query_libur = "SELECT * FROM hari_libur WHERE tanggal = '$today'";
$result_libur = mysqli_query($conn, $query_libur);
$is_holiday = mysqli_num_rows($result_libur) > 0;
$holiday_info = $is_holiday ? mysqli_fetch_assoc($result_libur) : null;

// Response data
echo json_encode([
    'status' => 'success',
    'data' => [
        'karyawan' => [
            'id' => $karyawan['id'],
            'nik' => $karyawan['nik'],
            'nama' => $karyawan['nama'],
            'bidang_nama' => $karyawan['nama_bidang'],
            'jabatan' => $karyawan['jabatan'],
            'lokasi_nama' => $karyawan['nama_lokasi'],
            'foto_profil' => $karyawan['foto_profil']
        ],
        'presensi_hari_ini' => $presensi_hari_ini,
        'jam_kerja' => $jam_kerja,
        'statistik' => [
            'total_hadir' => (int)$stats['total_hadir'],
            'total_terlambat' => (int)$stats['total_terlambat'],
            'total_tepat_waktu' => (int)$stats['total_tepat_waktu']
        ],
        'is_holiday' => $is_holiday,
        'holiday_info' => $holiday_info,
        'tanggal' => $today,
        'waktu_sekarang' => date('H:i:s')
    ]
]);
?>
