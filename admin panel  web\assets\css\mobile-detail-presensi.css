/* Mobile Detail Presensi Style */

/* Base Styles */
.mobile-detail-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

@media (min-width: 768px) {
    .mobile-detail-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-detail-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-detail-container {
        max-width: 840px;
    }
}

/* Header Styles */
.detail-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.detail-header .user-info {
    display: flex;
    align-items: center;
}

.detail-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
}

.detail-header .user-avatar .user-photo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
    max-height: 100%;
}

.detail-header .user-details {
    flex: 1;
}

.detail-header .user-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.detail-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.detail-header .date-info {
    margin-top: 15px;
    font-size: 14px;
    opacity: 0.8;
}

/* Content Styles */
.detail-content {
    padding: 0 15px 80px 15px;
}

.detail-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.detail-title h6 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.back-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #4e73df;
    border: none;
    border-radius: 8px;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.back-button:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button i {
    margin-right: 8px;
}

/* Card Styles */
.detail-card {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
}

.detail-card-header {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #4e73df;
}

.detail-card-body {
    padding: 15px;
}

/* Table Styles */
.detail-table {
    width: 100%;
    border-collapse: collapse;
}

.detail-table tr {
    border-bottom: 1px solid #e9ecef;
}

.detail-table tr:last-child {
    border-bottom: none;
}

.detail-table th {
    width: 40%;
    padding: 12px 8px;
    text-align: left;
    color: #6c757d;
    font-weight: 500;
    vertical-align: top;
}

.detail-table td {
    padding: 12px 8px;
    color: #343a40;
}

/* Status Badge Styles */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 50px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.success {
    background-color: #e6f7ee;
    color: #1cc88a;
}

.status-badge.warning {
    background-color: #fff8e6;
    color: #f6c23e;
}

.status-badge.info {
    background-color: #e6f3ff;
    color: #36b9cc;
}

.status-badge.danger {
    background-color: #ffe6e6;
    color: #e74a3b;
}

/* Image Styles */
.detail-image {
    text-align: center;
    padding: 15px;
}

.detail-image img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.no-image-alert {
    background-color: #e6f3ff;
    color: #36b9cc;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .detail-header {
        padding: 15px;
    }
    
    .detail-content {
        padding: 0 10px 80px 10px;
    }
}
