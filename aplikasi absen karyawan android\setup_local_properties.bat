@echo off
echo ========================================
echo    SETUP LOCAL PROPERTIES
echo ========================================
echo.

REM Check if local.properties already exists
if exist "android\local.properties" (
    echo File local.properties sudah ada.
    set /p overwrite="Overwrite? (y/n): "
    if /i not "%overwrite%"=="y" (
        echo Setup dibatalkan.
        pause
        exit /b 0
    )
)

echo Mencari Flutter SDK...

REM Try to find Flutter SDK path
for /f "tokens=*" %%i in ('where flutter 2^>nul') do (
    set "flutter_path=%%i"
    goto :found_flutter
)

echo Flutter tidak ditemukan di PATH.
set /p flutter_path="Masukkan path Flutter SDK (contoh: C:\flutter): "

:found_flutter
REM Get directory from flutter.exe path
for %%i in ("%flutter_path%") do set "flutter_dir=%%~dpi"
REM Remove trailing backslash and \bin
set "flutter_dir=%flutter_dir:~0,-1%"
set "flutter_dir=%flutter_dir:\bin=%"

echo Flutter SDK ditemukan di: %flutter_dir%

REM Try to find Android SDK
set "android_sdk="
if exist "%LOCALAPPDATA%\Android\Sdk" (
    set "android_sdk=%LOCALAPPDATA%\Android\Sdk"
) else if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
    set "android_sdk=%USERPROFILE%\AppData\Local\Android\Sdk"
) else (
    echo Android SDK tidak ditemukan otomatis.
    set /p android_sdk="Masukkan path Android SDK (atau kosongkan jika auto-detect): "
)

REM Create local.properties
echo Membuat file local.properties...
(
    echo # Auto-generated local.properties
    echo # Generated on %date% %time%
    echo.
    echo flutter.sdk=%flutter_dir%
    if defined android_sdk (
        echo sdk.dir=%android_sdk%
    )
) > android\local.properties

echo.
echo ========================================
echo    SETUP BERHASIL!
echo ========================================
echo.
echo File local.properties telah dibuat dengan konfigurasi:
echo Flutter SDK: %flutter_dir%
if defined android_sdk (
    echo Android SDK: %android_sdk%
) else (
    echo Android SDK: Auto-detect
)
echo.
echo Sekarang Anda bisa menjalankan build_apk.bat
echo.
pause
