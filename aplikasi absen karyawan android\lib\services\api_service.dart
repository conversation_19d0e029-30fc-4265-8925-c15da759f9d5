import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../utils/constants.dart';

class ApiService {
  static const Duration _timeout = Duration(seconds: 30);

  static Future<Map<String, dynamic>> _makeRequest(
    String url,
    String method, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    try {
      final uri = Uri.parse(url);
      final defaultHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (headers != null) {
        defaultHeaders.addAll(headers);
      }

      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: defaultHeaders).timeout(_timeout);
          break;
        case 'POST':
          response = await http.post(
            uri,
            headers: defaultHeaders,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(_timeout);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseBody = response.body;
        if (responseBody.isEmpty) {
          return {'status': 'success', 'data': null};
        }
        return jsonDecode(responseBody);
      } else {
        // Try to parse error response
        try {
          final errorResponse = jsonDecode(response.body);
          return {
            'status': 'error',
            'message': errorResponse['message'] ?? 'Terjadi kesalahan server',
          };
        } catch (e) {
          return {
            'status': 'error',
            'message': 'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          };
        }
      }
    } on SocketException {
      return {
        'status': 'error',
        'message': Constants.msgNetworkError,
      };
    } on http.ClientException {
      return {
        'status': 'error',
        'message': Constants.msgNetworkError,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'Terjadi kesalahan: ${e.toString()}',
      };
    }
  }

  // Login API
  static Future<Map<String, dynamic>> login(String nik, String password) async {
    return await _makeRequest(
      Constants.loginEndpoint,
      'POST',
      body: {
        'nik': nik,
        'password': password,
      },
    );
  }

  // Dashboard API
  static Future<Map<String, dynamic>> getDashboard(int userId) async {
    return await _makeRequest(
      Constants.dashboardEndpoint,
      'POST',
      body: {
        'user_id': userId,
      },
    );
  }

  // Presensi API
  static Future<Map<String, dynamic>> submitPresensi({
    required int userId,
    required String jenisAbsen,
    required double latitude,
    required double longitude,
    String? fotoBase64,
  }) async {
    final body = {
      'user_id': userId,
      'jenis_absen': jenisAbsen,
      'latitude': latitude,
      'longitude': longitude,
    };

    if (fotoBase64 != null) {
      body['foto'] = fotoBase64;
    }

    return await _makeRequest(
      Constants.presensiEndpoint,
      'POST',
      body: body,
    );
  }

  // Riwayat API
  static Future<Map<String, dynamic>> getRiwayat({
    required int userId,
    int? bulan,
    int? tahun,
  }) async {
    return await _makeRequest(
      Constants.riwayatEndpoint,
      'POST',
      body: {
        'user_id': userId,
        'bulan': bulan ?? DateTime.now().month,
        'tahun': tahun ?? DateTime.now().year,
      },
    );
  }

  // Helper method untuk test koneksi
  static Future<bool> testConnection() async {
    try {
      final response = await _makeRequest(
        Constants.dashboardEndpoint,
        'GET',
      );
      return response['status'] != 'error' || 
             response['message'] != Constants.msgNetworkError;
    } catch (e) {
      return false;
    }
  }
}
