# Disable directory browsing
Options -Indexes

# Allow only image files
<FilesMatch "\.(?i:gif|jpe?g|png)$">
    Order Allow,<PERSON>y
    Allow from all
</FilesMatch>

# Deny access to all other file types
<FilesMatch "^(?!.*\.(gif|jpe?g|png)$)">
    Order Allow,<PERSON>y
    <PERSON> from all
</FilesMatch>

# Protect against script execution
<FilesMatch "\.(?i:php|pl|py|cgi|asp|js)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Disable PHP execution in this directory
php_flag engine off
