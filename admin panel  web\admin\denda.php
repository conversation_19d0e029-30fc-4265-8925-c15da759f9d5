<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses update denda
if (isset($_POST['update'])) {
    $denda_masuk = clean($_POST['denda_masuk']);
    $denda_pulang = clean($_POST['denda_pulang']);
    $denda_tidak_absen = clean($_POST['denda_tidak_absen']);
    $denda_tidak_absen_pulang = clean($_POST['denda_tidak_absen_pulang']);

    // Cek apakah data denda sudah ada
    $query = "SELECT * FROM denda LIMIT 1";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        // Update data denda
        $query = "UPDATE denda SET denda_masuk = '$denda_masuk', denda_pulang = '$denda_pulang', denda_tidak_absen = '$denda_tidak_absen', denda_tidak_absen_pulang = '$denda_tidak_absen_pulang'";
    } else {
        // Insert data denda baru
        $query = "INSERT INTO denda (denda_masuk, denda_pulang, denda_tidak_absen, denda_tidak_absen_pulang) VALUES ('$denda_masuk', '$denda_pulang', '$denda_tidak_absen', '$denda_tidak_absen_pulang')";
    }

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Konfigurasi denda berhasil diperbarui!');
    } else {
        setMessage('danger', 'Gagal memperbarui konfigurasi denda!');
    }

    redirect('admin/denda.php');
}

// Ambil data denda
$denda = getDenda();

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Konfigurasi Denda</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Pengaturan Denda</h6>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="denda_masuk" class="form-label">Denda Keterlambatan Masuk</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" id="denda_masuk" name="denda_masuk" value="<?php echo $denda ? $denda['denda_masuk'] : 0; ?>" required>
                        </div>
                        <small class="text-muted">Denda untuk keterlambatan masuk kerja</small>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="denda_pulang" class="form-label">Denda Pulang Awal</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" id="denda_pulang" name="denda_pulang" value="<?php echo $denda ? $denda['denda_pulang'] : 0; ?>" required>
                        </div>
                        <small class="text-muted">Denda untuk pulang sebelum waktunya</small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="denda_tidak_absen" class="form-label">Denda Tidak Absen</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" id="denda_tidak_absen" name="denda_tidak_absen" value="<?php echo $denda ? $denda['denda_tidak_absen'] : 0; ?>" required>
                        </div>
                        <small class="text-muted">Denda untuk tidak melakukan absensi masuk dan pulang</small>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="denda_tidak_absen_pulang" class="form-label">Denda Tidak Absen Pulang</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" id="denda_tidak_absen_pulang" name="denda_tidak_absen_pulang" value="<?php echo $denda ? $denda['denda_tidak_absen_pulang'] : 0; ?>" required>
                        </div>
                        <small class="text-muted">Denda untuk absen masuk tetapi tidak absen pulang</small>
                    </div>
                </div>

                <div class="mt-3">
                    <button type="submit" name="update" class="btn btn-primary">Simpan Konfigurasi</button>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Informasi Denda</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5 class="alert-heading">Ketentuan Denda:</h5>
                <ul>
                    <li><strong>Denda Keterlambatan Masuk</strong> - Dikenakan jika karyawan melakukan absensi masuk setelah jam masuk yang ditentukan.</li>
                    <li><strong>Denda Pulang Awal</strong> - Dikenakan jika karyawan melakukan absensi pulang sebelum jam pulang yang ditentukan.</li>
                    <li><strong>Denda Tidak Absen</strong> - Dikenakan jika karyawan tidak melakukan absensi masuk dan pulang pada hari kerja.</li>
                    <li><strong>Denda Tidak Absen Pulang</strong> - Dikenakan jika karyawan melakukan absensi masuk tetapi tidak melakukan absensi pulang.</li>
                </ul>
                <hr>
                <p class="mb-0">Denda akan dihitung secara otomatis dan ditampilkan dalam laporan absensi karyawan.</p>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
