<?php
/**
 * API untuk mendapatkan riwayat presensi karyawan
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Ambil data dari request
$user_id = null;
$bulan = null;
$tahun = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);
    $user_id = isset($data['user_id']) ? clean($data['user_id']) : null;
    $bulan = isset($data['bulan']) ? clean($data['bulan']) : date('m');
    $tahun = isset($data['tahun']) ? clean($data['tahun']) : date('Y');
} else {
    $user_id = isset($_GET['user_id']) ? clean($_GET['user_id']) : null;
    $bulan = isset($_GET['bulan']) ? clean($_GET['bulan']) : date('m');
    $tahun = isset($_GET['tahun']) ? clean($_GET['tahun']) : date('Y');
}

// Validasi user_id
if (!$user_id) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'User ID harus diisi'
    ]);
    exit;
}

// Validasi bulan dan tahun
if (!is_numeric($bulan) || $bulan < 1 || $bulan > 12) {
    $bulan = date('m');
}
if (!is_numeric($tahun) || $tahun < 2020 || $tahun > 2030) {
    $tahun = date('Y');
}

// Ambil data karyawan
$query = "SELECT u.*, b.nama_bidang 
          FROM users u 
          LEFT JOIN bidang b ON u.bidang_id = b.id 
          WHERE u.id = '$user_id' AND u.role = 'karyawan'";
$result = mysqli_query($conn, $query);

if (!$result || mysqli_num_rows($result) == 0) {
    http_response_code(404);
    echo json_encode([
        'status' => 'error',
        'message' => 'Karyawan tidak ditemukan'
    ]);
    exit;
}

$karyawan = mysqli_fetch_assoc($result);

// Ambil data presensi
$query_presensi = "SELECT * FROM presensi 
                   WHERE user_id = '$user_id' 
                   AND MONTH(tanggal) = '$bulan' 
                   AND YEAR(tanggal) = '$tahun' 
                   ORDER BY tanggal DESC";
$result_presensi = mysqli_query($conn, $query_presensi);

$presensi = [];
if ($result_presensi) {
    while ($row = mysqli_fetch_assoc($result_presensi)) {
        $presensi[] = [
            'id' => $row['id'],
            'tanggal' => $row['tanggal'],
            'jam_masuk' => $row['jam_masuk'],
            'jam_pulang' => $row['jam_pulang'],
            'foto_masuk' => $row['foto_masuk'],
            'foto_pulang' => $row['foto_pulang'],
            'lokasi_masuk' => $row['lokasi_masuk'],
            'lokasi_pulang' => $row['lokasi_pulang'],
            'status' => $row['status'],
            'keterangan' => $row['keterangan']
        ];
    }
}

// Hitung statistik
$total_hadir = count($presensi);
$total_terlambat = 0;
$total_tepat_waktu = 0;

foreach ($presensi as $p) {
    if ($p['status'] === 'Terlambat') {
        $total_terlambat++;
    } elseif ($p['status'] === 'Tepat Waktu') {
        $total_tepat_waktu++;
    }
}

// Hitung total hari kerja dalam bulan
$total_hari = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
$hari_libur = 0;

// Hitung hari libur
$query_libur = "SELECT COUNT(*) as total_libur 
                FROM hari_libur 
                WHERE MONTH(tanggal) = '$bulan' 
                AND YEAR(tanggal) = '$tahun'";
$result_libur = mysqli_query($conn, $query_libur);
if ($result_libur) {
    $libur_data = mysqli_fetch_assoc($result_libur);
    $hari_libur = $libur_data['total_libur'];
}

// Hitung hari weekend (Sabtu dan Minggu)
$weekend_count = 0;
for ($day = 1; $day <= $total_hari; $day++) {
    $date = sprintf('%04d-%02d-%02d', $tahun, $bulan, $day);
    $day_of_week = date('w', strtotime($date));
    if ($day_of_week == 0 || $day_of_week == 6) { // Minggu = 0, Sabtu = 6
        $weekend_count++;
    }
}

$hari_kerja_efektif = $total_hari - $weekend_count - $hari_libur;
$total_alpha = $hari_kerja_efektif - $total_hadir;

// Response data
echo json_encode([
    'status' => 'success',
    'data' => [
        'karyawan' => [
            'nama' => $karyawan['nama'],
            'nik' => $karyawan['nik'],
            'bidang_nama' => $karyawan['nama_bidang']
        ],
        'periode' => [
            'bulan' => (int)$bulan,
            'tahun' => (int)$tahun,
            'nama_bulan' => date('F', mktime(0, 0, 0, $bulan, 1))
        ],
        'statistik' => [
            'total_hadir' => $total_hadir,
            'total_terlambat' => $total_terlambat,
            'total_tepat_waktu' => $total_tepat_waktu,
            'total_alpha' => $total_alpha,
            'hari_kerja_efektif' => $hari_kerja_efektif,
            'persentase_kehadiran' => $hari_kerja_efektif > 0 ? round(($total_hadir / $hari_kerja_efektif) * 100, 2) : 0
        ],
        'presensi' => $presensi
    ]
]);
?>
