<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Pastikan header belum dikirim
if (!headers_sent()) {
    // Tambahkan SweetAlert2 langsung di awal halaman
    echo '<!DOCTYPE html>
    <html>
    <head>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    </head>
    <body>
    <script>
        console.log("SweetAlert2 dimuat di awal halaman");
        if (typeof Swal !== "undefined") {
            console.log("SweetAlert2 tersedia di awal halaman");
        } else {
            console.error("SweetAlert2 tidak tersedia di awal halaman");
        }
    </script>';
}

// Cek akses
checkAccess('karyawan');

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Cek perizinan absensi
$query = "SELECT allow_barcode, allow_face FROM users WHERE id = '$user_id'";
$result = mysqli_query($conn, $query);
$permissions = mysqli_fetch_assoc($result);

$allow_barcode = isset($permissions['allow_barcode']) && $permissions['allow_barcode'] == 1;
$allow_face = isset($permissions['allow_face']) && $permissions['allow_face'] == 1;

// Ambil data lokasi karyawan
$lokasi = null;
if ($karyawan['lokasi_id']) {
    $lokasi = getLokasiById($karyawan['lokasi_id']);
}

// Ambil data jam kerja berdasarkan bidang karyawan dan hari ini
$jam_kerja = null;
$hari_names = ['', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
$hari_num = date('N'); // 1 (Senin) sampai 7 (Minggu)
$hari_name = $hari_names[$hari_num];

if ($karyawan['bidang_id']) {
    $query = "SELECT jk.*
              FROM jam_kerja_bidang jkb
              JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
              WHERE jkb.bidang_id = '{$karyawan['bidang_id']}'
              AND jkb.hari = '$hari_name'";
    $result = mysqli_query($conn, $query);
    if ($result && mysqli_num_rows($result) > 0) {
        $jam_kerja = mysqli_fetch_assoc($result);
    }
}

// Ambil data presensi hari ini
$today = date('Y-m-d');
$query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result = mysqli_query($conn, $query);
$presensi_hari_ini = mysqli_fetch_assoc($result);

// Cek apakah karyawan sedang dalam perjalanan dinas
$query = "SELECT * FROM izin_dinas
          WHERE user_id = '$user_id'
          AND status = 'Approved'
          AND tanggal_mulai <= '$today'
          AND tanggal_selesai >= '$today'";
$result = mysqli_query($conn, $query);
$izin_dinas_aktif = mysqli_fetch_assoc($result);

// Cek apakah hari ini hari libur
$query = "SELECT * FROM hari_libur WHERE tanggal = '$today'";
$result = mysqli_query($conn, $query);
$hari_libur = mysqli_fetch_assoc($result);

// Cek apakah hari ini adalah hari kerja berdasarkan bidang karyawan
$is_hari_kerja = false;
if ($karyawan['bidang_id']) {
    $query = "SELECT * FROM hari_kerja WHERE bidang_id = '{$karyawan['bidang_id']}' AND hari = '$hari_name'";
    $result = mysqli_query($conn, $query);
    if ($result && mysqli_num_rows($result) > 0) {
        $hari_kerja_data = mysqli_fetch_assoc($result);
        $is_hari_kerja = (bool)$hari_kerja_data['status'];
    }
}

$is_weekend = !$is_hari_kerja;

// Log semua data POST untuk debugging
error_log("POST Data: " . print_r($_POST, true));

// Proses absensi masuk
if (isset($_POST['absen_masuk'])) {
    // Cek apakah jam kerja sudah diatur
    if (!$jam_kerja) {
        setMessage('danger', 'Jam kerja belum diatur untuk bidang Anda. Silakan hubungi administrator.');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah sudah melewati jam akhir masuk
    $current_time = date('H:i:s');
    if ($current_time > $jam_kerja['akhir_jam_masuk']) {
        setMessage('danger', 'Anda tidak dapat melakukan absensi masuk karena sudah melewati jam akhir masuk (' . $jam_kerja['akhir_jam_masuk'] . ').');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah sudah melewati jam awal masuk
    if ($current_time < $jam_kerja['awal_jam_masuk']) {
        setMessage('warning', 'Anda belum dapat melakukan absensi masuk karena belum memasuki jam awal masuk (' . $jam_kerja['awal_jam_masuk'] . ').');
        redirect('karyawan/presensi.php');
    }

    // Ambil data lokasi dari POST
    $latitude = isset($_POST['latitude']) ? clean($_POST['latitude']) : null;
    $longitude = isset($_POST['longitude']) ? clean($_POST['longitude']) : null;
    $accuracy = isset($_POST['accuracy']) ? clean($_POST['accuracy']) : null;
    $distance = isset($_POST['distance']) ? clean($_POST['distance']) : null;

    // Ambil data foto dari POST
    $foto_data = isset($_POST['foto']) ? $_POST['foto'] : null;

    // Cek apakah lokasi tersedia
    if (!$latitude || !$longitude) {
        setMessage('danger', 'Data lokasi tidak tersedia. Pastikan GPS Anda aktif dan izin lokasi diberikan.');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah lokasi dalam radius yang ditentukan
    if ($lokasi && $distance > $lokasi['radius']) {
        setMessage('danger', 'Anda berada di luar radius lokasi absensi. Jarak Anda: ' . round($distance) . ' meter, Radius yang diizinkan: ' . $lokasi['radius'] . ' meter.');
        redirect('karyawan/presensi.php');
    }

    // Tentukan status berdasarkan waktu
    $status = 'Tepat Waktu';
    if ($current_time > $jam_kerja['jam_masuk']) {
        $status = 'Terlambat';
    }

    // Proses foto jika ada
    $foto_filename = null;
    if ($foto_data) {
        // Hapus header data URI
        $foto_data = str_replace('data:image/jpeg;base64,', '', $foto_data);
        $foto_data = str_replace(' ', '+', $foto_data);

        // Decode base64 ke binary
        $foto_binary = base64_decode($foto_data);

        // Buat nama file unik
        $foto_filename = 'selfie_masuk_' . $user_id . '_' . date('Ymd_His') . '.jpg';

        // Buat direktori uploads jika belum ada
        $upload_dir = '../uploads/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Simpan file
        $file_path = $upload_dir . $foto_filename;
        file_put_contents($file_path, $foto_binary);
    }

    // Buat lokasi masuk dalam format string
    $lokasi_masuk = "Lat: $latitude, Long: $longitude, Akurasi: $accuracy m";

    // Simpan data presensi
    $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, status, keterangan)
              VALUES ('$user_id', '$today', '$current_time', " .
              ($foto_filename ? "'$foto_filename'" : "NULL") . ", " .
              "'$lokasi_masuk', '$status', 'Absen via aplikasi')";

    // Coba eksekusi query
    $result = mysqli_query($conn, $query);

    if ($result) {
        // Redirect ke dashboard tanpa setMessage (hanya gunakan SweetAlert)
        redirect('karyawan/index.php?absen_status=success&type=masuk');
    } else {
        // Tampilkan error detail untuk debugging
        $error_message = mysqli_error($conn);
        error_log("Error absensi masuk: " . $error_message . " Query: " . $query);
        setMessage('danger', 'Gagal menyimpan absensi masuk: ' . $error_message . ' Query: ' . $query);
        redirect('karyawan/presensi.php');
    }
}

// Proses absensi pulang
if (isset($_POST['absen_pulang'])) {
    // Cek apakah sudah absen masuk
    if (empty($presensi_hari_ini)) {
        setMessage('danger', 'Anda belum melakukan absensi masuk!');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah jam kerja sudah diatur
    if (!$jam_kerja) {
        setMessage('danger', 'Jam kerja belum diatur untuk bidang Anda. Silakan hubungi administrator.');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah sudah melewati jam akhir pulang
    $current_time = date('H:i:s');
    if ($current_time > $jam_kerja['akhir_jam_pulang']) {
        setMessage('danger', 'Anda tidak dapat melakukan absensi pulang karena sudah melewati jam akhir pulang (' . $jam_kerja['akhir_jam_pulang'] . ').');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah sudah mencapai jam pulang
    if ($current_time < $jam_kerja['jam_pulang']) {
        setMessage('danger', 'Anda tidak dapat melakukan absensi pulang karena belum mencapai jam pulang (' . $jam_kerja['jam_pulang'] . ').');
        redirect('karyawan/presensi.php');
    }

    // Ambil data lokasi dari POST
    $latitude = isset($_POST['latitude']) ? clean($_POST['latitude']) : null;
    $longitude = isset($_POST['longitude']) ? clean($_POST['longitude']) : null;
    $accuracy = isset($_POST['accuracy']) ? clean($_POST['accuracy']) : null;
    $distance = isset($_POST['distance']) ? clean($_POST['distance']) : null;

    // Ambil data foto dari POST
    $foto_data = isset($_POST['foto']) ? $_POST['foto'] : null;

    // Cek apakah lokasi tersedia
    if (!$latitude || !$longitude) {
        setMessage('danger', 'Data lokasi tidak tersedia. Pastikan GPS Anda aktif dan izin lokasi diberikan.');
        redirect('karyawan/presensi.php');
    }

    // Cek apakah lokasi dalam radius yang ditentukan
    if ($lokasi && $distance > $lokasi['radius']) {
        setMessage('danger', 'Anda berada di luar radius lokasi absensi. Jarak Anda: ' . round($distance) . ' meter, Radius yang diizinkan: ' . $lokasi['radius'] . ' meter.');
        redirect('karyawan/presensi.php');
    }

    // Tentukan status berdasarkan waktu
    $status = $presensi_hari_ini['status']; // Pertahankan status sebelumnya
    // Catatan: Kita sudah memvalidasi bahwa waktu saat ini >= jam_pulang, jadi tidak perlu lagi status 'Pulang Awal'

    // Proses foto jika ada
    $foto_filename = null;
    if ($foto_data) {
        // Hapus header data URI
        $foto_data = str_replace('data:image/jpeg;base64,', '', $foto_data);
        $foto_data = str_replace(' ', '+', $foto_data);

        // Decode base64 ke binary
        $foto_binary = base64_decode($foto_data);

        // Buat nama file unik
        $foto_filename = 'selfie_pulang_' . $user_id . '_' . date('Ymd_His') . '.jpg';

        // Buat direktori uploads jika belum ada
        $upload_dir = '../uploads/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Simpan file
        $file_path = $upload_dir . $foto_filename;
        file_put_contents($file_path, $foto_binary);
    }

    // Buat lokasi pulang dalam format string
    $lokasi_pulang = "Lat: $latitude, Long: $longitude, Akurasi: $accuracy m";

    // Update data presensi
    $presensi_id = $presensi_hari_ini['id'];
    $query = "UPDATE presensi SET
              jam_pulang = '$current_time',
              status = '$status',
              lokasi_pulang = '$lokasi_pulang'" .
              ($foto_filename ? ", foto_pulang = '$foto_filename'" : "") .
              " WHERE id = '$presensi_id'";

    // Coba eksekusi query
    $result = mysqli_query($conn, $query);

    if ($result) {
        // Redirect ke dashboard tanpa setMessage (hanya gunakan SweetAlert)
        redirect('karyawan/index.php?absen_status=success&type=pulang');
    } else {
        // Tampilkan error detail untuk debugging
        $error_message = mysqli_error($conn);
        error_log("Error absensi pulang: " . $error_message . " Query: " . $query);
        setMessage('danger', 'Gagal menyimpan absensi pulang: ' . $error_message . ' Query: ' . $query);
        redirect('karyawan/presensi.php');
    }
}

// Include header
include_once '../includes/header.php';

// Variabel untuk menentukan tampilan mobile
$is_mobile_presensi = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'presensi.php';

// Cek apakah ada pesan untuk ditampilkan dengan SweetAlert
$message = getMessage();
?>

<style>
/* Floating Barcode Button */
.floating-barcode-button {
    position: fixed;
    bottom: 80px;
    left: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #28a745;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    z-index: 1000;
    text-decoration: none;
    font-size: 20px;
}

.floating-barcode-button:hover, .floating-barcode-button:focus {
    background-color: #218838;
    color: white;
    text-decoration: none;
}

/* Panduan Wajah untuk Absensi */
.face-guide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.face-guide {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70%;
    height: 70%;
    border: 2px dashed rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 0 2000px rgba(0, 0, 0, 0.3);
}

.face-guide::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    height: 90%;
    border: 2px solid rgba(76, 175, 80, 0.6);
    border-radius: 50%;
}

.face-guide-instructions {
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px;
    text-align: center;
    font-size: 14px;
    border-radius: 0 0 8px 8px;
}
</style>

<!-- Tambahkan script untuk memastikan SweetAlert2 berfungsi -->
<script>
    // Fungsi untuk memeriksa apakah SweetAlert2 sudah dimuat
    function checkSweetAlert() {
        console.log('Memeriksa SweetAlert2...');

        if (typeof Swal === 'undefined') {
            console.error('SweetAlert2 tidak ditemukan. Memuat dari CDN...');

            // Tambahkan SweetAlert2 secara langsung ke halaman
            document.write('<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"><\/script>');
            document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">');

            // Tunggu sebentar untuk memastikan script dimuat
            setTimeout(function() {
                console.log('Memeriksa SweetAlert2 setelah pemuatan...');
                if (typeof Swal !== 'undefined') {
                    console.log('SweetAlert2 berhasil dimuat.');
                    // Tampilkan SweetAlert untuk memastikan berfungsi
                    Swal.fire({
                        title: 'SweetAlert2 Berhasil Dimuat',
                        text: 'Sistem peringatan sudah siap digunakan',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    initSweetAlertMessages();
                } else {
                    console.error('SweetAlert2 masih tidak tersedia setelah pemuatan.');
                    alert('Gagal memuat SweetAlert2. Peringatan akan menggunakan alert biasa.');
                }
            }, 1000);
        } else {
            console.log('SweetAlert2 sudah dimuat.');
            // Tampilkan SweetAlert untuk memastikan berfungsi
            Swal.fire({
                title: 'SweetAlert2 Sudah Tersedia',
                text: 'Sistem peringatan sudah siap digunakan',
                icon: 'success',
                confirmButtonText: 'OK'
            });
            initSweetAlertMessages();
        }
    }

    // Fungsi untuk menampilkan pesan dengan SweetAlert2
    function initSweetAlertMessages() {
        <?php if ($message): ?>
        Swal.fire({
            icon: '<?php echo $message['type'] == 'danger' ? 'error' : ($message['type'] == 'warning' ? 'warning' : 'success'); ?>',
            title: '<?php echo $message['type'] == 'danger' ? 'Error' : ($message['type'] == 'warning' ? 'Perhatian' : 'Berhasil'); ?>',
            text: '<?php echo addslashes($message['text']); ?>',
            confirmButtonColor: '<?php echo $message['type'] == 'danger' ? '#d33' : ($message['type'] == 'warning' ? '#f6c23e' : '#1cc88a'); ?>'
        });
        <?php endif; ?>
    }

    // Fungsi untuk menampilkan SweetAlert2
    function showSweetAlert(title, text, icon, confirmButtonText) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: title,
                text: text,
                icon: icon,
                confirmButtonText: confirmButtonText
            });
            return true;
        } else {
            console.error('SweetAlert2 tidak tersedia. Menggunakan alert biasa.');
            alert(title + '\n\n' + text);
            return false;
        }
    }

    // Jalankan pemeriksaan saat dokumen dimuat
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Dokumen dimuat, memeriksa SweetAlert2...');

        // Tambahkan SweetAlert2 jika belum ada
        if (typeof Swal === 'undefined') {
            console.error('SweetAlert2 tidak ditemukan. Memuat dari CDN...');

            var sweetAlertCSS = document.createElement('link');
            sweetAlertCSS.rel = 'stylesheet';
            sweetAlertCSS.href = 'https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css';
            document.head.appendChild(sweetAlertCSS);

            var sweetAlertJS = document.createElement('script');
            sweetAlertJS.src = 'https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js';
            sweetAlertJS.onload = function() {
                console.log('SweetAlert2 berhasil dimuat.');
            };
            document.head.appendChild(sweetAlertJS);
        } else {
            console.log('SweetAlert2 sudah dimuat.');
        }
    });
</script>

<?php if ($is_mobile_presensi): ?>
<div class="mobile-presensi-container">
    <!-- Header Section -->
    <div class="presensi-header">
        <div class="user-info">
            <div class="user-avatar">
                                <?php if (!empty($karyawan['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $karyawan['foto_profil']; ?>" alt="Foto Profil" style="width: 100px; height: 100px; ">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                <div class="user-position"><?php echo $karyawan['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <div><i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?></div>
            <div><i class="fas fa-map-marker-alt"></i> <?php echo $lokasi ? $lokasi['nama_lokasi'] : 'Belum diatur'; ?></div>
        </div>
    </div>

    <!-- Clock Section -->
    <div class="clock-container">
        <div class="clock" id="jam-sekarang"><?php echo date('H:i:s'); ?></div>
        <div class="date"><?php echo date('d F Y'); ?></div>
    </div>

    <!-- Floating Barcode Button -->
    <a href="scan_barcode.php" class="floating-barcode-button">
        <i class="fas fa-qrcode"></i>
    </a>
<?php else: ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Presensi</h1>
        <div>
            <span class="badge bg-primary">Tanggal: <?php echo date('d-m-Y'); ?></span>
            <span class="badge bg-secondary">Jam: <span id="jam-sekarang"><?php echo date('H:i:s'); ?></span></span>
        </div>
    </div>
<?php endif; ?>

    <?php if ($izin_dinas_aktif): ?>
        <?php if ($is_mobile_presensi): ?>
            <div class="status-card">
                <div class="status-title"><i class="fas fa-plane"></i> Perjalanan Dinas</div>
                <div class="status-content">
                    <div class="status-badge info">Perjalanan Dinas</div>
                    <p>Anda sedang dalam perjalanan dinas ke <strong><?php echo $izin_dinas_aktif['tujuan']; ?></strong> sampai tanggal <?php echo date('d/m/Y', strtotime($izin_dinas_aktif['tanggal_selesai'])); ?>.</p>
                    <p>Presensi Anda akan diisi otomatis selama periode perjalanan dinas.</p>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <h5 class="alert-heading">Perjalanan Dinas!</h5>
                <p>Anda sedang dalam perjalanan dinas ke <strong><?php echo $izin_dinas_aktif['tujuan']; ?></strong> sampai tanggal <?php echo date('d/m/Y', strtotime($izin_dinas_aktif['tanggal_selesai'])); ?>.</p>
                <p>Presensi Anda akan diisi otomatis selama periode perjalanan dinas.</p>
            </div>
        <?php endif; ?>
    <?php elseif ($hari_libur): ?>
        <?php if ($is_mobile_presensi): ?>
            <div class="status-card">
                <div class="status-title"><i class="fas fa-calendar-times"></i> Status Hari Ini</div>
                <div class="status-content">
                    <div class="status-badge warning">Hari Libur</div>
                    <p>Hari ini adalah hari libur: <?php echo $hari_libur['nama_libur']; ?>. Tidak perlu melakukan absensi.</p>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-warning">
                <h5 class="alert-heading">Hari Libur!</h5>
                <p>Hari ini adalah hari libur: <?php echo $hari_libur['nama_libur']; ?>. Tidak perlu melakukan absensi.</p>
            </div>
        <?php endif; ?>
    <?php elseif ($is_weekend): ?>
        <?php if ($is_mobile_presensi): ?>
            <div class="status-card">
                <div class="status-title"><i class="fas fa-calendar-times"></i> Status Hari Ini</div>
                <div class="status-content">
                    <div class="status-badge warning">Bukan Hari Kerja</div>
                    <p>Hari ini (<?php echo $hari_name; ?>) bukan merupakan hari kerja untuk bidang Anda. Tidak perlu melakukan absensi.</p>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-warning">
                <h5 class="alert-heading">Bukan Hari Kerja!</h5>
                <p>Hari ini (<?php echo $hari_name; ?>) bukan merupakan hari kerja untuk bidang Anda. Tidak perlu melakukan absensi.</p>
            </div>
        <?php endif; ?>
    <?php else: ?>
        <?php if ($is_mobile_presensi): ?>
            <!-- Mobile Presensi View -->
            <div class="container px-3">


                <!-- Status Lokasi (Simpel) -->
                <div class="status-card">
                    <div class="status-title"><i class="fas fa-map-marker-alt"></i> Status Lokasi</div>
                    <div class="status-content">
                        <div id="location-status-simple">
                            <div class="location-indicator waiting">
                                <i class="fas fa-spinner fa-pulse"></i>
                                <span>Mendapatkan lokasi...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Camera Preview -->
                <?php if (empty($presensi_hari_ini)): ?>
                    <!-- Absen Masuk -->
                    <?php if ($allow_face): ?>
                    <div class="camera-preview" id="camera-preview-masuk">
                        <video id="video-masuk" autoplay playsinline style="display: none; width: 100%; height: 100%; object-fit: cover;"></video>
                        <canvas id="canvas-masuk" style="display: none; width: 100%; height: 100%;"></canvas>
                        <i class="fas fa-camera"></i>
                        <div class="camera-overlay"></div>
                        <div class="camera-message">Mengaktifkan kamera...</div>

                        <!-- Panduan Wajah -->
                        <div class="face-guide-overlay" id="face-guide-masuk">
                            <div class="face-guide"></div>
                            <div class="face-guide-instructions">
                                Posisikan wajah Anda di dalam lingkaran dan pastikan wajah terlihat jelas
                            </div>
                        </div>
                    </div>

                    <form method="post" action="" id="formAbsenMasuk">
                        <input type="hidden" name="latitude" id="latitude_masuk">
                        <input type="hidden" name="longitude" id="longitude_masuk">
                        <input type="hidden" name="accuracy" id="accuracy_masuk">
                        <input type="hidden" name="distance" id="distance_masuk">
                        <input type="hidden" name="foto" id="foto_masuk">
                        <input type="hidden" name="absen_masuk" value="1">
                        <button type="button" id="btn-absen-masuk" class="attendance-btn check-in" disabled title="Menunggu lokasi...">
                            <i class="fas fa-camera"></i>
                        </button>
                    </form>
                    <?php endif; ?>

                    <?php if ($allow_barcode): ?>
                    <div class="mt-3 text-center">
                        <a href="scan_barcode.php?type=masuk" class="btn btn-primary btn-lg">
                            <i class="fas fa-qrcode"></i> Absen Masuk dengan Barcode
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if (!$allow_face && !$allow_barcode): ?>
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i> Anda tidak memiliki izin untuk melakukan absensi. Silakan hubungi administrator.
                    </div>
                    <?php endif; ?>

                    <div class="info-section">
                        <div class="info-title"><i class="fas fa-info-circle"></i> Informasi Jam Kerja</div>
                        <div class="info-content">
                            <ul>
                                <li>Awal Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['awal_jam_masuk'] : '-'; ?></li>
                                <li>Batas Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['jam_masuk'] : '-'; ?></li>
                                <li>Akhir Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['akhir_jam_masuk'] : '-'; ?></li>
                            </ul>
                        </div>
                    </div>
                <?php elseif (empty($presensi_hari_ini['jam_pulang'])): ?>
                    <!-- Absen Pulang -->
                    <?php if ($allow_face): ?>
                    <div class="camera-preview" id="camera-preview-pulang">
                        <video id="video-pulang" autoplay playsinline style="display: none; width: 100%; height: 100%; object-fit: cover;"></video>
                        <canvas id="canvas-pulang" style="display: none; width: 100%; height: 100%;"></canvas>
                        <i class="fas fa-camera"></i>
                        <div class="camera-overlay"></div>
                        <div class="camera-message">Mengaktifkan kamera...</div>

                        <!-- Panduan Wajah -->
                        <div class="face-guide-overlay" id="face-guide-pulang">
                            <div class="face-guide"></div>
                            <div class="face-guide-instructions">
                                Posisikan wajah Anda di dalam lingkaran dan pastikan wajah terlihat jelas
                            </div>
                        </div>
                    </div>

                    <form method="post" action="" id="formAbsenPulang">
                        <input type="hidden" name="latitude" id="latitude_pulang">
                        <input type="hidden" name="longitude" id="longitude_pulang">
                        <input type="hidden" name="accuracy" id="accuracy_pulang">
                        <input type="hidden" name="distance" id="distance_pulang">
                        <input type="hidden" name="foto" id="foto_pulang">
                        <input type="hidden" name="absen_pulang" value="1">
                        <button type="button" id="btn-absen-pulang" class="attendance-btn check-out" disabled title="Menunggu lokasi...">
                            <i class="fas fa-camera"></i>
                        </button>
                    </form>
                    <?php endif; ?>

                    <?php if ($allow_barcode): ?>
                    <div class="mt-3 text-center">
                        <a href="scan_barcode.php?type=pulang" class="btn btn-danger btn-lg">
                            <i class="fas fa-qrcode"></i> Absen Pulang dengan Barcode
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if (!$allow_face && !$allow_barcode): ?>
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i> Anda tidak memiliki izin untuk melakukan absensi. Silakan hubungi administrator.
                    </div>
                    <?php endif; ?>

                    <div class="info-section">
                        <div class="info-title"><i class="fas fa-info-circle"></i> Informasi Jam Kerja</div>
                        <div class="info-content">
                            <ul>
                                <li>Jam Pulang: <?php echo $jam_kerja ? $jam_kerja['jam_pulang'] : '-'; ?></li>
                                <li>Akhir Jam Pulang: <?php echo $jam_kerja ? $jam_kerja['akhir_jam_pulang'] : '-'; ?></li>
                            </ul>
                        </div>
                    </div>


                <?php else: ?>
                    <!-- Absensi Lengkap -->
                    <div class="info-section">
                        <div class="info-title"><i class="fas fa-check-circle"></i> Absensi Hari Ini Selesai</div>
                        <div class="info-content">
                            <p>Anda telah menyelesaikan absensi hari ini. Terima kasih atas kehadiran Anda.</p>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Lokasi Info -->
                <div class="info-section">
                    <div class="info-title"><i class="fas fa-map-marker-alt"></i> Informasi Lokasi</div>
                    <div class="info-content">
                        <?php if ($lokasi): ?>
                            <p>Nama Lokasi: <?php echo $lokasi['nama_lokasi']; ?></p>
                            <p>Koordinat: <?php echo $lokasi['latitude']; ?>, <?php echo $lokasi['longitude']; ?></p>
                            <p>Radius: <?php echo $lokasi['radius']; ?> meter</p>
                            <p class="text-warning">Pastikan Anda berada dalam radius lokasi yang ditentukan saat melakukan absensi.</p>
                        <?php else: ?>
                            <p class="text-danger">Lokasi absensi Anda belum diatur. Silakan hubungi administrator.</p>
                        <?php endif; ?>
                    </div>
                </div>


        <?php else: ?>
            <!-- Desktop Presensi View (Part 1) -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold">Absensi Masuk</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($presensi_hari_ini)): ?>
                                <div class="text-center mb-4">
                                    <?php if ($allow_face): ?>
                                    <div id="camera-masuk-desktop" class="mb-3 camera-preview-desktop">
                                        <video id="video-masuk-desktop" autoplay playsinline style="display: none; width: 100%; height: 100%; object-fit: cover;"></video>
                                        <canvas id="canvas-masuk-desktop" style="display: none; width: 100%; height: 100%;"></canvas>
                                        <i class="fas fa-camera"></i>
                                        <div class="camera-overlay"></div>
                                        <div class="camera-message">Mengaktifkan kamera...</div>

                                        <!-- Panduan Wajah -->
                                        <div class="face-guide-overlay" id="face-guide-masuk-desktop">
                                            <div class="face-guide"></div>
                                            <div class="face-guide-instructions">
                                                Posisikan wajah Anda di dalam lingkaran dan pastikan wajah terlihat jelas
                                            </div>
                                        </div>
                                    </div>

                                    <form method="post" action="" id="formAbsenMasukDesktop">
                                        <input type="hidden" name="latitude" id="latitude_masuk_desktop">
                                        <input type="hidden" name="longitude" id="longitude_masuk_desktop">
                                        <input type="hidden" name="accuracy" id="accuracy_masuk_desktop">
                                        <input type="hidden" name="distance" id="distance_masuk_desktop">
                                        <input type="hidden" name="foto" id="foto_masuk_desktop">
                                        <input type="hidden" name="absen_masuk" value="1">
                                        <button type="button" id="btn-absen-masuk-desktop" class="btn btn-primary btn-lg" disabled title="Menunggu lokasi...">
                                            <i class="fas fa-camera"></i> Absen Masuk
                                        </button>
                                    </form>
                                    <?php endif; ?>

                                    <?php if ($allow_barcode): ?>
                                    <div class="mt-3">
                                        <a href="scan_barcode.php?type=masuk" class="btn btn-primary btn-lg">
                                            <i class="fas fa-qrcode"></i> Absen Masuk dengan Barcode
                                        </a>
                                    </div>
                                    <?php endif; ?>

                                    <?php if (!$allow_face && !$allow_barcode): ?>
                                    <div class="alert alert-warning mt-3">
                                        <i class="fas fa-exclamation-triangle"></i> Anda tidak memiliki izin untuk melakukan absensi. Silakan hubungi administrator.
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="alert alert-primary">
                                    <h6 class="alert-heading">Informasi Jam Kerja:</h6>
                                    <ul>
                                        <li>Awal Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['awal_jam_masuk'] : '-'; ?></li>
                                        <li>Batas Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['jam_masuk'] : '-'; ?></li>
                                        <li>Akhir Jam Masuk: <?php echo $jam_kerja ? $jam_kerja['akhir_jam_masuk'] : '-'; ?></li>
                                    </ul>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">Anda sudah melakukan absensi masuk!</h6>
                                    <p>Jam Masuk: <?php echo $presensi_hari_ini['jam_masuk']; ?></p>
                                </div>

                                <?php if (!empty($presensi_hari_ini['foto_masuk'])): ?>
                                    <div class="text-center">
                                        <img src="<?php echo BASE_URL . 'uploads/' . $presensi_hari_ini['foto_masuk']; ?>" class="img-thumbnail" style="max-height: 200px;">
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold">Absensi Pulang</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($presensi_hari_ini) || empty($presensi_hari_ini['jam_pulang'])): ?>
                                <?php if (empty($presensi_hari_ini)): ?>
                                    <div class="alert alert-warning">
                                        <h6 class="alert-heading">Anda belum melakukan absensi masuk!</h6>
                                        <p>Silakan lakukan absensi masuk terlebih dahulu.</p>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center mb-4">
                                        <?php if ($allow_face): ?>
                                        <div id="camera-pulang-desktop" class="mb-3 camera-preview-desktop">
                                            <video id="video-pulang-desktop" autoplay playsinline style="display: none; width: 100%; height: 100%; object-fit: cover;"></video>
                                            <canvas id="canvas-pulang-desktop" style="display: none; width: 100%; height: 100%;"></canvas>
                                            <i class="fas fa-camera"></i>
                                            <div class="camera-overlay"></div>
                                            <div class="camera-message">Mengaktifkan kamera...</div>

                                            <!-- Panduan Wajah -->
                                            <div class="face-guide-overlay" id="face-guide-pulang-desktop">
                                                <div class="face-guide"></div>
                                                <div class="face-guide-instructions">
                                                    Posisikan wajah Anda di dalam lingkaran dan pastikan wajah terlihat jelas
                                                </div>
                                            </div>
                                        </div>

                                        <form method="post" action="" id="formAbsenPulangDesktop">
                                            <input type="hidden" name="latitude" id="latitude_pulang_desktop">
                                            <input type="hidden" name="longitude" id="longitude_pulang_desktop">
                                            <input type="hidden" name="accuracy" id="accuracy_pulang_desktop">
                                            <input type="hidden" name="distance" id="distance_pulang_desktop">
                                            <input type="hidden" name="foto" id="foto_pulang_desktop">
                                            <input type="hidden" name="absen_pulang" value="1">
                                            <button type="button" id="btn-absen-pulang-desktop" class="btn btn-primary btn-lg" disabled title="Menunggu lokasi...">
                                                <i class="fas fa-camera"></i> Absen Pulang
                                            </button>
                                        </form>
                                        <?php endif; ?>

                                        <?php if ($allow_barcode): ?>
                                        <div class="mt-3">
                                            <a href="scan_barcode.php?type=pulang" class="btn btn-danger btn-lg">
                                                <i class="fas fa-qrcode"></i> Absen Pulang dengan Barcode
                                            </a>
                                        </div>
                                        <?php endif; ?>

                                        <?php if (!$allow_face && !$allow_barcode): ?>
                                        <div class="alert alert-warning mt-3">
                                            <i class="fas fa-exclamation-triangle"></i> Anda tidak memiliki izin untuk melakukan absensi. Silakan hubungi administrator.
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="alert alert-primary">
                                        <h6 class="alert-heading">Informasi Jam Kerja:</h6>
                                        <ul>
                                            <li>Jam Pulang: <?php echo $jam_kerja ? $jam_kerja['jam_pulang'] : '-'; ?></li>
                                            <li>Akhir Jam Pulang: <?php echo $jam_kerja ? $jam_kerja['akhir_jam_pulang'] : '-'; ?></li>
                                        </ul>
                                    </div>


                                <?php endif; ?>
                            <?php else: ?>
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">Anda sudah melakukan absensi pulang!</h6>
                                    <p>Jam Pulang: <?php echo $presensi_hari_ini['jam_pulang']; ?></p>
                                </div>

                                <?php if (!empty($presensi_hari_ini['foto_pulang'])): ?>
                                    <div class="text-center">
                                        <img src="<?php echo BASE_URL . 'uploads/' . $presensi_hari_ini['foto_pulang']; ?>" class="img-thumbnail" style="max-height: 200px;">
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informasi Lokasi</h6>
                </div>
                <div class="card-body">
                    <?php if ($lokasi): ?>
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Lokasi Absensi Anda:</h6>
                            <p>Nama Lokasi: <?php echo $lokasi['nama_lokasi']; ?></p>
                            <p>Koordinat: <?php echo $lokasi['latitude']; ?>, <?php echo $lokasi['longitude']; ?></p>
                            <p>Radius: <?php echo $lokasi['radius']; ?> meter</p>
                        </div>

                        <div id="location-status-desktop">
                            <div class="location-indicator waiting">
                                <i class="fas fa-spinner fa-pulse"></i>
                                <span>Mendapatkan lokasi...</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <h6 class="alert-heading">Lokasi Belum Diatur!</h6>
                            <p>Lokasi absensi Anda belum diatur. Silakan hubungi administrator.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<script>
    // Update jam secara real-time
    function updateJam() {
        var now = new Date();
        var hours = now.getHours().toString().padStart(2, '0');
        var minutes = now.getMinutes().toString().padStart(2, '0');
        var seconds = now.getSeconds().toString().padStart(2, '0');

        // Update semua elemen dengan id jam-sekarang
        var elements = document.querySelectorAll('#jam-sekarang');
        elements.forEach(function(element) {
            element.textContent = hours + ':' + minutes + ':' + seconds;
        });
    }

    // Update jam setiap detik
    setInterval(updateJam, 1000);
    updateJam();

    // Variabel untuk menyimpan data lokasi
    var userLocation = {
        latitude: null,
        longitude: null,
        accuracy: null,
        timestamp: null,
        distance: null,
        inRadius: false
    };

    // Lokasi kantor dari database
    <?php if ($lokasi): ?>
    var officeLocation = {
        latitude: <?php echo $lokasi['latitude']; ?>,
        longitude: <?php echo $lokasi['longitude']; ?>,
        radius: <?php echo $lokasi['radius']; ?>
    };
    <?php else: ?>
    var officeLocation = {
        latitude: 0,
        longitude: 0,
        radius: 0
    };
    <?php endif; ?>

    // Fungsi untuk menghitung jarak antara dua titik koordinat (Haversine formula)
    function calculateDistance(lat1, lon1, lat2, lon2) {
        var R = 6371000; // Radius bumi dalam meter
        var dLat = toRad(lat2 - lat1);
        var dLon = toRad(lon2 - lon1);
        var a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2);
        var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        var d = R * c;
        return d; // Jarak dalam meter
    }

    function toRad(value) {
        return value * Math.PI / 180;
    }

    // Fungsi untuk mendapatkan lokasi pengguna
    function getUserLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    // Simpan data lokasi
                    userLocation.latitude = position.coords.latitude;
                    userLocation.longitude = position.coords.longitude;
                    userLocation.accuracy = position.coords.accuracy;
                    userLocation.timestamp = position.timestamp;

                    // Hitung jarak ke kantor
                    userLocation.distance = calculateDistance(
                        userLocation.latitude,
                        userLocation.longitude,
                        officeLocation.latitude,
                        officeLocation.longitude
                    );

                    // Cek apakah dalam radius
                    userLocation.inRadius = userLocation.distance <= officeLocation.radius;

                    // Update tampilan lokasi
                    updateLocationDisplay();

                    // Update posisi pada peta jika modal terbuka
                    if (isMapModalOpen) {
                        updateMapPosition();
                    }
                },
                function(error) {
                    console.error("Error getting location:", error);
                    var errorMessage = "";
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage = "Akses lokasi ditolak. Mohon izinkan akses lokasi untuk melakukan absensi.";
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage = "Informasi lokasi tidak tersedia.";
                            break;
                        case error.TIMEOUT:
                            errorMessage = "Waktu permintaan lokasi habis.";
                            break;
                        case error.UNKNOWN_ERROR:
                            errorMessage = "Terjadi kesalahan yang tidak diketahui.";
                            break;
                    }

                    // Tampilkan pesan error
                    var locationStatusElement = document.getElementById('location-status');
                    if (locationStatusElement) {
                        locationStatusElement.innerHTML = '<div class="alert alert-danger">' + errorMessage + '</div>';
                    }
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
        } else {
            console.error("Geolocation is not supported by this browser.");
            var locationStatusElement = document.getElementById('location-status');
            if (locationStatusElement) {
                locationStatusElement.innerHTML = '<div class="alert alert-danger">Browser Anda tidak mendukung geolokasi.</div>';
            }
        }
    }

    // Fungsi untuk memperbarui tampilan lokasi
    function updateLocationDisplay() {
        var locationStatusSimple = document.getElementById('location-status-simple');
        var locationStatusDesktop = document.getElementById('location-status-desktop');

        // Update nilai input hidden pada form
        if (userLocation.latitude && userLocation.longitude) {
            // Update nilai input hidden pada form mobile
            document.getElementById('latitude_masuk')?.setAttribute('value', userLocation.latitude);
            document.getElementById('longitude_masuk')?.setAttribute('value', userLocation.longitude);
            document.getElementById('accuracy_masuk')?.setAttribute('value', userLocation.accuracy);
            document.getElementById('distance_masuk')?.setAttribute('value', userLocation.distance);

            document.getElementById('latitude_pulang')?.setAttribute('value', userLocation.latitude);
            document.getElementById('longitude_pulang')?.setAttribute('value', userLocation.longitude);
            document.getElementById('accuracy_pulang')?.setAttribute('value', userLocation.accuracy);
            document.getElementById('distance_pulang')?.setAttribute('value', userLocation.distance);

            // Update nilai input hidden pada form desktop
            document.getElementById('latitude_masuk_desktop')?.setAttribute('value', userLocation.latitude);
            document.getElementById('longitude_masuk_desktop')?.setAttribute('value', userLocation.longitude);
            document.getElementById('accuracy_masuk_desktop')?.setAttribute('value', userLocation.accuracy);
            document.getElementById('distance_masuk_desktop')?.setAttribute('value', userLocation.distance);

            document.getElementById('latitude_pulang_desktop')?.setAttribute('value', userLocation.latitude);
            document.getElementById('longitude_pulang_desktop')?.setAttribute('value', userLocation.longitude);
            document.getElementById('accuracy_pulang_desktop')?.setAttribute('value', userLocation.accuracy);
            document.getElementById('distance_pulang_desktop')?.setAttribute('value', userLocation.distance);

            // Fungsi untuk memeriksa waktu absensi
            function checkAbsensiTime(type) {
                var now = new Date();
                var hours = now.getHours().toString().padStart(2, '0');
                var minutes = now.getMinutes().toString().padStart(2, '0');
                var seconds = now.getSeconds().toString().padStart(2, '0');
                var currentTime = hours + ':' + minutes + ':' + seconds;

                <?php if ($jam_kerja): ?>
                if (type === 'masuk') {
                    var awalJamMasuk = '<?php echo $jam_kerja['awal_jam_masuk']; ?>';
                    var jamMasuk = '<?php echo $jam_kerja['jam_masuk']; ?>';
                    var akhirJamMasuk = '<?php echo $jam_kerja['akhir_jam_masuk']; ?>';

                    if (currentTime < awalJamMasuk) {
                        return {
                            valid: false,
                            message: 'Anda belum dapat melakukan absensi masuk karena belum memasuki jam awal masuk (' + awalJamMasuk + ').'
                        };
                    } else if (currentTime > akhirJamMasuk) {
                        return {
                            valid: false,
                            message: 'Anda tidak dapat melakukan absensi masuk karena sudah melewati jam akhir masuk (' + akhirJamMasuk + ').'
                        };
                    } else if (currentTime > jamMasuk) {
                        // Jika terlambat, tampilkan peringatan tapi tetap izinkan
                        return {
                            valid: true,
                            warning: true,
                            message: 'Anda melakukan absensi masuk setelah batas jam masuk (' + jamMasuk + '). Status Anda akan tercatat sebagai Terlambat.'
                        };
                    }
                } else if (type === 'pulang') {
                    var jamPulang = '<?php echo $jam_kerja['jam_pulang']; ?>';
                    var akhirJamPulang = '<?php echo $jam_kerja['akhir_jam_pulang']; ?>';

                    if (currentTime > akhirJamPulang) {
                        return {
                            valid: false,
                            message: 'Anda tidak dapat melakukan absensi pulang karena sudah melewati jam akhir pulang (' + akhirJamPulang + ').'
                        };
                    } else if (currentTime < jamPulang) {
                        // Jika belum jam pulang, tidak izinkan absensi pulang
                        return {
                            valid: false,
                            message: 'Anda tidak dapat melakukan absensi pulang karena belum mencapai jam pulang (' + jamPulang + ').'
                        };
                    }
                }
                <?php endif; ?>

                return { valid: true };
            }

            // Fungsi untuk mengatur tombol absensi
            function setupButton(btnId, formId, type) {
                var btn = document.getElementById(btnId);
                if (!btn) return;

                // Hapus event listener yang ada
                var newBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newBtn, btn);
                btn = newBtn;

                // Cek apakah karyawan sedang dalam perjalanan dinas
                <?php if ($izin_dinas_aktif): ?>
                    btn.disabled = true;
                    btn.title = "Anda sedang dalam perjalanan dinas. Presensi diisi otomatis.";

                    // Tambahkan event listener untuk menampilkan pesan
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        // Tampilkan pesan menggunakan SweetAlert
                        if (typeof Swal !== 'undefined') {
                            Swal.fire({
                                icon: 'info',
                                title: 'Perjalanan Dinas Aktif',
                                text: 'Anda sedang dalam perjalanan dinas ke <?php echo $izin_dinas_aktif['tujuan']; ?>. Presensi Anda akan diisi otomatis selama periode perjalanan dinas.',
                                confirmButtonColor: '#3085d6',
                                confirmButtonText: 'Saya Mengerti'
                            });
                        } else {
                            alert('Anda sedang dalam perjalanan dinas. Presensi diisi otomatis.');
                        }
                    });
                    return;
                <?php endif; ?>

                if (userLocation.inRadius) {
                    btn.disabled = false;
                    btn.title = "Anda dapat melakukan absensi";

                    // Tambahkan event listener untuk langsung submit
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();

                        // Periksa waktu absensi
                        var timeCheck = checkAbsensiTime(type);
                        if (!timeCheck.valid) {
                            // Tampilkan peringatan waktu
                            showTimeAlert(timeCheck.message);
                            return;
                        }

                        // Langsung tangkap gambar dan submit form
                        captureImage(type);
                        console.log('Submitting form: ' + formId);
                        var form = document.getElementById(formId);
                        console.log('Form data:', {
                            latitude: form.querySelector('[name="latitude"]').value,
                            longitude: form.querySelector('[name="longitude"]').value,
                            accuracy: form.querySelector('[name="accuracy"]').value,
                            distance: form.querySelector('[name="distance"]').value,
                            foto: form.querySelector('[name="foto"]').value ? 'Data foto tersedia' : 'Tidak ada foto'
                        });
                        form.submit();
                    });
                } else {
                    btn.disabled = false; // Tetap aktifkan tombol untuk menampilkan peringatan
                    btn.title = "Anda harus berada dalam radius lokasi untuk melakukan absensi";

                    // Tambahkan event listener untuk peringatan
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        showLocationAlert(type);
                    });
                }
            }

            // Setup tombol absensi
            setupButton('btn-absen-masuk', 'formAbsenMasuk', 'masuk');
            setupButton('btn-absen-pulang', 'formAbsenPulang', 'pulang');
            setupButton('btn-absen-masuk-desktop', 'formAbsenMasukDesktop', 'masuk');
            setupButton('btn-absen-pulang-desktop', 'formAbsenPulangDesktop', 'pulang');



            // Update tampilan status lokasi simpel
            var statusClass = userLocation.inRadius ? 'in-radius' : 'out-radius';
            var statusIcon = userLocation.inRadius ? 'fa-check-circle' : 'fa-times-circle';
            var statusText = userLocation.inRadius ? 'Lokasi Tepat' : 'Di Luar Radius';



            // Update untuk mobile
            if (locationStatusSimple) {
                locationStatusSimple.innerHTML = '<div class="location-indicator ' + statusClass + '">' +
                    '<i class="fas ' + statusIcon + '"></i>' +
                    '<span>' + statusText + ' (' + Math.round(userLocation.distance) + 'm)</span>' +
                    '</div>';
            }

            // Update untuk desktop
            if (locationStatusDesktop) {
                locationStatusDesktop.innerHTML = '<div class="location-indicator ' + statusClass + '">' +
                    '<i class="fas ' + statusIcon + '"></i>' +
                    '<span>' + statusText + ' (' + Math.round(userLocation.distance) + 'm)</span>' +
                    '</div>';
            }
        }
    }

    // Variabel untuk menyimpan stream kamera
    var videoStreamMasuk = null;
    var videoStreamPulang = null;

    // Fungsi untuk mengaktifkan kamera
    function startCamera(type) {
        // Tentukan elemen berdasarkan tipe (masuk atau pulang)
        var videoElement = document.getElementById('video-' + type);
        var cameraPreview = document.getElementById('camera-preview-' + type);
        var cameraMessage = cameraPreview.querySelector('.camera-message');
        var iconElement = cameraPreview.querySelector('i.fas');

        if (!videoElement) return;

        // Cek apakah browser mendukung getUserMedia
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            // Tampilkan pesan sedang mengaktifkan kamera
            cameraMessage.textContent = 'Mengaktifkan kamera...';

            // Minta izin akses kamera
            navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'user',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            })
            .then(function(stream) {
                // Simpan stream untuk digunakan nanti
                if (type === 'masuk') {
                    videoStreamMasuk = stream;
                } else {
                    videoStreamPulang = stream;
                }

                // Tampilkan video dan sembunyikan ikon kamera
                videoElement.style.display = 'block';
                iconElement.style.display = 'none';

                // Hubungkan stream ke elemen video
                videoElement.srcObject = stream;

                // Perbarui pesan
                cameraMessage.textContent = 'Kamera aktif. Posisikan wajah Anda di tengah frame.';

                // Tambahkan event listener untuk menangkap gambar saat tombol absen ditekan
                var btnAbsen = document.getElementById('btn-absen-' + type);
                if (btnAbsen) {
                    // Event listener sudah ditambahkan di setupButton
                }
            })
            .catch(function(error) {
                console.error('Error accessing camera:', error);
                cameraMessage.textContent = 'Gagal mengakses kamera: ' + error.message;
            });
        } else {
            cameraMessage.textContent = 'Browser Anda tidak mendukung akses kamera.';
        }
    }

    // Fungsi untuk menangkap gambar dari kamera
    function captureImage(type) {
        var videoElement = document.getElementById('video-' + type);
        var canvasElement = document.getElementById('canvas-' + type);
        var fotoInput = document.getElementById('foto_' + type);

        if (!videoElement || !canvasElement || !fotoInput) return null;

        // Sesuaikan ukuran canvas dengan video
        canvasElement.width = videoElement.videoWidth;
        canvasElement.height = videoElement.videoHeight;

        // Gambar frame video ke canvas
        var context = canvasElement.getContext('2d');

        // Flip gambar secara horizontal untuk mengembalikan efek cermin
        context.translate(canvasElement.width, 0);
        context.scale(-1, 1);
        context.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);

        // Kembalikan transformasi ke normal
        context.setTransform(1, 0, 0, 1, 0, 0);

        // Konversi canvas ke data URL (base64)
        var dataURL = canvasElement.toDataURL('image/jpeg', 0.8);

        // Simpan data URL ke input hidden
        fotoInput.value = dataURL;

        return dataURL;
    }

    // Fungsi untuk menghentikan stream kamera
    function stopCamera(type) {
        var stream = (type === 'masuk') ? videoStreamMasuk : videoStreamPulang;
        var videoElement = document.getElementById('video-' + type);

        if (stream) {
            stream.getTracks().forEach(function(track) {
                track.stop();
            });

            if (videoElement) {
                videoElement.srcObject = null;
            }

            if (type === 'masuk') {
                videoStreamMasuk = null;
            } else {
                videoStreamPulang = null;
            }
        }
    }

    // Mulai deteksi lokasi saat halaman dimuat
    getUserLocation();

    // Set interval untuk memperbarui lokasi setiap 5 detik
    setInterval(getUserLocation, 5000);

    // Aktifkan kamera saat halaman dimuat
    <?php if (empty($presensi_hari_ini) && $allow_face): ?>
    // Aktifkan kamera untuk absen masuk
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            startCamera('masuk');
            startCamera('masuk-desktop');
        }, 1000); // Tunggu 1 detik untuk memastikan elemen sudah dimuat
    });
    <?php elseif (empty($presensi_hari_ini['jam_pulang']) && $allow_face): ?>
    // Aktifkan kamera untuk absen pulang
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            startCamera('pulang');
            startCamera('pulang-desktop');
        }, 1000); // Tunggu 1 detik untuk memastikan elemen sudah dimuat
    });
    <?php endif; ?>

    // Variabel untuk menyimpan objek peta Leaflet
    var leafletMap = null;
    var userMarker = null;
    var officeMarker = null;
    var radiusCircle = null;
    var mapLine = null;
    var isMapModalOpen = false;

    // Fungsi untuk menampilkan peta Leaflet
    function showLeafletMap() {
        if (!userLocation.latitude || !userLocation.longitude) {
            // Periksa apakah SweetAlert2 tersedia
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'warning',
                    title: 'Lokasi Belum Tersedia',
                    text: 'Mohon tunggu sementara kami mendapatkan lokasi Anda.',
                    confirmButtonColor: '#3085d6'
                });
            } else {
                alert('Lokasi Anda belum tersedia. Mohon tunggu beberapa saat.');
            }
            return;
        }

        // Tampilkan modal
        document.getElementById('map-modal').style.display = 'block';
        isMapModalOpen = true;

        // Inisialisasi peta jika belum ada
        if (!leafletMap) {
            // Buat peta
            leafletMap = L.map('leaflet-map').setView([userLocation.latitude, userLocation.longitude], 15);

            // Tambahkan layer peta
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(leafletMap);

            // Tambahkan marker untuk lokasi kantor
            var officeIcon = L.icon({
                iconUrl: '<?php echo BASE_URL; ?>assets/img/office-marker.png',
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32]
            });

            officeMarker = L.marker([officeLocation.latitude, officeLocation.longitude], {
                icon: officeIcon
            }).addTo(leafletMap);
            officeMarker.bindPopup('<b>Lokasi Kantor</b><br>' + officeLocation.latitude + ', ' + officeLocation.longitude);

            // Tambahkan lingkaran radius
            radiusCircle = L.circle([officeLocation.latitude, officeLocation.longitude], {
                radius: officeLocation.radius,
                color: '#4e73df',
                fillColor: '#4e73df',
                fillOpacity: 0.2
            }).addTo(leafletMap);

            // Tambahkan marker untuk lokasi pengguna
            var userIcon = L.icon({
                iconUrl: '<?php echo BASE_URL; ?>assets/img/user-marker.png',
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32]
            });

            userMarker = L.marker([userLocation.latitude, userLocation.longitude], {
                icon: userIcon
            }).addTo(leafletMap);
            userMarker.bindPopup('<b>Lokasi Anda</b><br>' + userLocation.latitude + ', ' + userLocation.longitude);

            // Tambahkan garis antara lokasi pengguna dan kantor
            mapLine = L.polyline([
                [userLocation.latitude, userLocation.longitude],
                [officeLocation.latitude, officeLocation.longitude]
            ], {
                color: '#36b9cc',
                weight: 3,
                opacity: 0.7,
                dashArray: '10, 10'
            }).addTo(leafletMap);

            // Tambahkan informasi jarak
            var distanceInfo = L.control({position: 'bottomleft'});
            distanceInfo.onAdd = function(map) {
                this._div = L.DomUtil.create('div', 'distance-info');
                this.update();
                return this._div;
            };

            distanceInfo.update = function() {
                var inRadius = userLocation.inRadius ? 'Dalam Radius' : 'Di Luar Radius';
                var statusClass = userLocation.inRadius ? 'in-radius' : 'out-radius';
                this._div.innerHTML = '<div class="distance-box ' + statusClass + '">' +
                    '<strong>Jarak:</strong> ' + Math.round(userLocation.distance) + ' meter<br>' +
                    '<strong>Status:</strong> ' + inRadius + '<br>' +
                    '<strong>Akurasi:</strong> ' + Math.round(userLocation.accuracy) + ' meter' +
                    '</div>';
            };

            distanceInfo.addTo(leafletMap);
            window.distanceInfo = distanceInfo;

            // Sesuaikan tampilan agar semua marker terlihat
            var bounds = L.latLngBounds([
                [userLocation.latitude, userLocation.longitude],
                [officeLocation.latitude, officeLocation.longitude]
            ]);
            leafletMap.fitBounds(bounds.pad(0.3));
        } else {
            // Sesuaikan tampilan
            leafletMap.invalidateSize();
            updateMapPosition();
        }
    }

    // Fungsi untuk memperbarui posisi pada peta
    function updateMapPosition() {
        if (!leafletMap || !userMarker || !isMapModalOpen) return;

        // Update posisi marker pengguna
        userMarker.setLatLng([userLocation.latitude, userLocation.longitude]);
        userMarker.getPopup().setContent('<b>Lokasi Anda</b><br>' + userLocation.latitude + ', ' + userLocation.longitude);

        // Update garis
        if (mapLine) {
            leafletMap.removeLayer(mapLine);
        }
        mapLine = L.polyline([
            [userLocation.latitude, userLocation.longitude],
            [officeLocation.latitude, officeLocation.longitude]
        ], {
            color: '#36b9cc',
            weight: 3,
            opacity: 0.7,
            dashArray: '10, 10'
        }).addTo(leafletMap);

        // Update informasi jarak
        if (window.distanceInfo) {
            window.distanceInfo.update();
        }

        // Sesuaikan tampilan agar semua marker terlihat
        var bounds = L.latLngBounds([
            [userLocation.latitude, userLocation.longitude],
            [officeLocation.latitude, officeLocation.longitude]
        ]);
        leafletMap.fitBounds(bounds.pad(0.3));
    }

    // Fungsi untuk menutup peta Leaflet
    function closeLeafletMap() {
        document.getElementById('map-modal').style.display = 'none';
        isMapModalOpen = false;
    }

    // Tutup modal jika user klik di luar modal
    window.onclick = function(event) {
        var modal = document.getElementById('map-modal');
        if (event.target == modal) {
            closeLeafletMap();
        }
    }

    // Fungsi untuk menampilkan peringatan lokasi menggunakan SweetAlert2
    function showLocationAlert(type) {
        console.log('Menampilkan peringatan lokasi untuk absensi ' + type);

        var title = 'Lokasi Di Luar Radius';
        var text = 'Anda tidak dapat melakukan absensi ' + type + ' karena berada di luar radius lokasi yang ditentukan. Jarak Anda saat ini: ' +
                  Math.round(userLocation.distance) + ' meter, sedangkan radius yang diizinkan: ' + officeLocation.radius + ' meter.';

        // Periksa apakah SweetAlert2 tersedia
        if (typeof Swal !== 'undefined') {
            console.log('Menggunakan SweetAlert2 untuk peringatan lokasi');

            // Gunakan SweetAlert2
            Swal.fire({
                icon: 'error',
                title: title,
                text: text,
                footer: '<a href="#" onclick="showLeafletMap(); return false;">Lihat lokasi Anda di peta</a>',
                confirmButtonColor: '#d33',
                confirmButtonText: 'Saya Mengerti'
            }).then(function() {
                console.log('Peringatan lokasi ditutup');
            });
        } else {
            // Fallback ke alert biasa jika SweetAlert2 tidak tersedia
            console.error('SweetAlert2 tidak tersedia. Menggunakan alert biasa.');
            alert(title + '\n\n' + text + '\n\nKlik OK untuk melihat lokasi Anda di peta.');
            showLeafletMap();
        }
    }

    // Fungsi untuk menampilkan peringatan waktu menggunakan SweetAlert2
    function showTimeAlert(message) {
        console.log('Menampilkan peringatan waktu: ' + message);

        // Periksa apakah SweetAlert2 tersedia
        if (typeof Swal !== 'undefined') {
            console.log('Menggunakan SweetAlert2 untuk peringatan waktu');

            // Gunakan SweetAlert2
            Swal.fire({
                icon: 'warning',
                title: 'Waktu Tidak Sesuai',
                text: message,
                confirmButtonColor: '#f6c23e',
                confirmButtonText: 'Saya Mengerti'
            }).then(function() {
                console.log('Peringatan waktu ditutup');
            });
        } else {
            // Fallback ke alert biasa jika SweetAlert2 tidak tersedia
            console.error('SweetAlert2 tidak tersedia. Menggunakan alert biasa.');
            alert('Waktu Tidak Sesuai\n\n' + message);
        }
    }

    // Fungsi untuk membuka peta lokasi di Google Maps (sebagai fallback)
    function openLocationMap() {
        if (!userLocation.latitude || !userLocation.longitude) {
            // Periksa apakah SweetAlert2 tersedia
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'warning',
                    title: 'Lokasi Belum Tersedia',
                    text: 'Mohon tunggu sementara kami mendapatkan lokasi Anda.',
                    confirmButtonColor: '#3085d6'
                });
            } else {
                alert('Lokasi Anda belum tersedia. Mohon tunggu beberapa saat.');
            }
            return;
        }

        // Buat URL untuk Google Maps dengan lokasi pengguna dan lokasi kantor
        var mapUrl = 'https://www.google.com/maps/dir/?api=1&origin=' +
                     userLocation.latitude + ',' + userLocation.longitude +
                     '&destination=' + officeLocation.latitude + ',' + officeLocation.longitude;

        // Buka di tab baru
        window.open(mapUrl, '_blank');
    }

    // Fungsi untuk menampilkan panduan wajah saat kamera aktif
    document.addEventListener('DOMContentLoaded', function() {
        // Fungsi untuk menampilkan panduan wajah saat video dimulai
        function showFaceGuide(videoId, guideId) {
            const video = document.getElementById(videoId);
            if (video) {
                // Tampilkan panduan wajah segera jika video sudah terlihat
                if (video.style.display !== 'none') {
                    const guide = document.getElementById(guideId);
                    if (guide) {
                        guide.style.display = 'block';
                        adjustFaceGuideSize(videoId, guideId);
                    }
                }

                // Tampilkan panduan wajah saat video dimulai
                video.addEventListener('play', function() {
                    const guide = document.getElementById(guideId);
                    if (guide) {
                        guide.style.display = 'block';
                        adjustFaceGuideSize(videoId, guideId);
                    }
                });

                // Sesuaikan ukuran saat video berubah ukuran
                video.addEventListener('loadedmetadata', function() {
                    adjustFaceGuideSize(videoId, guideId);
                });

                // Sesuaikan ukuran saat resolusi video berubah
                video.addEventListener('resize', function() {
                    adjustFaceGuideSize(videoId, guideId);
                });

                // Tampilkan panduan wajah saat video menjadi terlihat
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.attributeName === 'style' && video.style.display !== 'none') {
                            const guide = document.getElementById(guideId);
                            if (guide) {
                                guide.style.display = 'block';
                                adjustFaceGuideSize(videoId, guideId);
                            }
                        }
                    });
                });

                observer.observe(video, { attributes: true });
            }
        }

        // Fungsi untuk menyesuaikan ukuran panduan wajah agar tetap lingkaran
        function adjustFaceGuideSize(videoId, guideId) {
            const video = document.getElementById(videoId);
            const guide = document.getElementById(guideId);
            const faceGuide = guide.querySelector('.face-guide');

            if (video && faceGuide) {
                // Dapatkan ukuran video yang sebenarnya
                let videoWidth, videoHeight;

                // Cek apakah video sudah memiliki dimensi
                if (video.videoWidth && video.videoHeight) {
                    videoWidth = video.videoWidth;
                    videoHeight = video.videoHeight;
                } else {
                    // Jika video belum memiliki dimensi, gunakan dimensi elemen
                    const videoContainer = video.closest('.camera-preview') || video.closest('.camera-preview-desktop');
                    videoWidth = videoContainer.offsetWidth;
                    videoHeight = videoContainer.offsetHeight;
                }

                // Dapatkan ukuran elemen video di layar
                const displayWidth = video.offsetWidth || video.clientWidth;
                const displayHeight = video.offsetHeight || video.clientHeight;

                // Gunakan ukuran terkecil untuk memastikan lingkaran sempurna
                const minDimension = Math.min(displayWidth, displayHeight);
                const size = minDimension * 0.7; // 70% dari dimensi terkecil

                // Terapkan ukuran yang sama untuk width dan height
                faceGuide.style.width = size + 'px';
                faceGuide.style.height = size + 'px';

                // Pastikan lingkaran berada di tengah frame kamera
                const videoRect = video.getBoundingClientRect();
                const containerRect = guide.parentElement.getBoundingClientRect();

                // Hitung posisi tengah video relatif terhadap container
                const offsetX = videoRect.left - containerRect.left + (videoRect.width / 2);
                const offsetY = videoRect.top - containerRect.top + (videoRect.height / 2);

                // Terapkan posisi tengah ke lingkaran
                faceGuide.style.left = offsetX + 'px';
                faceGuide.style.top = offsetY + 'px';
                faceGuide.style.transform = 'translate(-50%, -50%)';

                console.log('Menyesuaikan ukuran panduan wajah:', videoId,
                    'Video asli:', videoWidth, 'x', videoHeight,
                    'Display:', displayWidth, 'x', displayHeight,
                    'Size:', size);
            }
        }

        // Tampilkan panduan wajah untuk semua kamera
        showFaceGuide('video-masuk', 'face-guide-masuk');
        showFaceGuide('video-pulang', 'face-guide-pulang');
        showFaceGuide('video-masuk-desktop', 'face-guide-masuk-desktop');
        showFaceGuide('video-pulang-desktop', 'face-guide-pulang-desktop');

        // Sesuaikan ukuran saat jendela diubah ukurannya
        window.addEventListener('resize', function() {
            adjustFaceGuideSize('video-masuk', 'face-guide-masuk');
            adjustFaceGuideSize('video-pulang', 'face-guide-pulang');
            adjustFaceGuideSize('video-masuk-desktop', 'face-guide-masuk-desktop');
            adjustFaceGuideSize('video-pulang-desktop', 'face-guide-pulang-desktop');
        });

        // Fungsi untuk menyesuaikan semua panduan wajah
        function adjustAllFaceGuides() {
            adjustFaceGuideSize('video-masuk', 'face-guide-masuk');
            adjustFaceGuideSize('video-pulang', 'face-guide-pulang');
            adjustFaceGuideSize('video-masuk-desktop', 'face-guide-masuk-desktop');
            adjustFaceGuideSize('video-pulang-desktop', 'face-guide-pulang-desktop');
        }

        // Jalankan penyesuaian ukuran setelah halaman dimuat
        setTimeout(adjustAllFaceGuides, 500);
        setTimeout(adjustAllFaceGuides, 1000);
        setTimeout(adjustAllFaceGuides, 2000);

        // Jalankan penyesuaian ukuran secara berkala untuk menangani perubahan ukuran yang mungkin terjadi
        setInterval(adjustAllFaceGuides, 2000);

        // Tambahkan event listener untuk mendeteksi perubahan orientasi perangkat
        window.addEventListener('orientationchange', function() {
            // Tunggu sebentar agar perubahan orientasi selesai
            setTimeout(adjustAllFaceGuides, 300);
            setTimeout(adjustAllFaceGuides, 1000);
        });
    });
</script>

<?php
// Include footer
include_once '../includes/footer.php';

// Tutup tag body dan html jika dibuka di awal
if (!headers_sent()) {
    echo '</body></html>';
}
?>

<!-- Tombol Maps Melayang -->
<div id="floating-map-btn" onclick="showLeafletMap()">
    <i class="fas fa-map-marked-alt"></i>
</div>

<!-- Modal untuk Maps Leaflet -->
<div id="map-modal" class="map-modal">
    <div class="map-modal-content">
        <div class="map-modal-header">
            <h5><i class="fas fa-map-marker-alt"></i> Lokasi Anda</h5>
            <span class="map-close" onclick="closeLeafletMap()">&times;</span>
        </div>
        <div class="map-modal-body">
            <div id="leaflet-map"></div>
        </div>
        <div class="map-modal-footer">
            <div class="map-legend">
                <div class="legend-item">
                    <img src="<?php echo BASE_URL; ?>assets/img/user-marker.png" alt="User" width="20">
                    <span>Lokasi Anda</span>
                </div>
                <div class="legend-item">
                    <img src="<?php echo BASE_URL; ?>assets/img/office-marker.png" alt="Office" width="20">
                    <span>Lokasi Kantor</span>
                </div>
                <div class="legend-item">
                    <span class="circle-icon"></span>
                    <span>Radius Absensi</span>
                </div>
            </div>
            <button type="button" class="btn btn-secondary" onclick="closeLeafletMap()">Tutup</button>
        </div>
    </div>
</div>

