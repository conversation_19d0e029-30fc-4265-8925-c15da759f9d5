<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Fungsi untuk menghitung jarak antara dua koordinat
function calculateDistance($lat1, $lon1, $lat2, $lon2) {
    $earthRadius = 6371000; // meter
    $lat1 = deg2rad($lat1);
    $lon1 = deg2rad($lon1);
    $lat2 = deg2rad($lat2);
    $lon2 = deg2rad($lon2);

    $latDelta = $lat2 - $lat1;
    $lonDelta = $lon2 - $lon1;

    $a = sin($latDelta/2) * sin($latDelta/2) +
         cos($lat1) * cos($lat2) *
         sin($lonDelta/2) * sin($lonDelta/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));

    return $earthRadius * $c;
}

// Fungsi untuk menghasilkan koordinat acak dalam radius tertentu
function generateRandomCoordinate($baseLat, $baseLon, $radiusMin, $radiusMax) {
    // Konversi radius dari meter ke derajat (perkiraan kasar)
    $radiusMinDegrees = $radiusMin / 111000; // 1 derajat ~ 111 km
    $radiusMaxDegrees = $radiusMax / 111000;

    // Pilih jarak acak dalam rentang
    $distance = rand($radiusMinDegrees * 1000, $radiusMaxDegrees * 1000) / 1000;

    // Pilih sudut acak
    $angle = rand(0, 360) * (M_PI / 180);

    // Hitung offset
    $latOffset = $distance * cos($angle);
    $lonOffset = $distance * sin($angle) / cos(deg2rad($baseLat));

    // Hitung koordinat baru
    $newLat = $baseLat + $latOffset;
    $newLon = $baseLon + $lonOffset;

    return [$newLat, $newLon];
}

// Ambil data lokasi kantor
$query_lokasi = "SELECT * FROM lokasi";
$result_lokasi = mysqli_query($conn, $query_lokasi);
$lokasi_kantor = [];
while ($row = mysqli_fetch_assoc($result_lokasi)) {
    $lokasi_kantor[] = $row;
}

// Ambil data karyawan
$query_karyawan = "SELECT id, nik, nama FROM users WHERE role = 'karyawan'";
$result_karyawan = mysqli_query($conn, $query_karyawan);
$karyawan = [];
while ($row = mysqli_fetch_assoc($result_karyawan)) {
    $karyawan[] = $row;
}

// Cek apakah form telah disubmit
$message = '';
if (isset($_POST['generate'])) {
    $num_records = intval($_POST['num_records']);
    $start_date = $_POST['start_date'];
    $end_date = $_POST['end_date'];

    // Validasi input
    if ($num_records <= 0 || empty($start_date) || empty($end_date)) {
        $message = '<div class="alert alert-danger">Semua field harus diisi dengan benar.</div>';
    } else if (count($karyawan) == 0) {
        $message = '<div class="alert alert-danger">Tidak ada karyawan yang tersedia.</div>';
    } else if (count($lokasi_kantor) == 0) {
        $message = '<div class="alert alert-danger">Tidak ada lokasi kantor yang tersedia.</div>';
    } else {
        // Konversi tanggal ke timestamp
        $start_timestamp = strtotime($start_date);
        $end_timestamp = strtotime($end_date);

        // Hitung jumlah hari
        $days_diff = floor(($end_timestamp - $start_timestamp) / (60 * 60 * 24)) + 1;

        // Hitung jumlah record per hari
        $records_per_day = ceil($num_records / $days_diff);

        // Counter untuk jumlah record yang berhasil dibuat
        $success_count = 0;

        // Loop untuk setiap hari
        for ($day = 0; $day < $days_diff; $day++) {
            $current_date = date('Y-m-d', $start_timestamp + ($day * 24 * 60 * 60));

            // Loop untuk setiap karyawan
            foreach ($karyawan as $k) {
                // Pilih lokasi kantor secara acak
                $lokasi = $lokasi_kantor[array_rand($lokasi_kantor)];

                // Jumlah aktivitas per karyawan per hari (1-5)
                $num_activities = rand(1, 5);

                for ($i = 0; $i < $num_activities; $i++) {
                    // Jam acak antara 7 pagi dan 6 sore
                    $hour = rand(7, 18);
                    $minute = rand(0, 59);
                    $second = rand(0, 59);
                    $time = sprintf("%02d:%02d:%02d", $hour, $minute, $second);

                    // Tentukan apakah di dalam atau di luar radius
                    $in_radius = (rand(0, 100) < 70); // 70% kemungkinan di dalam radius

                    // Generate koordinat
                    if ($in_radius) {
                        // Di dalam radius
                        list($latitude, $longitude) = generateRandomCoordinate(
                            $lokasi['latitude'],
                            $lokasi['longitude'],
                            0,
                            $lokasi['radius']
                        );
                        $status = 'di dalam radius';
                    } else {
                        // Di luar radius
                        list($latitude, $longitude) = generateRandomCoordinate(
                            $lokasi['latitude'],
                            $lokasi['longitude'],
                            $lokasi['radius'] + 10,
                            $lokasi['radius'] + 1000
                        );
                        $status = 'di luar radius';
                    }

                    // Hitung jarak dari kantor
                    $jarak = calculateDistance(
                        $latitude,
                        $longitude,
                        $lokasi['latitude'],
                        $lokasi['longitude']
                    );

                    // Insert data ke database
                    $query = "INSERT INTO aktivitas_karyawan (user_id, tanggal, waktu, longitude, latitude, status, jarak_dari_kantor, lokasi_id)
                              VALUES ('{$k['id']}', '$current_date', '$time', '$longitude', '$latitude', '$status', '$jarak', '{$lokasi['id']}')";

                    if (mysqli_query($conn, $query)) {
                        $success_count++;
                    }

                    // Jika sudah mencapai jumlah record yang diminta, hentikan
                    if ($success_count >= $num_records) {
                        break 3; // Keluar dari semua loop
                    }
                }
            }
        }

        $message = '<div class="alert alert-success">Berhasil membuat ' . $success_count . ' data aktivitas karyawan.</div>';
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Generate Data Aktivitas Karyawan</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="aktivitas_karyawan.php">Aktivitas Karyawan</a></li>
        <li class="breadcrumb-item active">Generate Data</li>
    </ol>

    <?php echo $message; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-database me-1"></i>
            Generate Data Sampel
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="num_records" class="form-label">Jumlah Data</label>
                        <input type="number" class="form-control" id="num_records" name="num_records" min="1" max="1000" value="100">
                        <div class="form-text">Jumlah data aktivitas yang akan dibuat.</div>
                    </div>
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">Tanggal Mulai</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo date('Y-m-d', strtotime('-7 days')); ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">Tanggal Akhir</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo date('Y-m-d'); ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <p><strong>Informasi:</strong></p>
                            <ul>
                                <li>Data akan dibuat secara acak untuk semua karyawan.</li>
                                <li>Setiap karyawan akan memiliki 1-5 aktivitas per hari.</li>
                                <li>70% aktivitas akan berada di dalam radius kantor.</li>
                                <li>Waktu aktivitas akan berada antara jam 7 pagi dan 6 sore.</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <button type="submit" name="generate" class="btn btn-primary">
                            <i class="fas fa-cogs me-1"></i> Generate Data
                        </button>
                        <a href="aktivitas_karyawan.php" class="btn btn-success ms-2">
                            <i class="fas fa-list me-1"></i> Lihat Data Aktivitas
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Status Tabel
        </div>
        <div class="card-body">
            <?php
            // Cek apakah tabel aktivitas_karyawan ada
            $table_exists = mysqli_query($conn, "SHOW TABLES LIKE 'aktivitas_karyawan'");

            if (mysqli_num_rows($table_exists) > 0) {
                // Hitung jumlah data
                $count_query = "SELECT COUNT(*) as total FROM aktivitas_karyawan";
                $count_result = mysqli_query($conn, $count_query);
                $count_data = mysqli_fetch_assoc($count_result);

                echo '<div class="alert alert-success">Tabel aktivitas_karyawan ada.</div>';
                echo '<div class="alert alert-info">Jumlah data saat ini: ' . $count_data['total'] . '</div>';

                // Tampilkan tombol untuk menghapus semua data
                echo '<form method="post" action="" onsubmit="return confirm(\'Apakah Anda yakin ingin menghapus semua data aktivitas karyawan?\');">';
                echo '<button type="submit" name="delete_all" class="btn btn-danger"><i class="fas fa-trash me-1"></i> Hapus Semua Data</button>';
                echo '</form>';

                // Proses penghapusan data jika tombol ditekan
                if (isset($_POST['delete_all'])) {
                    $delete_query = "TRUNCATE TABLE aktivitas_karyawan";
                    if (mysqli_query($conn, $delete_query)) {
                        echo '<div class="alert alert-success mt-3">Semua data berhasil dihapus.</div>';
                        echo '<meta http-equiv="refresh" content="2;url=generate_sample_data.php">';
                    } else {
                        echo '<div class="alert alert-danger mt-3">Gagal menghapus data: ' . mysqli_error($conn) . '</div>';
                    }
                }
            } else {
                echo '<div class="alert alert-danger">Tabel aktivitas_karyawan tidak ada.</div>';
                echo '<div class="alert alert-info">Silakan jalankan file <code>db/aktivitas_karyawan.sql</code> untuk membuat tabel.</div>';

                // Tampilkan tombol untuk membuat tabel
                echo '<a href="update_database_aktivitas.php" class="btn btn-primary"><i class="fas fa-database me-1"></i> Buat Tabel</a>';
            }
            ?>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
