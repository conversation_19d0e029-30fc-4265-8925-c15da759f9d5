import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import '../services/api_service.dart';
import '../utils/constants.dart';

class PresensiProvider with ChangeNotifier {
  bool _isLoading = false;
  String? _errorMessage;
  Position? _currentPosition;
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Position? get currentPosition => _currentPosition;
  File? get selectedImage => _selectedImage;

  Future<bool> checkLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      _errorMessage = 'Layanan lokasi tidak aktif';
      notifyListeners();
      return false;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        _errorMessage = 'Izin lokasi ditolak';
        notifyListeners();
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      _errorMessage = 'Izin lokasi ditolak secara permanen';
      notifyListeners();
      return false;
    }

    return true;
  }

  Future<bool> getCurrentLocation() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final hasPermission = await checkLocationPermission();
      if (!hasPermission) {
        _isLoading = false;
        notifyListeners();
        return false;
      }

      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: Constants.locationTimeout),
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = Constants.msgLocationError;
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> takePicture() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: Constants.imageQuality,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        _selectedImage = File(image.path);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _errorMessage = Constants.msgCameraError;
      notifyListeners();
      return false;
    }
  }

  String? _convertImageToBase64(File imageFile) {
    try {
      final bytes = imageFile.readAsBytesSync();
      if (bytes.length > Constants.maxImageSize) {
        _errorMessage = 'Ukuran foto terlalu besar';
        return null;
      }
      return base64Encode(bytes);
    } catch (e) {
      _errorMessage = 'Gagal memproses foto';
      return null;
    }
  }

  Future<Map<String, dynamic>> submitPresensi({
    required int userId,
    required String jenisAbsen,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // Validate location
      if (_currentPosition == null) {
        final locationOk = await getCurrentLocation();
        if (!locationOk) {
          _isLoading = false;
          notifyListeners();
          return {
            'success': false,
            'message': _errorMessage ?? Constants.msgLocationError,
          };
        }
      }

      // Validate image
      if (_selectedImage == null) {
        _isLoading = false;
        notifyListeners();
        return {
          'success': false,
          'message': 'Foto harus diambil terlebih dahulu',
        };
      }

      // Convert image to base64
      final fotoBase64 = _convertImageToBase64(_selectedImage!);
      if (fotoBase64 == null) {
        _isLoading = false;
        notifyListeners();
        return {
          'success': false,
          'message': _errorMessage ?? 'Gagal memproses foto',
        };
      }

      // Submit presensi
      final response = await ApiService.submitPresensi(
        userId: userId,
        jenisAbsen: jenisAbsen,
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        fotoBase64: fotoBase64,
      );

      _isLoading = false;

      if (response['status'] == 'success') {
        // Clear selected image after successful submission
        _selectedImage = null;
        notifyListeners();
        
        return {
          'success': true,
          'message': response['message'] ?? Constants.msgPresensiSuccess,
          'data': response['data'],
        };
      } else {
        _errorMessage = response['message'];
        notifyListeners();
        
        return {
          'success': false,
          'message': response['message'] ?? Constants.msgPresensiFailed,
        };
      }
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Terjadi kesalahan: ${e.toString()}';
      notifyListeners();
      
      return {
        'success': false,
        'message': _errorMessage!,
      };
    }
  }

  void clearSelectedImage() {
    _selectedImage = null;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearData() {
    _selectedImage = null;
    _currentPosition = null;
    _errorMessage = null;
    notifyListeners();
  }

  String getLocationString() {
    if (_currentPosition == null) return 'Lokasi tidak tersedia';
    return '${_currentPosition!.latitude.toStringAsFixed(6)}, ${_currentPosition!.longitude.toStringAsFixed(6)}';
  }
}
