<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Ambil ID karyawan, bulan, dan tahun
$id = isset($_GET['id']) ? clean($_GET['id']) : 0;
$bulan = isset($_GET['bulan']) ? clean($_GET['bulan']) : date('m');
$tahun = isset($_GET['tahun']) ? clean($_GET['tahun']) : date('Y');

// Ambil data karyawan
$karyawan = getKaryawanById($id);

if (!$karyawan) {
    setMessage('danger', 'Data karyawan tidak ditemukan!');
    redirect('admin/laporan.php');
}

// Ambil data presensi
$query = "SELECT * FROM presensi
          WHERE user_id = '$id'
          AND MONTH(tanggal) = '$bulan'
          AND YEAR(tanggal) = '$tahun'
          ORDER BY tanggal ASC";
$result = mysqli_query($conn, $query);

$presensi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $presensi[] = $row;
    }
}

// Ambil data denda
$denda = getDenda();

// Hitung jumlah hari dalam bulan
$jumlah_hari = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);

// Ambil data hari libur dalam bulan ini
$query = "SELECT tanggal FROM hari_libur WHERE MONTH(tanggal) = '$bulan' AND YEAR(tanggal) = '$tahun'";
$result = mysqli_query($conn, $query);

$hari_libur = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $hari_libur[] = $row['tanggal'];
    }
}

// Hitung jumlah hari kerja (tidak termasuk Sabtu, Minggu, dan hari libur)
$hari_kerja = 0;
for ($i = 1; $i <= $jumlah_hari; $i++) {
    $tanggal = sprintf('%04d-%02d-%02d', $tahun, $bulan, $i);
    $hari = date('N', strtotime($tanggal)); // 1 (Senin) sampai 7 (Minggu)

    // Cek apakah hari kerja (Senin-Jumat) dan bukan hari libur
    if ($hari >= 1 && $hari <= 5 && !in_array($tanggal, $hari_libur)) {
        $hari_kerja++;
    }
}

// Hitung statistik
$jumlah_kehadiran = count($presensi);
$jumlah_terlambat = 0;
$jumlah_pulang_awal = 0;
$jumlah_tidak_absen_pulang = 0;

foreach ($presensi as $p) {
    if ($p['status'] == 'Terlambat') {
        $jumlah_terlambat++;
    } elseif ($p['status'] == 'Pulang Awal') {
        $jumlah_pulang_awal++;
    }

    // Hitung jumlah tidak absen pulang (absen masuk ada tapi absen pulang tidak ada)
    if (!empty($p['jam_masuk']) && empty($p['jam_pulang'])) {
        $jumlah_tidak_absen_pulang++;
    }
}

$jumlah_tidak_hadir = $hari_kerja - $jumlah_kehadiran;
if ($jumlah_tidak_hadir < 0) $jumlah_tidak_hadir = 0;

// Hitung total denda
$total_denda_terlambat = $jumlah_terlambat * $denda['denda_masuk'];
$total_denda_pulang_awal = $jumlah_pulang_awal * $denda['denda_pulang'];
$total_denda_tidak_hadir = $jumlah_tidak_hadir * $denda['denda_tidak_absen'];
$total_denda_tidak_absen_pulang = $jumlah_tidak_absen_pulang * $denda['denda_tidak_absen_pulang'];
$total_denda = $total_denda_terlambat + $total_denda_pulang_awal + $total_denda_tidak_hadir + $total_denda_tidak_absen_pulang;

// Proses export ke Excel
if (isset($_GET['export']) && $_GET['export'] == 'excel') {
    // Set header untuk download file Excel
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="Laporan_Absensi_' . $karyawan['nama'] . '_' . $bulan . '_' . $tahun . '.xls"');
    header('Cache-Control: max-age=0');

    // Output tabel Excel
    echo '<table border="1">';
    echo '<tr>';
    echo '<th colspan="7">LAPORAN ABSENSI KARYAWAN</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th colspan="7">Periode: ' . date('F Y', strtotime($tahun . '-' . $bulan . '-01')) . '</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th colspan="7">Nama: ' . $karyawan['nama'] . ' (' . $karyawan['nik'] . ')</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th>No</th>';
    echo '<th>Tanggal</th>';
    echo '<th>Jam Masuk</th>';
    echo '<th>Jam Pulang</th>';
    echo '<th>Status</th>';
    echo '<th>Keterangan</th>';
    echo '<th>Denda</th>';
    echo '</tr>';

    $no = 1;
    foreach ($presensi as $p) {
        echo '<tr>';
        echo '<td>' . $no++ . '</td>';
        echo '<td>' . date('d-m-Y', strtotime($p['tanggal'])) . '</td>';
        echo '<td>' . $p['jam_masuk'] . '</td>';
        echo '<td>' . ($p['jam_pulang'] ?? '-') . '</td>';
        echo '<td>' . $p['status'] . '</td>';
        echo '<td>' . ($p['keterangan'] ?? '-') . '</td>';

        // Hitung denda
        $denda_item = 0;
        if ($p['status'] == 'Terlambat') {
            $denda_item = $denda['denda_masuk'];
        } elseif ($p['status'] == 'Pulang Awal') {
            $denda_item = $denda['denda_pulang'];
        }

        // Tambahkan denda tidak absen pulang
        if (!empty($p['jam_masuk']) && empty($p['jam_pulang'])) {
            $denda_item += $denda['denda_tidak_absen_pulang'];
        }

        echo '<td>Rp ' . number_format($denda_item, 0, ',', '.') . '</td>';
        echo '</tr>';
    }

    // Tambahkan baris untuk denda tidak absen pulang
    if ($jumlah_tidak_absen_pulang > 0) {
        echo '<tr>';
        echo '<td colspan="6">Jumlah Tidak Absen Pulang: ' . $jumlah_tidak_absen_pulang . ' kali x Rp ' . number_format($denda['denda_tidak_absen_pulang'], 0, ',', '.') . '</td>';
        echo '<td>Rp ' . number_format($total_denda_tidak_absen_pulang, 0, ',', '.') . '</td>';
        echo '</tr>';
    }

    // Tambahkan baris untuk hari tidak hadir
    if ($jumlah_tidak_hadir > 0) {
        echo '<tr>';
        echo '<td colspan="6">Jumlah Hari Tidak Hadir: ' . $jumlah_tidak_hadir . ' hari x Rp ' . number_format($denda['denda_tidak_absen'], 0, ',', '.') . '</td>';
        echo '<td>Rp ' . number_format($total_denda_tidak_hadir, 0, ',', '.') . '</td>';
        echo '</tr>';
    }

    echo '<tr>';
    echo '<th colspan="6">Total Denda</th>';
    echo '<th>Rp ' . number_format($total_denda, 0, ',', '.') . '</th>';
    echo '</tr>';

    echo '</table>';
    exit;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Detail Laporan Absensi</h1>
        <div>
            <a href="laporan.php" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="detail_laporan.php?id=<?php echo $id; ?>&bulan=<?php echo $bulan; ?>&tahun=<?php echo $tahun; ?>&export=excel" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Informasi Karyawan</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">NIK</th>
                            <td><?php echo $karyawan['nik']; ?></td>
                        </tr>
                        <tr>
                            <th>Nama</th>
                            <td><?php echo $karyawan['nama']; ?></td>
                        </tr>
                        <tr>
                            <th>Bidang</th>
                            <td><?php echo $karyawan['bidang']; ?></td>
                        </tr>
                        <tr>
                            <th>Jabatan</th>
                            <td><?php echo $karyawan['jabatan']; ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Periode</th>
                            <td><?php echo date('F Y', strtotime($tahun . '-' . $bulan . '-01')); ?></td>
                        </tr>
                        <tr>
                            <th>Hari Kerja</th>
                            <td><?php echo $hari_kerja; ?> hari</td>
                        </tr>
                        <tr>
                            <th>Kehadiran</th>
                            <td><?php echo $jumlah_kehadiran; ?> hari</td>
                        </tr>
                        <tr>
                            <th>Total Denda</th>
                            <td>Rp <?php echo number_format($total_denda, 0, ',', '.'); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Rincian Absensi</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Tanggal</th>
                            <th>Jam Masuk</th>
                            <th>Jam Pulang</th>
                            <th>Status</th>
                            <th>Keterangan</th>
                            <th>Denda</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($presensi)): ?>
                            <tr>
                                <td colspan="7" class="text-center">Tidak ada data presensi</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($presensi as $p): ?>
                                <tr>
                                    <td><?php echo date('d-m-Y', strtotime($p['tanggal'])); ?></td>
                                    <td><?php echo $p['jam_masuk']; ?></td>
                                    <td><?php echo $p['jam_pulang'] ?? '-'; ?></td>
                                    <td>
                                        <?php if ($p['status'] == 'Tepat Waktu'): ?>
                                            <span class="badge bg-success">Tepat Waktu</span>
                                        <?php elseif ($p['status'] == 'Terlambat'): ?>
                                            <span class="badge bg-warning">Terlambat</span>
                                        <?php elseif ($p['status'] == 'Pulang Awal'): ?>
                                            <span class="badge bg-warning">Pulang Awal</span>
                                        <?php elseif ($p['status'] == 'Lembur'): ?>
                                            <span class="badge bg-info">Lembur</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?php echo $p['status']; ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $p['keterangan'] ?? '-'; ?></td>
                                    <td>
                                        <?php
                                        $denda_item = 0;
                                        if ($p['status'] == 'Terlambat') {
                                            $denda_item = $denda['denda_masuk'];
                                        } elseif ($p['status'] == 'Pulang Awal') {
                                            $denda_item = $denda['denda_pulang'];
                                        }

                                        // Tambahkan denda tidak absen pulang
                                        if (!empty($p['jam_masuk']) && empty($p['jam_pulang'])) {
                                            $denda_item += $denda['denda_tidak_absen_pulang'];
                                        }

                                        echo 'Rp ' . number_format($denda_item, 0, ',', '.');
                                        ?>
                                    </td>
                                    <td>
                                        <a href="detail_presensi.php?id=<?php echo $p['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> Detail
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Rincian Denda</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Jenis Denda</th>
                            <th>Jumlah</th>
                            <th>Nominal per Denda</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Keterlambatan</td>
                            <td><?php echo $jumlah_terlambat; ?> kali</td>
                            <td>Rp <?php echo number_format($denda['denda_masuk'], 0, ',', '.'); ?></td>
                            <td>Rp <?php echo number_format($total_denda_terlambat, 0, ',', '.'); ?></td>
                        </tr>
                        <tr>
                            <td>Pulang Awal</td>
                            <td><?php echo $jumlah_pulang_awal; ?> kali</td>
                            <td>Rp <?php echo number_format($denda['denda_pulang'], 0, ',', '.'); ?></td>
                            <td>Rp <?php echo number_format($total_denda_pulang_awal, 0, ',', '.'); ?></td>
                        </tr>
                        <tr>
                            <td>Tidak Absen Pulang</td>
                            <td><?php echo $jumlah_tidak_absen_pulang; ?> kali</td>
                            <td>Rp <?php echo number_format($denda['denda_tidak_absen_pulang'], 0, ',', '.'); ?></td>
                            <td>Rp <?php echo number_format($total_denda_tidak_absen_pulang, 0, ',', '.'); ?></td>
                        </tr>
                        <tr>
                            <td>Tidak Hadir</td>
                            <td><?php echo $jumlah_tidak_hadir; ?> hari</td>
                            <td>Rp <?php echo number_format($denda['denda_tidak_absen'], 0, ',', '.'); ?></td>
                            <td>Rp <?php echo number_format($total_denda_tidak_hadir, 0, ',', '.'); ?></td>
                        </tr>
                        <tr class="table-primary">
                            <th colspan="3">Total Denda</th>
                            <th>Rp <?php echo number_format($total_denda, 0, ',', '.'); ?></th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
