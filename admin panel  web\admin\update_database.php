<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Fungsi untuk menjalankan query dan menampilkan hasilnya
function runQuery($conn, $query, $message) {
    echo "<p>Menjalankan query: <code>$query</code></p>";

    if (mysqli_query($conn, $query)) {
        echo "<p class='text-success'>$message berhasil!</p>";
        return true;
    } else {
        echo "<p class='text-danger'>Error: " . mysqli_error($conn) . "</p>";
        return false;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Update Database</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Proses Update Database</h6>
        </div>
        <div class="card-body">
            <?php
            // Cek apakah tabel bidang sudah ada
            $query = "SHOW TABLES LIKE 'bidang'";
            $result = mysqli_query($conn, $query);

            if (mysqli_num_rows($result) == 0) {
                // Buat tabel bidang
                $query = "CREATE TABLE bidang (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    nama_bidang VARCHAR(100) NOT NULL UNIQUE,
                    keterangan TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )";

                if (!runQuery($conn, $query, "Pembuatan tabel bidang")) {
                    echo "<p class='text-danger'>Gagal membuat tabel bidang. Proses dihentikan.</p>";
                    exit;
                }

                // Tambahkan data bidang default
                $query = "INSERT INTO bidang (nama_bidang, keterangan) VALUES
                ('Umum', 'Bidang umum untuk semua karyawan'),
                ('IT', 'Bidang teknologi informasi'),
                ('HRD', 'Bidang sumber daya manusia'),
                ('Keuangan', 'Bidang keuangan dan akuntansi'),
                ('Marketing', 'Bidang pemasaran')";

                runQuery($conn, $query, "Penambahan data bidang default");
            } else {
                echo "<p>Tabel bidang sudah ada.</p>";
            }

            // Cek struktur tabel jam_kerja
            $query = "SHOW COLUMNS FROM jam_kerja LIKE 'bidang_id'";
            $result = mysqli_query($conn, $query);

            if (mysqli_num_rows($result) == 0) {
                // Tambahkan kolom bidang_id ke tabel jam_kerja
                $query = "ALTER TABLE jam_kerja ADD COLUMN bidang_id INT AFTER id";
                runQuery($conn, $query, "Penambahan kolom bidang_id ke tabel jam_kerja");

                // Update data jam_kerja yang sudah ada
                $query = "SELECT * FROM jam_kerja";
                $result = mysqli_query($conn, $query);

                while ($row = mysqli_fetch_assoc($result)) {
                    $bidang_name = $row['bidang'];

                    // Cari id bidang berdasarkan nama
                    $query_bidang = "SELECT id FROM bidang WHERE nama_bidang = '$bidang_name'";
                    $result_bidang = mysqli_query($conn, $query_bidang);

                    if (mysqli_num_rows($result_bidang) > 0) {
                        $bidang_row = mysqli_fetch_assoc($result_bidang);
                        $bidang_id = $bidang_row['id'];

                        // Update jam_kerja dengan bidang_id
                        $jam_kerja_id = $row['id'];
                        $query_update = "UPDATE jam_kerja SET bidang_id = $bidang_id WHERE id = $jam_kerja_id";
                        runQuery($conn, $query_update, "Update data jam kerja ID $jam_kerja_id");
                    } else {
                        // Jika bidang tidak ditemukan, buat bidang baru
                        $query_insert = "INSERT INTO bidang (nama_bidang, keterangan) VALUES ('$bidang_name', 'Bidang yang dimigrasi dari data lama')";
                        runQuery($conn, $query_insert, "Penambahan bidang baru '$bidang_name'");

                        // Ambil id bidang yang baru dibuat
                        $bidang_id = mysqli_insert_id($conn);

                        // Update jam_kerja dengan bidang_id
                        $jam_kerja_id = $row['id'];
                        $query_update = "UPDATE jam_kerja SET bidang_id = $bidang_id WHERE id = $jam_kerja_id";
                        runQuery($conn, $query_update, "Update data jam kerja ID $jam_kerja_id");
                    }
                }

                // Tambahkan foreign key ke tabel jam_kerja
                $query = "ALTER TABLE jam_kerja ADD FOREIGN KEY (bidang_id) REFERENCES bidang(id) ON DELETE CASCADE";
                runQuery($conn, $query, "Penambahan foreign key ke tabel jam_kerja");
            } else {
                echo "<p>Kolom bidang_id sudah ada di tabel jam_kerja.</p>";
            }

            // Cek struktur tabel users
            $query = "SHOW COLUMNS FROM users LIKE 'bidang_id'";
            $result = mysqli_query($conn, $query);

            if (mysqli_num_rows($result) == 0) {
                // Tambahkan kolom bidang_id ke tabel users
                $query = "ALTER TABLE users ADD COLUMN bidang_id INT AFTER nama";
                runQuery($conn, $query, "Penambahan kolom bidang_id ke tabel users");

                // Update data users yang sudah ada
                $query = "SELECT * FROM users WHERE bidang IS NOT NULL";
                $result = mysqli_query($conn, $query);

                while ($row = mysqli_fetch_assoc($result)) {
                    $bidang_name = $row['bidang'];

                    // Cari id bidang berdasarkan nama
                    $query_bidang = "SELECT id FROM bidang WHERE nama_bidang = '$bidang_name'";
                    $result_bidang = mysqli_query($conn, $query_bidang);

                    if (mysqli_num_rows($result_bidang) > 0) {
                        $bidang_row = mysqli_fetch_assoc($result_bidang);
                        $bidang_id = $bidang_row['id'];

                        // Update users dengan bidang_id
                        $user_id = $row['id'];
                        $query_update = "UPDATE users SET bidang_id = $bidang_id WHERE id = $user_id";
                        runQuery($conn, $query_update, "Update data user ID $user_id");
                    } else {
                        // Jika bidang tidak ditemukan, buat bidang baru
                        $query_insert = "INSERT INTO bidang (nama_bidang, keterangan) VALUES ('$bidang_name', 'Bidang yang dimigrasi dari data lama')";
                        runQuery($conn, $query_insert, "Penambahan bidang baru '$bidang_name'");

                        // Ambil id bidang yang baru dibuat
                        $bidang_id = mysqli_insert_id($conn);

                        // Update users dengan bidang_id
                        $user_id = $row['id'];
                        $query_update = "UPDATE users SET bidang_id = $bidang_id WHERE id = $user_id";
                        runQuery($conn, $query_update, "Update data user ID $user_id");
                    }
                }

                // Tambahkan foreign key ke tabel users
                $query = "ALTER TABLE users ADD FOREIGN KEY (bidang_id) REFERENCES bidang(id) ON DELETE SET NULL";
                runQuery($conn, $query, "Penambahan foreign key ke tabel users");
            } else {
                echo "<p>Kolom bidang_id sudah ada di tabel users.</p>";
            }

            // Cek kolom akhir_jam_masuk dan akhir_jam_pulang di tabel jam_kerja
            $query = "SHOW COLUMNS FROM jam_kerja LIKE 'akhir_jam_masuk'";
            $result = mysqli_query($conn, $query);

            if (mysqli_num_rows($result) == 0) {
                // Tambahkan kolom akhir_jam_masuk dan akhir_jam_pulang ke tabel jam_kerja
                $query = "ALTER TABLE jam_kerja
                          ADD COLUMN akhir_jam_masuk TIME NOT NULL AFTER jam_masuk,
                          ADD COLUMN akhir_jam_pulang TIME NOT NULL AFTER jam_pulang";
                runQuery($conn, $query, "Penambahan kolom akhir_jam_masuk dan akhir_jam_pulang ke tabel jam_kerja");

                // Update data jam_kerja yang sudah ada
                $query = "UPDATE jam_kerja
                          SET akhir_jam_masuk = ADDTIME(jam_masuk, '01:00:00'),
                              akhir_jam_pulang = ADDTIME(jam_pulang, '01:00:00')";
                runQuery($conn, $query, "Update data jam kerja dengan nilai default untuk akhir_jam_masuk dan akhir_jam_pulang");
            } else {
                echo "<p>Kolom akhir_jam_masuk dan akhir_jam_pulang sudah ada di tabel jam_kerja.</p>";
            }

            // Cek apakah tabel hari_kerja sudah ada
            $query = "SHOW TABLES LIKE 'hari_kerja'";
            $result = mysqli_query($conn, $query);

            if (mysqli_num_rows($result) == 0) {
                // Buat tabel hari_kerja
                $query = "CREATE TABLE hari_kerja (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    bidang_id INT NOT NULL,
                    hari ENUM('Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu') NOT NULL,
                    status BOOLEAN NOT NULL DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (bidang_id) REFERENCES bidang(id) ON DELETE CASCADE,
                    UNIQUE KEY (bidang_id, hari)
                )";

                if (!runQuery($conn, $query, "Pembuatan tabel hari_kerja")) {
                    echo "<p class='text-danger'>Gagal membuat tabel hari_kerja. Proses dihentikan.</p>";
                    exit;
                }

                // Tambahkan data hari kerja default untuk semua bidang
                $query = "SELECT id FROM bidang";
                $result = mysqli_query($conn, $query);

                while ($row = mysqli_fetch_assoc($result)) {
                    $bidang_id = $row['id'];

                    // Tambahkan data hari kerja default
                    $query = "INSERT INTO hari_kerja (bidang_id, hari, status) VALUES
                    ($bidang_id, 'Senin', TRUE),
                    ($bidang_id, 'Selasa', TRUE),
                    ($bidang_id, 'Rabu', TRUE),
                    ($bidang_id, 'Kamis', TRUE),
                    ($bidang_id, 'Jumat', TRUE),
                    ($bidang_id, 'Sabtu', FALSE),
                    ($bidang_id, 'Minggu', FALSE)";

                    runQuery($conn, $query, "Penambahan data hari kerja default untuk bidang ID $bidang_id");
                }
            } else {
                echo "<p>Tabel hari_kerja sudah ada.</p>";
            }

            // Cek apakah tabel jam_kerja_bidang sudah ada
            $query = "SHOW TABLES LIKE 'jam_kerja_bidang'";
            $result = mysqli_query($conn, $query);

            if (mysqli_num_rows($result) == 0) {
                // Buat tabel jam_kerja_bidang
                $query = "CREATE TABLE jam_kerja_bidang (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    bidang_id INT NOT NULL,
                    hari ENUM('Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu') NOT NULL,
                    jam_kerja_id INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (bidang_id) REFERENCES bidang(id) ON DELETE CASCADE,
                    FOREIGN KEY (jam_kerja_id) REFERENCES jam_kerja(id) ON DELETE SET NULL,
                    UNIQUE KEY (bidang_id, hari)
                )";

                if (!runQuery($conn, $query, "Pembuatan tabel jam_kerja_bidang")) {
                    echo "<p class='text-danger'>Gagal membuat tabel jam_kerja_bidang. Proses dihentikan.</p>";
                    exit;
                }

                // Tambahkan data jam kerja bidang default untuk semua bidang
                $query = "SELECT id FROM bidang";
                $result = mysqli_query($conn, $query);

                while ($row = mysqli_fetch_assoc($result)) {
                    $bidang_id = $row['id'];

                    // Cari jam kerja default untuk bidang ini
                    $query_jam_kerja = "SELECT id FROM jam_kerja WHERE bidang_id = $bidang_id LIMIT 1";
                    $result_jam_kerja = mysqli_query($conn, $query_jam_kerja);
                    $jam_kerja_id = null;

                    if (mysqli_num_rows($result_jam_kerja) > 0) {
                        $jam_kerja_row = mysqli_fetch_assoc($result_jam_kerja);
                        $jam_kerja_id = $jam_kerja_row['id'];
                    }

                    // Tambahkan data jam kerja bidang default
                    $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
                    foreach ($hari as $h) {
                        // Cek apakah hari ini adalah hari kerja
                        $query_hari_kerja = "SELECT status FROM hari_kerja WHERE bidang_id = $bidang_id AND hari = '$h'";
                        $result_hari_kerja = mysqli_query($conn, $query_hari_kerja);
                        $is_hari_kerja = false;

                        if (mysqli_num_rows($result_hari_kerja) > 0) {
                            $hari_kerja_row = mysqli_fetch_assoc($result_hari_kerja);
                            $is_hari_kerja = (bool)$hari_kerja_row['status'];
                        }

                        $jk_id = $is_hari_kerja ? ($jam_kerja_id ? $jam_kerja_id : "NULL") : "NULL";

                        $query_insert = "INSERT INTO jam_kerja_bidang (bidang_id, hari, jam_kerja_id) VALUES
                        ($bidang_id, '$h', $jk_id)";

                        runQuery($conn, $query_insert, "Penambahan data jam kerja bidang untuk bidang ID $bidang_id, hari $h");
                    }
                }
            } else {
                echo "<p>Tabel jam_kerja_bidang sudah ada.</p>";
            }

            // Cek kolom denda_tidak_absen_pulang di tabel denda
            $query = "SHOW COLUMNS FROM denda LIKE 'denda_tidak_absen_pulang'";
            $result = mysqli_query($conn, $query);

            if (mysqli_num_rows($result) == 0) {
                // Tambahkan kolom denda_tidak_absen_pulang ke tabel denda
                $query = "ALTER TABLE denda ADD COLUMN denda_tidak_absen_pulang DECIMAL(10, 2) NOT NULL DEFAULT 25000 AFTER denda_tidak_absen";
                runQuery($conn, $query, "Penambahan kolom denda_tidak_absen_pulang ke tabel denda");
            } else {
                echo "<p>Kolom denda_tidak_absen_pulang sudah ada di tabel denda.</p>";
            }

            // Cek apakah tabel izin_dinas sudah ada
            $query = "SHOW TABLES LIKE 'izin_dinas'";
            $result = mysqli_query($conn, $query);

            if (mysqli_num_rows($result) == 0) {
                // Buat tabel izin_dinas
                $query = "CREATE TABLE izin_dinas (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    tanggal_mulai DATE NOT NULL,
                    tanggal_selesai DATE NOT NULL,
                    tujuan VARCHAR(255) NOT NULL,
                    keterangan TEXT,
                    status ENUM('Pending', 'Approved', 'Rejected') NOT NULL DEFAULT 'Pending',
                    approved_by INT,
                    approved_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
                )";

                if (!runQuery($conn, $query, "Pembuatan tabel izin_dinas")) {
                    echo "<p class='text-danger'>Gagal membuat tabel izin_dinas. Proses dihentikan.</p>";
                    exit;
                }
            } else {
                echo "<p>Tabel izin_dinas sudah ada.</p>";
            }

            echo "<div class='alert alert-success mt-4'>Proses update database selesai!</div>";
            ?>

            <div class="mt-4">
                <a href="jam_kerja.php" class="btn btn-primary">Kembali ke Halaman Jam Kerja</a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
