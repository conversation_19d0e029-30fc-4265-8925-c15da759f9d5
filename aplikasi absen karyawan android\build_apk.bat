@echo off
echo ========================================
echo    BUILD APK APLIKASI ABSENSI KARYAWAN
echo ========================================
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter tidak ditemukan!
    echo <PERSON>lakan install Flutter terlebih dahulu.
    echo Download dari: https://docs.flutter.dev/get-started/install
    pause
    exit /b 1
)

echo [1/5] Checking Flutter installation...
flutter doctor --android-licenses

echo.
echo [2/6] Cleaning project...
flutter clean

echo.
echo [3/6] Getting dependencies...
flutter pub get

echo.
echo [4/6] Checking for Android v1 embedding issues...
flutter pub deps

echo.
echo [5/6] Building APK...
echo Pilih jenis build:
echo 1. Debug APK (untuk testing)
echo 2. Release APK (untuk production)
echo 3. Release APK dengan optimasi
set /p choice="Masukkan pilihan (1-3): "

if "%choice%"=="1" (
    echo Building Debug APK...
    flutter build apk --debug
    set "apk_file=app-debug.apk"
) else if "%choice%"=="2" (
    echo Building Release APK...
    flutter build apk --release
    set "apk_file=app-release.apk"
) else if "%choice%"=="3" (
    echo Building Optimized Release APK...
    flutter build apk --release --obfuscate --split-debug-info=build/debug-info
    set "apk_file=app-release.apk"
) else (
    echo Pilihan tidak valid!
    pause
    exit /b 1
)

echo.
echo [6/6] Build completed!

REM Check if APK was created successfully
if exist "build\app\outputs\flutter-apk\%apk_file%" (
    echo.
    echo ========================================
    echo           BUILD BERHASIL!
    echo ========================================
    echo.
    echo File APK tersedia di:
    echo %cd%\build\app\outputs\flutter-apk\%apk_file%
    echo.
    echo Ukuran file:
    for %%A in ("build\app\outputs\flutter-apk\%apk_file%") do echo %%~zA bytes
    echo.
    echo Cara install di Android:
    echo 1. Copy file APK ke perangkat Android
    echo 2. Enable "Unknown Sources" di Settings
    echo 3. Tap file APK dan install
    echo.
    
    REM Ask if user wants to open the APK folder
    set /p open_folder="Buka folder APK? (y/n): "
    if /i "%open_folder%"=="y" (
        explorer "build\app\outputs\flutter-apk"
    )
) else (
    echo.
    echo ========================================
    echo           BUILD GAGAL!
    echo ========================================
    echo.
    echo Silakan cek error di atas dan coba lagi.
    echo.
    echo Tips troubleshooting:
    echo 1. Pastikan Android SDK terinstall
    echo 2. Jalankan: flutter doctor
    echo 3. Pastikan ada koneksi internet
)

echo.
pause
