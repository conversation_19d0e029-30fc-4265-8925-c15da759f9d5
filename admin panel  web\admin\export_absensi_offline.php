<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Filter data
$filter_user_id = isset($_GET['user_id']) ? clean($_GET['user_id']) : '';
$filter_tanggal_awal = isset($_GET['tanggal_awal']) ? clean($_GET['tanggal_awal']) : date('Y-m-01');
$filter_tanggal_akhir = isset($_GET['tanggal_akhir']) ? clean($_GET['tanggal_akhir']) : date('Y-m-t');
$filter_jenis_absen = isset($_GET['jenis_absen']) ? clean($_GET['jenis_absen']) : '';
$filter_status = isset($_GET['status']) ? clean($_GET['status']) : '';

// Ambil data karyawan untuk filter
$query_karyawan = "SELECT id, nik, nama FROM users WHERE role = 'karyawan' ORDER BY nama";
$result_karyawan = mysqli_query($conn, $query_karyawan);
$karyawan = [];
while ($row = mysqli_fetch_assoc($result_karyawan)) {
    $karyawan[] = $row;
}

// Jika tombol export ditekan
if (isset($_POST['export'])) {
    // Set header untuk download file CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=absensi_offline_' . date('Y-m-d') . '.csv');
    
    // Buat file pointer untuk output
    $output = fopen('php://output', 'w');
    
    // Tambahkan BOM untuk UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Tambahkan header kolom
    fputcsv($output, ['No', 'NIK', 'Nama', 'Tanggal', 'Jam', 'Jenis Absen', 'Status', 'Keterangan']);
    
    // Query untuk mengambil data absensi offline
    $query = "SELECT ao.*, u.nik, u.nama 
              FROM absensi_offline ao
              LEFT JOIN users u ON ao.user_id = u.id
              WHERE 1=1";
    
    // Tambahkan filter
    if (!empty($filter_user_id)) {
        $query .= " AND ao.user_id = '$filter_user_id'";
    }
    
    if (!empty($filter_tanggal_awal)) {
        $query .= " AND ao.tanggal >= '$filter_tanggal_awal'";
    }
    
    if (!empty($filter_tanggal_akhir)) {
        $query .= " AND ao.tanggal <= '$filter_tanggal_akhir'";
    }
    
    if (!empty($filter_jenis_absen)) {
        $query .= " AND ao.jenis_absen = '$filter_jenis_absen'";
    }
    
    if (!empty($filter_status)) {
        $query .= " AND ao.status = '$filter_status'";
    }
    
    $query .= " ORDER BY ao.tanggal DESC, ao.jam DESC";
    $result = mysqli_query($conn, $query);
    
    $no = 1;
    while ($row = mysqli_fetch_assoc($result)) {
        // Format status
        if ($row['status'] == 'pending') {
            $status = 'Pending';
        } else if ($row['status'] == 'approved') {
            $status = 'Disetujui';
        } else {
            $status = 'Ditolak';
        }
        
        // Format jenis absen
        $jenis_absen = $row['jenis_absen'] == 'masuk' ? 'Masuk' : 'Keluar';
        
        // Tambahkan data ke CSV
        fputcsv($output, [
            $no++,
            $row['nik'],
            $row['nama'],
            date('d/m/Y', strtotime($row['tanggal'])),
            date('H:i:s', strtotime($row['jam'])),
            $jenis_absen,
            $status,
            $row['keterangan'] ?? '-'
        ]);
    }
    
    fclose($output);
    exit;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Export Data Absensi Offline</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="absensi_offline.php">Absensi Offline</a></li>
        <li class="breadcrumb-item active">Export Data</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Data
        </div>
        <div class="card-body">
            <form method="get" action="" class="row g-3">
                <div class="col-md-3">
                    <label for="user_id" class="form-label">Karyawan</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">Semua Karyawan</option>
                        <?php foreach ($karyawan as $k): ?>
                            <option value="<?php echo $k['id']; ?>" <?php echo $filter_user_id == $k['id'] ? 'selected' : ''; ?>>
                                <?php echo $k['nik'] . ' - ' . $k['nama']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="tanggal_awal" class="form-label">Tanggal Awal</label>
                    <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?php echo $filter_tanggal_awal; ?>">
                </div>
                <div class="col-md-3">
                    <label for="tanggal_akhir" class="form-label">Tanggal Akhir</label>
                    <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?php echo $filter_tanggal_akhir; ?>">
                </div>
                <div class="col-md-3">
                    <label for="jenis_absen" class="form-label">Jenis Absen</label>
                    <select class="form-select" id="jenis_absen" name="jenis_absen">
                        <option value="">Semua Jenis</option>
                        <option value="masuk" <?php echo $filter_jenis_absen == 'masuk' ? 'selected' : ''; ?>>Masuk</option>
                        <option value="keluar" <?php echo $filter_jenis_absen == 'keluar' ? 'selected' : ''; ?>>Keluar</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Semua Status</option>
                        <option value="pending" <?php echo $filter_status == 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="approved" <?php echo $filter_status == 'approved' ? 'selected' : ''; ?>>Disetujui</option>
                        <option value="rejected" <?php echo $filter_status == 'rejected' ? 'selected' : ''; ?>>Ditolak</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <a href="export_absensi_offline.php" class="btn btn-secondary">
                        <i class="fas fa-sync-alt me-1"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Ringkasan Absensi Offline
        </div>
        <div class="card-body">
            <div class="mb-3">
                <form method="post" action="">
                    <input type="hidden" name="user_id" value="<?php echo $filter_user_id; ?>">
                    <input type="hidden" name="tanggal_awal" value="<?php echo $filter_tanggal_awal; ?>">
                    <input type="hidden" name="tanggal_akhir" value="<?php echo $filter_tanggal_akhir; ?>">
                    <input type="hidden" name="jenis_absen" value="<?php echo $filter_jenis_absen; ?>">
                    <input type="hidden" name="status" value="<?php echo $filter_status; ?>">
                    <button type="submit" name="export" class="btn btn-success">
                        <i class="fas fa-file-export me-1"></i> Export ke CSV
                    </button>
                </form>
            </div>
            
            <div class="alert alert-info">
                <p><strong>Periode:</strong> <?php echo date('d/m/Y', strtotime($filter_tanggal_awal)); ?> s/d <?php echo date('d/m/Y', strtotime($filter_tanggal_akhir)); ?></p>
                <?php if (!empty($filter_user_id)): ?>
                <p><strong>Karyawan:</strong> <?php echo getUserName($conn, $filter_user_id); ?></p>
                <?php endif; ?>
                <?php if (!empty($filter_jenis_absen)): ?>
                <p><strong>Jenis Absen:</strong> <?php echo $filter_jenis_absen == 'masuk' ? 'Masuk' : 'Keluar'; ?></p>
                <?php endif; ?>
                <?php if (!empty($filter_status)): ?>
                <p><strong>Status:</strong> 
                    <?php 
                    if ($filter_status == 'pending') echo 'Pending';
                    else if ($filter_status == 'approved') echo 'Disetujui';
                    else echo 'Ditolak';
                    ?>
                </p>
                <?php endif; ?>
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-datatable" id="absensiOfflineTable">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Tanggal</th>
                            <th>Jam</th>
                            <th>Jenis Absen</th>
                            <th>Status</th>
                            <th>Keterangan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Query untuk mengambil data absensi offline
                        $query = "SELECT ao.*, u.nik, u.nama 
                                  FROM absensi_offline ao
                                  LEFT JOIN users u ON ao.user_id = u.id
                                  WHERE 1=1";
                        
                        // Tambahkan filter
                        if (!empty($filter_user_id)) {
                            $query .= " AND ao.user_id = '$filter_user_id'";
                        }
                        
                        if (!empty($filter_tanggal_awal)) {
                            $query .= " AND ao.tanggal >= '$filter_tanggal_awal'";
                        }
                        
                        if (!empty($filter_tanggal_akhir)) {
                            $query .= " AND ao.tanggal <= '$filter_tanggal_akhir'";
                        }
                        
                        if (!empty($filter_jenis_absen)) {
                            $query .= " AND ao.jenis_absen = '$filter_jenis_absen'";
                        }
                        
                        if (!empty($filter_status)) {
                            $query .= " AND ao.status = '$filter_status'";
                        }
                        
                        $query .= " ORDER BY ao.tanggal DESC, ao.jam DESC";
                        $result = mysqli_query($conn, $query);
                        
                        $no = 1;
                        while ($row = mysqli_fetch_assoc($result)):
                        ?>
                        <tr>
                            <td><?php echo $no++; ?></td>
                            <td><?php echo $row['nik']; ?></td>
                            <td><?php echo $row['nama']; ?></td>
                            <td><?php echo date('d/m/Y', strtotime($row['tanggal'])); ?></td>
                            <td><?php echo date('H:i:s', strtotime($row['jam'])); ?></td>
                            <td>
                                <?php if ($row['jenis_absen'] == 'masuk'): ?>
                                    <span class="badge bg-primary">Masuk</span>
                                <?php else: ?>
                                    <span class="badge bg-info">Keluar</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($row['status'] == 'pending'): ?>
                                    <span class="badge bg-warning">Pending</span>
                                <?php elseif ($row['status'] == 'approved'): ?>
                                    <span class="badge bg-success">Disetujui</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Ditolak</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo $row['keterangan'] ?? '-'; ?></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';

// Fungsi untuk mendapatkan nama karyawan berdasarkan ID
function getUserName($conn, $user_id) {
    $query = "SELECT nik, nama FROM users WHERE id = '$user_id'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        return $user['nik'] . ' - ' . $user['nama'];
    }
    return 'Tidak Ada';
}
?>
