<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Pesan untuk ditampilkan
$message = '';

// Cek apakah tabel aktivitas_karyawan sudah ada
$query = "SHOW TABLES LIKE 'aktivitas_karyawan'";
$result = mysqli_query($conn, $query);
if (mysqli_num_rows($result) == 0) {
    // Buat tabel aktivitas_karyawan jika belum ada
    $query = "CREATE TABLE IF NOT EXISTS `aktivitas_karyawan` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `tanggal` date NOT NULL,
        `waktu` time NOT NULL,
        `longitude` varchar(50) NOT NULL,
        `latitude` varchar(50) NOT NULL,
        `alamat` text DEFAULT NULL,
        `status` enum('di dalam radius','di luar radius') NOT NULL DEFAULT 'di luar radius',
        `jarak_dari_kantor` float DEFAULT NULL,
        `lokasi_id` int(11) DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `lokasi_id` (`lokasi_id`),
        CONSTRAINT `aktivitas_karyawan_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT `aktivitas_karyawan_ibfk_2` FOREIGN KEY (`lokasi_id`) REFERENCES `lokasi` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if (mysqli_query($conn, $query)) {
        $message .= '<div class="alert alert-success">Tabel aktivitas_karyawan berhasil dibuat!</div>';
    } else {
        $message .= '<div class="alert alert-danger">Gagal membuat tabel aktivitas_karyawan: ' . mysqli_error($conn) . '</div>';
    }
}

// Proses generate data sampel
if (isset($_POST['generate'])) {
    $num_records = isset($_POST['num_records']) ? (int)$_POST['num_records'] : 10;
    
    // Ambil data karyawan
    $query = "SELECT id FROM users WHERE role = 'karyawan'";
    $result = mysqli_query($conn, $query);
    $karyawan = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $karyawan[] = $row;
    }
    
    // Ambil data lokasi
    $query = "SELECT * FROM lokasi";
    $result = mysqli_query($conn, $query);
    $lokasi = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $lokasi[] = $row;
    }
    
    // Jika tidak ada karyawan atau lokasi, tampilkan pesan error
    if (empty($karyawan)) {
        $message = '<div class="alert alert-danger">Tidak ada data karyawan!</div>';
    } else if (empty($lokasi)) {
        $message = '<div class="alert alert-danger">Tidak ada data lokasi!</div>';
    } else {
        // Generate data sampel
        $success_count = 0;
        
        // Tanggal untuk data sampel (7 hari terakhir)
        $dates = [];
        for ($i = 0; $i < 7; $i++) {
            $dates[] = date('Y-m-d', strtotime("-$i days"));
        }
        
        // Loop untuk setiap karyawan
        foreach ($karyawan as $k) {
            // Loop untuk setiap tanggal
            foreach ($dates as $date) {
                // Loop untuk beberapa waktu dalam sehari
                $times = ['08:00:00', '10:30:00', '13:00:00', '15:30:00', '17:00:00'];
                foreach ($times as $time) {
                    // Pilih lokasi secara acak
                    $lokasi_index = array_rand($lokasi);
                    $lokasi_item = $lokasi[$lokasi_index];
                    
                    // Generate koordinat acak di sekitar lokasi
                    $radius_meters = rand(50, 500); // Jarak acak dari lokasi (50-500 meter)
                    $angle = rand(0, 360) * (M_PI / 180); // Sudut acak dalam radian
                    
                    // Konversi radius dari meter ke derajat (perkiraan)
                    $radius_lat = $radius_meters / 111000; // 1 derajat latitude ~ 111 km
                    $radius_lng = $radius_meters / (111000 * cos(deg2rad($lokasi_item['latitude']))); // Sesuaikan dengan latitude
                    
                    // Hitung koordinat baru
                    $latitude = $lokasi_item['latitude'] + $radius_lat * sin($angle);
                    $longitude = $lokasi_item['longitude'] + $radius_lng * cos($angle);
                    
                    // Tentukan status berdasarkan jarak
                    $jarak = hitungJarak($latitude, $longitude, $lokasi_item['latitude'], $lokasi_item['longitude']);
                    $status = ($jarak <= $lokasi_item['radius']) ? 'di dalam radius' : 'di luar radius';
                    
                    // Insert data ke database
                    $query = "INSERT INTO aktivitas_karyawan (user_id, tanggal, waktu, longitude, latitude, status, jarak_dari_kantor, lokasi_id)
                              VALUES ('{$k['id']}', '$date', '$time', '$longitude', '$latitude', '$status', '$jarak', '{$lokasi_item['id']}')";
                    
                    if (mysqli_query($conn, $query)) {
                        $success_count++;
                    }
                    
                    // Jika sudah mencapai jumlah record yang diminta, hentikan
                    if ($success_count >= $num_records) {
                        break 3; // Keluar dari semua loop
                    }
                }
            }
        }
        
        $message = '<div class="alert alert-success">Berhasil membuat ' . $success_count . ' data aktivitas karyawan.</div>';
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Generate Data Sampel Aktivitas Karyawan</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="aktivitas_karyawan.php">Aktivitas Karyawan</a></li>
        <li class="breadcrumb-item active">Generate Data Sampel</li>
    </ol>
    
    <?php echo $message; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-database me-1"></i>
            Generate Data Sampel
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="mb-3">
                    <label for="num_records" class="form-label">Jumlah Data</label>
                    <input type="number" class="form-control" id="num_records" name="num_records" value="50" min="1" max="1000">
                    <div class="form-text">Jumlah data aktivitas karyawan yang akan dibuat.</div>
                </div>
                <button type="submit" name="generate" class="btn btn-primary">
                    <i class="fas fa-cogs me-1"></i> Generate Data
                </button>
                <a href="aktivitas_karyawan.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Kembali
                </a>
            </form>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Informasi
        </div>
        <div class="card-body">
            <p>Halaman ini digunakan untuk membuat data sampel aktivitas karyawan. Data yang dibuat meliputi:</p>
            <ul>
                <li>Tanggal dan waktu aktivitas (7 hari terakhir)</li>
                <li>Koordinat lokasi (latitude dan longitude)</li>
                <li>Status (di dalam radius atau di luar radius)</li>
                <li>Jarak dari lokasi kantor</li>
            </ul>
            <p>Data ini akan digunakan untuk menampilkan titik-titik lokasi aktivitas karyawan pada peta.</p>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
