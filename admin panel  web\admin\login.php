<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/auth.php';

// Cek jika sudah login sebagai admin
if (isLoggedIn() && isAdmin()) {
    redirect('admin/index.php');
}

// Proses login
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nik = $_POST['nik'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($nik) || empty($password)) {
        setMessage('danger', 'NIK dan Password harus diisi!');
    } else {
        // Set role admin untuk login
        $_GET['role'] = 'admin';

        // Cek apakah device diblokir sebelum login
        $device_id = $_SESSION['device_id'] ?? ($_COOKIE['device_id'] ?? null);
        if (!$device_id) {
            $device_id = md5($_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);
            // Simpan device_id di cookie selama 1 tahun
            setcookie('device_id', $device_id, time() + 60 * 60 * 24 * 365, '/');
        }

        // Cek apakah device diblokir (untuk admin, kita tetap cek tapi tidak memblokir)
        $blocked = isDeviceBlocked($nik, $device_id);
        if ($blocked) {
            // Tampilkan peringatan tapi tetap izinkan login untuk admin
            $pesan = "Perhatian: Device atau NIK Anda terdeteksi diblokir.<br>";
            $pesan .= "<strong>Alasan:</strong> " . $blocked['alasan'] . "<br>";
            $pesan .= "<strong>Tanggal Blokir:</strong> " . $blocked['tanggal_blokir'] . "<br>";
            $pesan .= "Namun, Anda tetap dapat login sebagai admin.";

            // Set pesan warning
            setMessage('warning', $pesan);
        }

        // Set flag bahwa device sudah dicek
        $_SESSION['device_checked'] = true;

        if (login($nik, $password)) {
            // Redirect dilakukan di fungsi login
        } else {
            setMessage('danger', 'NIK atau Password salah!');
        }
    }
}

// Include header
include_once '../includes/header.php';
?>

<style>
    /* Styling untuk pesan blokir */
    .alert strong {
        font-weight: 600;
        color: #333;
    }

    .alert br {
        margin-bottom: 5px;
        display: block;
        content: "";
    }

    .alert-danger strong {
        color: #e74a3b;
    }
</style>

<div class="container">
    <div class="row justify-content-center mt-5">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header text-center">
                    <h4><?php echo APP_NAME; ?> - Login Admin</h4>
                </div>
                <div class="card-body">
                    <?php
                    // Tampilkan pesan jika ada
                    $message = getMessage();
                    if ($message): ?>
                        <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                            <?php echo $message['text']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="nik" class="form-label">NIK Admin</label>
                            <input type="text" class="form-control" id="nik" name="nik" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary">Login Admin</button>
                        </div>
                    </form>

                    <div class="text-center mt-3">
                        <a href="<?php echo BASE_URL; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-user"></i> Login Karyawan
                        </a>
                    </div>

                    <div class="text-center mt-3">
                        <a href="<?php echo BASE_URL; ?>help.php" class="btn btn-link">
                            <i class="fas fa-question-circle"></i> Bantuan
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
