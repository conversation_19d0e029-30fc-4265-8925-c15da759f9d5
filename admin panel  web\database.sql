-- Buat database
CREATE DATABASE IF NOT EXISTS absensiku;

-- Gunakan database
USE absensiku;

-- Tabel users (admin dan karyawan)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nik VARCHAR(20) NOT NULL UNIQUE,
    nama VARCHAR(100) NOT NULL,
    bidang_id INT DEFAULT NULL,
    jabatan VARCHAR(100) DEFAULT NULL,
    lokasi_id INT DEFAULT NULL,
    foto_profil VARCHAR(255) DEFAULT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'karyawan') NOT NULL DEFAULT 'karyawan',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (bidang_id) REFERENCES bidang(id) ON DELETE SET NULL
);

-- Tabel lokasi
CREATE TABLE IF NOT EXISTS lokasi (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_lokasi VARCHAR(100) NOT NULL UNIQUE,
    longitude VARCHAR(50) NOT NULL,
    latitude VARCHAR(50) NOT NULL,
    radius INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel bidang
CREATE TABLE IF NOT EXISTS bidang (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_bidang VARCHAR(100) NOT NULL UNIQUE,
    keterangan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel jam_kerja
CREATE TABLE IF NOT EXISTS jam_kerja (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bidang_id INT NOT NULL,
    nama_jam_kerja VARCHAR(100) NOT NULL,
    awal_jam_masuk TIME NOT NULL,
    jam_masuk TIME NOT NULL,
    akhir_jam_masuk TIME NOT NULL,
    jam_pulang TIME NOT NULL,
    akhir_jam_pulang TIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (bidang_id) REFERENCES bidang(id) ON DELETE CASCADE
);

-- Tabel hari_kerja
CREATE TABLE IF NOT EXISTS hari_kerja (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bidang_id INT NOT NULL,
    hari ENUM('Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu') NOT NULL,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (bidang_id) REFERENCES bidang(id) ON DELETE CASCADE,
    UNIQUE KEY (bidang_id, hari)
);

-- Tabel jam_kerja_bidang
CREATE TABLE IF NOT EXISTS jam_kerja_bidang (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bidang_id INT NOT NULL,
    hari ENUM('Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu') NOT NULL,
    jam_kerja_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (bidang_id) REFERENCES bidang(id) ON DELETE CASCADE,
    FOREIGN KEY (jam_kerja_id) REFERENCES jam_kerja(id) ON DELETE SET NULL,
    UNIQUE KEY (bidang_id, hari)
);

-- Tabel denda
CREATE TABLE IF NOT EXISTS denda (
    id INT AUTO_INCREMENT PRIMARY KEY,
    denda_masuk DECIMAL(10, 2) NOT NULL,
    denda_pulang DECIMAL(10, 2) NOT NULL,
    denda_tidak_absen DECIMAL(10, 2) NOT NULL,
    denda_tidak_absen_pulang DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel izin_dinas
CREATE TABLE IF NOT EXISTS izin_dinas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    tanggal_mulai DATE NOT NULL,
    tanggal_selesai DATE NOT NULL,
    tujuan VARCHAR(255) NOT NULL,
    keterangan TEXT,
    status ENUM('Pending', 'Approved', 'Rejected') NOT NULL DEFAULT 'Pending',
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Tabel hari_libur
CREATE TABLE IF NOT EXISTS hari_libur (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_libur VARCHAR(100) NOT NULL,
    tanggal DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel presensi
CREATE TABLE IF NOT EXISTS presensi (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    tanggal DATE NOT NULL,
    jam_masuk TIME NOT NULL,
    foto_masuk VARCHAR(255) NOT NULL,
    lokasi_masuk VARCHAR(255) NOT NULL,
    jam_pulang TIME DEFAULT NULL,
    foto_pulang VARCHAR(255) DEFAULT NULL,
    lokasi_pulang VARCHAR(255) DEFAULT NULL,
    status ENUM('Tepat Waktu', 'Terlambat', 'Pulang Awal', 'Lembur') NOT NULL,
    keterangan TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabel deteksi_wajah
CREATE TABLE IF NOT EXISTS deteksi_wajah (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    foto_wajah VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tambahkan admin default
INSERT INTO users (nik, nama, bidang_id, password, role) VALUES
('admin', 'Administrator', 1, '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');
-- Password: password

-- Tambahkan data denda default
INSERT INTO denda (denda_masuk, denda_pulang, denda_tidak_absen, denda_tidak_absen_pulang) VALUES
(10000, 10000, 50000, 25000);

-- Tambahkan data bidang default
INSERT INTO bidang (nama_bidang, keterangan) VALUES
('Umum', 'Bidang umum untuk semua karyawan'),
('IT', 'Bidang teknologi informasi'),
('HRD', 'Bidang sumber daya manusia'),
('Keuangan', 'Bidang keuangan dan akuntansi'),
('Marketing', 'Bidang pemasaran');

-- Tambahkan data jam kerja default
INSERT INTO jam_kerja (bidang_id, nama_jam_kerja, awal_jam_masuk, jam_masuk, akhir_jam_masuk, jam_pulang, akhir_jam_pulang) VALUES
(1, 'Jam Kerja Normal', '07:30:00', '08:00:00', '09:00:00', '17:00:00', '18:00:00'),
(2, 'Jam Kerja IT', '08:30:00', '09:00:00', '10:00:00', '18:00:00', '19:00:00');

-- Tambahkan data hari kerja default untuk bidang Umum
INSERT INTO hari_kerja (bidang_id, hari, status) VALUES
(1, 'Senin', TRUE),
(1, 'Selasa', TRUE),
(1, 'Rabu', TRUE),
(1, 'Kamis', TRUE),
(1, 'Jumat', TRUE),
(1, 'Sabtu', FALSE),
(1, 'Minggu', FALSE);

-- Tambahkan data hari kerja default untuk bidang IT
INSERT INTO hari_kerja (bidang_id, hari, status) VALUES
(2, 'Senin', TRUE),
(2, 'Selasa', TRUE),
(2, 'Rabu', TRUE),
(2, 'Kamis', TRUE),
(2, 'Jumat', TRUE),
(2, 'Sabtu', FALSE),
(2, 'Minggu', FALSE);

-- Tambahkan data jam kerja bidang default untuk bidang Umum
INSERT INTO jam_kerja_bidang (bidang_id, hari, jam_kerja_id) VALUES
(1, 'Senin', 1),
(1, 'Selasa', 1),
(1, 'Rabu', 1),
(1, 'Kamis', 1),
(1, 'Jumat', 1),
(1, 'Sabtu', NULL),
(1, 'Minggu', NULL);

-- Tambahkan data jam kerja bidang default untuk bidang IT
INSERT INTO jam_kerja_bidang (bidang_id, hari, jam_kerja_id) VALUES
(2, 'Senin', 2),
(2, 'Selasa', 2),
(2, 'Rabu', 2),
(2, 'Kamis', 2),
(2, 'Jumat', 2),
(2, 'Sabtu', NULL),
(2, 'Minggu', NULL);

-- Tambahkan data lokasi contoh
INSERT INTO lokasi (nama_lokasi, longitude, latitude, radius) VALUES
('Kantor Pusat', '106.8456', '-6.2088', 100),
('Kantor Cabang', '106.8256', '-6.1751', 100);
