// Script untuk menghapus Service Worker yang ada
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for (let registration of registrations) {
      registration.unregister();
      console.log('Service Worker berhasil dihapus');
    }
    // Hapus semua cache
    if ('caches' in window) {
      caches.keys().then(function(cacheNames) {
        return Promise.all(
          cacheNames.map(function(cacheName) {
            return caches.delete(cacheName);
          })
        );
      }).then(function() {
        console.log('Semua cache berhasil dihapus');
        // Reload halaman setelah semua cache dihapus
        window.location.reload();
      });
    }
  });
}
