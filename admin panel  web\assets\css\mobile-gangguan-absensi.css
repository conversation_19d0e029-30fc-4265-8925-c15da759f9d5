/* Mobile Gangguan Absensi Style */

/* Base Styles */
.mobile-app-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
    padding-bottom: 80px;
}

@media (min-width: 768px) {
    .mobile-app-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-app-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-app-container {
        max-width: 840px;
    }
}

.mobile-app-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.mobile-app-header .left-section {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.mobile-app-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    margin-right: 15px;
}

.mobile-app-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
}

.mobile-app-header .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mobile-app-header .user-details {
    flex: 1;
}

.mobile-app-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.mobile-app-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.mobile-app-header .date-info {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 14px;
    align-self: flex-start;
}

.mobile-app-header .date-info i {
    margin-right: 5px;
}

.mobile-app-header .logout-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.mobile-app-header .logout-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Page Title */
.page-title-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 15px;
}

.back-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8f9fa;
    color: #4e73df;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    text-decoration: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.back-button:hover {
    background-color: #4e73df;
    color: white;
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: #333;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
}

.card-header h6 {
    font-weight: 600;
    color: #333;
}

.card-body {
    padding: 20px;
}

/* Form Styles */
.form-label {
    font-weight: 500;
    color: #555;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border-radius: 10px;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.form-text {
    font-size: 12px;
    color: #6c757d;
}

.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
}

/* List Group Styles */
.list-group-item {
    border-radius: 10px;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.list-group-item:last-child {
    margin-bottom: 0;
}

.list-group-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.badge {
    padding: 6px 10px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 12px;
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
    overflow: hidden;
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.modal-title {
    font-weight: 600;
    color: #333;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .mobile-app-container {
        max-width: 100%;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .mobile-app-header {
        padding: 15px;
    }
    
    .mobile-app-header .user-avatar {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .mobile-app-header .user-name {
        font-size: 16px;
    }
    
    .mobile-app-header .user-position {
        font-size: 12px;
    }
}
