<?php
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek apakah user sudah login
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit();
}

// Cek apakah user adalah karyawan
if ($_SESSION['role'] != 'karyawan') {
    header('Location: ../index.php');
    exit();
}

// Ambil data user
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = '$user_id'";
$result = mysqli_query($GLOBALS['conn'], $query);
$user = mysqli_fetch_assoc($result);

// Koneksi database
$conn = $GLOBALS['conn'];

// Cek apakah hari ini sudah ada presensi masuk
$today = date('Y-m-d');
$query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result = mysqli_query($conn, $query);
$presensi_hari_ini = mysqli_fetch_assoc($result);

// Cek apakah sudah ada izin dinas untuk hari ini
$query = "SELECT * FROM izin_dinas
          WHERE user_id = '$user_id'
          AND tanggal_mulai <= '$today'
          AND tanggal_selesai >= '$today'
          AND status != 'Rejected'";
$result = mysqli_query($conn, $query);
$izin_dinas_hari_ini = mysqli_fetch_assoc($result);

// Redirect jika belum absen masuk atau sudah absen pulang atau sudah ada izin dinas
if (!$presensi_hari_ini || empty($presensi_hari_ini['jam_masuk']) || !empty($presensi_hari_ini['jam_pulang']) || $izin_dinas_hari_ini) {
    // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
    $_SESSION['swal_type'] = 'error';
    $_SESSION['swal_title'] = 'Tidak Dapat Mengajukan';

    if (!$presensi_hari_ini || empty($presensi_hari_ini['jam_masuk'])) {
        $_SESSION['swal_text'] = 'Anda belum melakukan absen masuk hari ini!';
    } else if (!empty($presensi_hari_ini['jam_pulang'])) {
        $_SESSION['swal_text'] = 'Anda sudah melakukan absen pulang hari ini!';
    } else if ($izin_dinas_hari_ini) {
        $_SESSION['swal_text'] = 'Anda sudah memiliki izin perjalanan dinas untuk hari ini!';
    }

    header('Location: izin_dinas.php');
    exit();
}

// Proses pengajuan perjalanan dinas hari ini
if (isset($_POST['ajukan'])) {
    $tujuan = clean($_POST['tujuan']);
    $keterangan = clean($_POST['keterangan']);
    $tanggal_mulai = date('Y-m-d');
    $tanggal_selesai = date('Y-m-d');

    // Validasi foto surat tugas
    if (empty($_POST['foto_surat_tugas'])) {
        // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'error';
        $_SESSION['swal_title'] = 'Pengajuan Gagal';
        $_SESSION['swal_text'] = 'Foto surat tugas harus diambil!';
        header('Location: perjalanan_dinas_hari_ini.php');
        exit();
    }

    // Validasi foto wajah
    if (empty($_POST['foto_wajah'])) {
        // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'error';
        $_SESSION['swal_title'] = 'Pengajuan Gagal';
        $_SESSION['swal_text'] = 'Foto wajah harus diambil!';
        header('Location: perjalanan_dinas_hari_ini.php');
        exit();
    }

    // Proses foto surat tugas dari data base64
    if (isset($_POST['foto_surat_tugas']) && !empty($_POST['foto_surat_tugas'])) {
        $foto_data = $_POST['foto_surat_tugas'];

        // Hapus header data URI jika ada
        if (strpos($foto_data, 'data:image/jpeg;base64,') === 0) {
            $foto_data = substr($foto_data, strlen('data:image/jpeg;base64,'));
        } elseif (strpos($foto_data, 'data:image/png;base64,') === 0) {
            $foto_data = substr($foto_data, strlen('data:image/png;base64,'));
        }

        // Decode base64
        $foto_binary = base64_decode($foto_data);

        if ($foto_binary === false) {
            // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
            $_SESSION['swal_type'] = 'error';
            $_SESSION['swal_title'] = 'Pengajuan Gagal';
            $_SESSION['swal_text'] = 'Format foto surat tugas tidak valid!';
            header('Location: perjalanan_dinas_hari_ini.php');
            exit();
        }

        // Simpan foto surat tugas
        $upload_dir = '../uploads/';
        $file_name = "surat_tugas_{$user_id}_" . time() . '.jpg';
        $file_path = "{$upload_dir}{$file_name}";

        // Cek apakah direktori uploads ada, jika tidak buat direktori
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Simpan file
        if (file_put_contents($file_path, $foto_binary)) {
            $file_surat_db = $file_name;
        } else {
            // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
            $_SESSION['swal_type'] = 'error';
            $_SESSION['swal_title'] = 'Pengajuan Gagal';
            $_SESSION['swal_text'] = 'Gagal menyimpan foto surat tugas!';
            header('Location: perjalanan_dinas_hari_ini.php');
            exit();
        }
    } else {
        // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'error';
        $_SESSION['swal_title'] = 'Pengajuan Gagal';
        $_SESSION['swal_text'] = 'Foto surat tugas wajib diambil!';
        header('Location: perjalanan_dinas_hari_ini.php');
        exit();
    }

    // Proses foto wajah dari data base64
    if (isset($_POST['foto_wajah']) && !empty($_POST['foto_wajah'])) {
        $foto_data = $_POST['foto_wajah'];

        // Hapus header data URI jika ada
        if (strpos($foto_data, 'data:image/jpeg;base64,') === 0) {
            $foto_data = substr($foto_data, strlen('data:image/jpeg;base64,'));
        } elseif (strpos($foto_data, 'data:image/png;base64,') === 0) {
            $foto_data = substr($foto_data, strlen('data:image/png;base64,'));
        }

        // Decode base64
        $foto_binary = base64_decode($foto_data);

        if ($foto_binary === false) {
            // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
            $_SESSION['swal_type'] = 'error';
            $_SESSION['swal_title'] = 'Pengajuan Gagal';
            $_SESSION['swal_text'] = 'Format foto wajah tidak valid!';
            header('Location: perjalanan_dinas_hari_ini.php');
            exit();
        }

        // Simpan foto wajah
        $upload_dir = '../uploads/';
        $file_name = "wajah_izin_{$user_id}_" . time() . '.jpg';
        $file_path = "{$upload_dir}{$file_name}";

        // Cek apakah direktori uploads ada, jika tidak buat direktori
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Simpan file
        if (file_put_contents($file_path, $foto_binary)) {
            $file_wajah_db = $file_name;
        } else {
            // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
            $_SESSION['swal_type'] = 'error';
            $_SESSION['swal_title'] = 'Pengajuan Gagal';
            $_SESSION['swal_text'] = 'Gagal menyimpan foto wajah!';
            header('Location: perjalanan_dinas_hari_ini.php');
            exit();
        }
    } else {
        // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'error';
        $_SESSION['swal_title'] = 'Pengajuan Gagal';
        $_SESSION['swal_text'] = 'Foto wajah wajib diambil!';
        header('Location: perjalanan_dinas_hari_ini.php');
        exit();
    }

    // Simpan data izin dinas
    $query = "INSERT INTO izin_dinas (user_id, tanggal_mulai, tanggal_selesai, tujuan, keterangan, foto_surat_tugas, foto_wajah, status, created_at)
              VALUES ('$user_id', '$tanggal_mulai', '$tanggal_selesai', '$tujuan', '$keterangan', '$file_surat_db', '$file_wajah_db', 'Pending', NOW())";

    if (mysqli_query($conn, $query)) {
        // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'success';
        $_SESSION['swal_title'] = 'Pengajuan Berhasil';
        $_SESSION['swal_text'] = 'Perjalanan dinas hari ini berhasil diajukan dan menunggu persetujuan admin.';
        header('Location: izin_dinas.php');
        exit();
    } else {
        // Simpan pesan dalam session untuk ditampilkan sebagai SweetAlert
        $_SESSION['swal_type'] = 'error';
        $_SESSION['swal_title'] = 'Pengajuan Gagal';
        $_SESSION['swal_text'] = 'Terjadi kesalahan saat menyimpan data. Silakan coba lagi.';
        header('Location: perjalanan_dinas_hari_ini.php');
        exit();
    }
}
?>

<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('karyawan');

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Ambil data presensi hari ini
$today = date('Y-m-d');
$query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result = mysqli_query($conn, $query);
$presensi_hari_ini = mysqli_fetch_assoc($result);

// Variabel untuk menentukan tampilan mobile
$is_mobile_izin_dinas = true;

// Tambahkan CSS khusus untuk halaman ini
echo '<link rel="stylesheet" href="' . BASE_URL . 'assets/css/mobile-izin-dinas-hari-ini.css">';

// Tambahkan CSS untuk panduan wajah
echo '<style>
    /* Face Guide Styles */
    .face-guide-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: none;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;
        pointer-events: none;
    }

    .face-guide {
        width: 200px;
        height: 200px;
        border: 3px dashed rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        margin-bottom: 10px;
    }

    .face-guide-instructions {
        color: white;
        background-color: rgba(0, 0, 0, 0.7);
        padding: 8px 12px;
        border-radius: 5px;
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        max-width: 80%;
        margin-top: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    /* Ensure camera preview has correct positioning */
    .camera-preview {
        width: 100%;
        height: 350px;
        background-color: #343a40;
        border-radius: 15px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .camera-preview video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 15px;
        transform: scaleX(-1); /* Membalik video secara horizontal agar seperti cermin */
    }

    /* Untuk kamera belakang, jangan flip */
    #video-surat {
        transform: scaleX(1);
    }

    /* Perbaikan untuk Android WebView */
    video {
        background-color: #000; /* Latar belakang hitam untuk video */
        object-fit: cover !important; /* Pastikan video menutupi area */
        -webkit-transform-style: preserve-3d; /* Perbaikan untuk beberapa perangkat */
        transform-style: preserve-3d;
        z-index: 1; /* Pastikan video di atas elemen lain */
    }

    /* Pastikan canvas juga memiliki z-index yang tepat */
    canvas {
        z-index: 2; /* Canvas harus di atas video saat ditampilkan */
    }

    /* Perbaikan untuk beberapa perangkat Android */
    .camera-preview {
        -webkit-transform: translateZ(0); /* Hardware acceleration */
        transform: translateZ(0);
        backface-visibility: hidden;
        perspective: 1000px;
    }

    /* Pesan kamera */
    .camera-message {
        position: absolute;
        bottom: 10px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 12px;
        opacity: 0.5;
        z-index: 10;
        padding: 3px;
    }
</style>';

// Gunakan header tanpa sidebar
$no_sidebar = true;
include_once '../includes/header.php';
?>

<div class="mobile-izin-container">
    <!-- Header Section -->
    <div class="izin-header">
        <div class="user-info">
            <div class="user-avatar">
                <?php if (!empty($user['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $user['foto_profil']; ?>" alt="Foto Profil">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                <div class="user-position"><?php echo $user['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?>
        </div>
    </div>

    <!-- Navigation -->
    <div class="izin-navigation">
        <a href="izin_dinas.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="page-title">Ajukan Perjalanan Dinas Hari Ini</div>
    </div>

    <!-- Container -->
    <div class="container px-0">
        <?php if (isset($presensi_hari_ini) && $presensi_hari_ini): ?>
        <div class="info-section mb-4">
            <div class="info-title"><i class="fas fa-info-circle"></i> Informasi Penting</div>
            <div class="info-content">
                <div class="alert alert-info mb-0">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-3 fa-lg"></i>
                        <div>
                            <h6 class="mb-1 fw-bold">Anda sudah melakukan absen masuk hari ini</h6>
                            <p class="mb-0">Pada pukul <strong><?php echo $presensi_hari_ini ? $presensi_hari_ini['jam_masuk'] : ''; ?></strong>. Dengan mengajukan perjalanan dinas hari ini, Anda tidak perlu melakukan absen pulang secara manual.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="izin-card">
            <h5 class="card-title mb-4 fw-bold text-primary"><i class="fas fa-plane-departure me-2"></i>Form Perjalanan Dinas</h5>

            <form method="post" action="" enctype="multipart/form-data" id="formDinasHariIni">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label for="tujuan" class="form-label">Tujuan Perjalanan Dinas <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-map-marker-alt text-primary"></i></span>
                                <input type="text" class="form-control" id="tujuan" name="tujuan" placeholder="Contoh: Jakarta, Surabaya, dll." required>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="keterangan" class="form-label">Keterangan Tambahan</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-align-left text-primary"></i></span>
                                <textarea class="form-control" id="keterangan" name="keterangan" rows="4" placeholder="Contoh: Rapat koordinasi, Pelatihan, dll."></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label">Foto Surat Tugas <span class="text-danger">*</span></label>
                            <div class="camera-preview" id="camera-preview-surat">
                                <video id="video-surat" autoplay playsinline style="display: none; width: 100%; height: 100%; object-fit: cover;"></video>
                                <canvas id="canvas-surat" style="display: none; width: 100%; height: 100%;"></canvas>
                                <i class="fas fa-camera"></i>
                                <div class="camera-overlay"></div>
                                <div class="camera-message">Mengaktifkan kamera...</div>
                            </div>
                            <input type="hidden" name="foto_surat_tugas" id="foto_surat_tugas">
                            <div class="d-grid gap-2 mt-2">
                                <button type="button" id="btn-ambil-foto-surat" class="btn btn-primary">
                                    <i class="fas fa-camera me-2"></i> Ambil Foto Surat
                                </button>
                                <button type="button" id="btn-ulangi-foto-surat" class="btn btn-outline-secondary" style="display: none;">
                                    <i class="fas fa-redo me-2"></i> Ulangi Foto
                                </button>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Foto Wajah <span class="text-danger">*</span></label>
                            <div class="camera-preview" id="camera-preview-wajah">
                                <video id="video-wajah" autoplay playsinline style="display: none; width: 100%; height: 100%; object-fit: cover;"></video>
                                <canvas id="canvas-wajah" style="display: none; width: 100%; height: 100%;"></canvas>
                                <i class="fas fa-camera"></i>
                                <div class="camera-overlay"></div>
                                <div class="camera-message">Mengaktifkan kamera...</div>

                                <!-- Panduan Wajah -->
                                <div class="face-guide-overlay" id="face-guide-wajah">
                                    <div class="face-guide"></div>
                                    <div class="face-guide-instructions">
                                        Posisikan wajah Anda di dalam lingkaran dan pastikan wajah terlihat jelas
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="foto_wajah" id="foto_wajah">
                            <div class="d-grid gap-2 mt-2">
                                <button type="button" id="btn-ambil-foto-wajah" class="btn btn-primary">
                                    <i class="fas fa-camera me-2"></i> Ambil Foto Wajah
                                </button>
                                <button type="button" id="btn-ulangi-foto-wajah" class="btn btn-outline-secondary" style="display: none;">
                                    <i class="fas fa-redo me-2"></i> Ulangi Foto
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 mt-4">
                    <button type="submit" name="ajukan" class="btn btn-primary btn-lg" id="btn-ajukan">
                        <i class="fas fa-paper-plane me-2"></i> Ajukan Perjalanan Dinas
                    </button>
                </div>
            </form>
        </div>

        <!-- Bottom Spacing -->
        <div class="bottom-spacing"></div>
    </div>
</div>



<script>
    // Tampilkan SweetAlert jika ada pesan
    <?php if (isset($_SESSION['swal_type'])): ?>
    Swal.fire({
        icon: '<?php echo $_SESSION['swal_type']; ?>',
        title: '<?php echo $_SESSION['swal_title']; ?>',
        text: '<?php echo $_SESSION['swal_text']; ?>',
        confirmButtonColor: '<?php echo $_SESSION['swal_type'] == 'success' ? '#28a745' : '#dc3545'; ?>'
    });
    <?php
    unset($_SESSION['swal_type']);
    unset($_SESSION['swal_title']);
    unset($_SESSION['swal_text']);
    endif;
    ?>

        // Variabel untuk kamera
        let videoWajah = document.getElementById('video-wajah');
        let canvasWajah = document.getElementById('canvas-wajah');
        let cameraPreviewWajah = document.getElementById('camera-preview-wajah');
        let btnAmbilFotoWajah = document.getElementById('btn-ambil-foto-wajah');
        let btnUlangiFotoWajah = document.getElementById('btn-ulangi-foto-wajah');
        let fotoWajahInput = document.getElementById('foto_wajah');

        let videoSurat = document.getElementById('video-surat');
        let canvasSurat = document.getElementById('canvas-surat');
        let cameraPreviewSurat = document.getElementById('camera-preview-surat');
        let btnAmbilFotoSurat = document.getElementById('btn-ambil-foto-surat');
        let btnUlangiFotoSurat = document.getElementById('btn-ulangi-foto-surat');
        let fotoSuratTugasInput = document.getElementById('foto_surat_tugas');

        let streamWajah = null;
        let streamSurat = null;
        let fotoWajahTerambil = false;
        let fotoSuratTerambil = false;

        // Fungsi untuk mengaktifkan kamera wajah
        function startCameraWajah() {
            console.log('Memulai kamera wajah');

            // Perbarui pesan
            if (cameraPreviewWajah) {
                cameraPreviewWajah.querySelector('.camera-message').textContent = 'Meminta izin kamera...';
            }

            // Matikan kamera jika sudah aktif
            if (streamWajah) {
                streamWajah.getTracks().forEach(track => track.stop());
                streamWajah = null;
            }

            // Aktifkan kamera dengan pendekatan yang lebih kompatibel dengan Android 13+
            navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'user',
                    width: { ideal: 640 }, // Resolusi lebih rendah untuk kompatibilitas lebih baik
                    height: { ideal: 480 },
                    frameRate: { ideal: 15, max: 30 } // Batasi frame rate
                },
                audio: false
            }).then(function(stream) {
                console.log('Kamera wajah berhasil diaktifkan');
                streamWajah = stream;

                // Tampilkan video
                videoWajah.srcObject = stream;
                videoWajah.style.display = 'block';
                canvasWajah.style.display = 'none';

                if (cameraPreviewWajah.querySelector('i')) {
                    cameraPreviewWajah.querySelector('i').style.display = 'none';
                }

                if (cameraPreviewWajah.querySelector('.camera-message')) {
                    cameraPreviewWajah.querySelector('.camera-message').textContent = 'Kamera aktif.';
                }

                // Reset status foto
                fotoWajahTerambil = false;
                btnAmbilFotoWajah.style.display = 'block';
                btnUlangiFotoWajah.style.display = 'none';
                fotoWajahInput.value = '';

                // Mulai video
                videoWajah.play().then(function() {
                    console.log('Video wajah berhasil diputar');
                    // Tampilkan panduan wajah
                    adjustFaceGuideSize('video-wajah', 'face-guide-wajah');
                }).catch(function(error) {
                    console.error('Error playing video:', error);
                    videoWajah.muted = true;
                    videoWajah.play();
                });

            }).catch(function(error) {
                console.error('Error accessing camera:', error);

                // Coba fallback dengan constraint minimal
                console.log('Mencoba fallback dengan constraint minimal...');
                return navigator.mediaDevices.getUserMedia({
                    video: true, // Gunakan constraint minimal
                    audio: false
                }).then(function(stream) {
                    console.log('Kamera wajah berhasil diaktifkan dengan fallback');
                    streamWajah = stream;

                    // Tampilkan video
                    videoWajah.srcObject = stream;
                    videoWajah.style.display = 'block';
                    canvasWajah.style.display = 'none';

                    if (cameraPreviewWajah.querySelector('i')) {
                        cameraPreviewWajah.querySelector('i').style.display = 'none';
                    }

                    if (cameraPreviewWajah.querySelector('.camera-message')) {
                        cameraPreviewWajah.querySelector('.camera-message').textContent = 'Kamera aktif dengan mode kompatibilitas.';
                    }

                    // Reset status foto
                    fotoWajahTerambil = false;
                    btnAmbilFotoWajah.style.display = 'block';
                    btnUlangiFotoWajah.style.display = 'none';
                    fotoWajahInput.value = '';

                    // Mulai video
                    return videoWajah.play().then(function() {
                        console.log('Video wajah berhasil diputar dengan fallback');
                        // Tampilkan panduan wajah
                        adjustFaceGuideSize('video-wajah', 'face-guide-wajah');
                    }).catch(function(playError) {
                        console.error('Error playing video with fallback:', playError);
                        videoWajah.muted = true;
                        return videoWajah.play();
                    });
                }).catch(function(fallbackError) {
                    console.error('Fallback juga gagal:', fallbackError);

                    if (cameraPreviewWajah && cameraPreviewWajah.querySelector('.camera-message')) {
                        cameraPreviewWajah.querySelector('.camera-message').textContent = 'Gagal mengakses kamera. Pastikan kamera aktif dan izin diberikan.';
                    }

                    // Tampilkan error yang lebih detail
                    if (error.name === 'NotAllowedError' || fallbackError.name === 'NotAllowedError') {
                        alert('Akses kamera ditolak. Silakan berikan izin kamera di pengaturan browser Anda.');
                    } else if (error.name === 'NotFoundError' || fallbackError.name === 'NotFoundError') {
                        alert('Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera yang berfungsi.');
                    } else if (error.name === 'NotReadableError' || fallbackError.name === 'NotReadableError' ||
                              error.message.includes('Could not start video source') ||
                              fallbackError.message.includes('Could not start video source')) {
                        alert('Tidak dapat mengakses kamera. Ini mungkin karena kamera sedang digunakan oleh aplikasi lain atau ada masalah dengan perangkat Anda. Coba tutup aplikasi lain yang mungkin menggunakan kamera atau restart perangkat Anda.');
                    } else {
                        alert('Gagal mengakses kamera: ' + fallbackError.message);
                    }
                });
            });
        }

        // Fungsi untuk mengaktifkan kamera surat tugas
        function startCameraSurat() {
            console.log('Memulai kamera surat');

            // Perbarui pesan
            if (cameraPreviewSurat) {
                cameraPreviewSurat.querySelector('.camera-message').textContent = 'Meminta izin kamera...';
            }

            // Matikan kamera jika sudah aktif
            if (streamSurat) {
                streamSurat.getTracks().forEach(track => track.stop());
                streamSurat = null;
            }

            // Aktifkan kamera dengan pendekatan yang lebih kompatibel dengan Android 13+
            navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: { exact: 'environment' }, // Paksa menggunakan kamera belakang
                    width: { ideal: 640 }, // Resolusi lebih rendah untuk kompatibilitas lebih baik
                    height: { ideal: 480 },
                    frameRate: { ideal: 15, max: 30 } // Batasi frame rate
                },
                audio: false
            }).catch(function(err) {
                // Fallback jika exact environment gagal
                console.log('Gagal menggunakan kamera belakang dengan exact, mencoba alternatif:', err);
                return navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'environment', // Coba tanpa exact
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    },
                    audio: false
                });
            }).then(function(stream) {
                console.log('Kamera surat berhasil diaktifkan');
                streamSurat = stream;

                // Tampilkan video
                videoSurat.srcObject = stream;
                videoSurat.style.display = 'block';
                canvasSurat.style.display = 'none';

                if (cameraPreviewSurat.querySelector('i')) {
                    cameraPreviewSurat.querySelector('i').style.display = 'none';
                }

                if (cameraPreviewSurat.querySelector('.camera-message')) {
                    cameraPreviewSurat.querySelector('.camera-message').textContent = 'Kamera aktif. Posisikan surat tugas agar terlihat jelas.';
                }

                // Reset status foto
                fotoSuratTerambil = false;
                btnAmbilFotoSurat.style.display = 'block';
                btnUlangiFotoSurat.style.display = 'none';
                fotoSuratTugasInput.value = '';

                // Mulai video
                videoSurat.play().then(function() {
                    console.log('Video surat berhasil diputar');
                    // Tidak perlu menampilkan panduan untuk surat tugas
                }).catch(function(error) {
                    console.error('Error playing video:', error);
                    videoSurat.muted = true;
                    videoSurat.play();
                });

            }).catch(function(error) {
                console.error('Error accessing camera:', error);

                // Coba fallback dengan constraint minimal
                console.log('Mencoba fallback dengan constraint minimal untuk kamera surat...');
                return navigator.mediaDevices.getUserMedia({
                    video: true, // Gunakan constraint minimal
                    audio: false
                }).then(function(stream) {
                    console.log('Kamera surat berhasil diaktifkan dengan fallback');
                    streamSurat = stream;

                    // Tampilkan video
                    videoSurat.srcObject = stream;
                    videoSurat.style.display = 'block';
                    canvasSurat.style.display = 'none';

                    if (cameraPreviewSurat.querySelector('i')) {
                        cameraPreviewSurat.querySelector('i').style.display = 'none';
                    }

                    if (cameraPreviewSurat.querySelector('.camera-message')) {
                        cameraPreviewSurat.querySelector('.camera-message').textContent = 'Kamera aktif dengan mode kompatibilitas. Posisikan surat tugas agar terlihat jelas.';
                    }

                    // Reset status foto
                    fotoSuratTerambil = false;
                    btnAmbilFotoSurat.style.display = 'block';
                    btnUlangiFotoSurat.style.display = 'none';
                    fotoSuratTugasInput.value = '';

                    // Mulai video
                    return videoSurat.play().then(function() {
                        console.log('Video surat berhasil diputar dengan fallback');
                        // Tidak perlu menampilkan panduan untuk surat tugas
                    }).catch(function(playError) {
                        console.error('Error playing video with fallback:', playError);
                        videoSurat.muted = true;
                        return videoSurat.play();
                    });
                }).catch(function(fallbackError) {
                    console.error('Fallback juga gagal untuk kamera surat:', fallbackError);

                    if (cameraPreviewSurat && cameraPreviewSurat.querySelector('.camera-message')) {
                        cameraPreviewSurat.querySelector('.camera-message').textContent = 'Gagal mengakses kamera. Pastikan kamera aktif dan izin diberikan.';
                    }

                    // Tampilkan error yang lebih detail
                    if (error.name === 'NotAllowedError' || fallbackError.name === 'NotAllowedError') {
                        alert('Akses kamera ditolak. Silakan berikan izin kamera di pengaturan browser Anda.');
                    } else if (error.name === 'NotFoundError' || fallbackError.name === 'NotFoundError') {
                        alert('Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera yang berfungsi.');
                    } else if (error.name === 'NotReadableError' || fallbackError.name === 'NotReadableError' ||
                              error.message.includes('Could not start video source') ||
                              fallbackError.message.includes('Could not start video source')) {
                        alert('Tidak dapat mengakses kamera. Ini mungkin karena kamera sedang digunakan oleh aplikasi lain atau ada masalah dengan perangkat Anda. Coba tutup aplikasi lain yang mungkin menggunakan kamera atau restart perangkat Anda.');
                    } else {
                        alert('Gagal mengakses kamera: ' + fallbackError.message);
                    }
                });
            });
        }

        // Fungsi untuk menyesuaikan ukuran panduan wajah/dokumen
        function adjustFaceGuideSize(videoId, guideId) {
            const video = document.getElementById(videoId);
            const guide = document.getElementById(guideId);

            if (!video || !guide) return;

            const faceGuide = guide.querySelector('.face-guide');

            if (!faceGuide) return;

            // Dapatkan ukuran elemen video di layar
            const displayWidth = video.offsetWidth || video.clientWidth;
            const displayHeight = video.offsetHeight || video.clientHeight;

            // Gunakan ukuran terkecil untuk memastikan lingkaran sempurna
            const minDimension = Math.min(displayWidth, displayHeight);
            const size = minDimension * 0.7; // 70% dari dimensi terkecil

            // Terapkan ukuran yang sama untuk width dan height
            faceGuide.style.width = size + 'px';
            faceGuide.style.height = size + 'px';

            // Pastikan lingkaran berada di tengah frame kamera
            guide.style.display = 'flex';
            guide.style.alignItems = 'center';
            guide.style.justifyContent = 'center';
        }

        // Fungsi untuk mengambil foto wajah
        function ambilFotoWajah() {
            try {
                console.log('Mengambil foto wajah');

                // Cek apakah elemen canvas dan video ada
                if (!canvasWajah || !videoWajah) {
                    console.error('Elemen canvas atau video tidak ditemukan');
                    alert('Terjadi kesalahan saat mengambil foto. Silakan coba lagi.');
                    return;
                }

                // Cek apakah video sudah siap
                if (!videoWajah.videoWidth || !videoWajah.videoHeight) {
                    console.error('Video belum siap, width/height: ', videoWajah.videoWidth, videoWajah.videoHeight);
                    alert('Kamera belum siap. Silakan tunggu beberapa saat dan coba lagi.');
                    return;
                }

                // Ambil konteks canvas
                const context = canvasWajah.getContext('2d');

                // Set ukuran canvas sesuai video
                canvasWajah.width = videoWajah.videoWidth;
                canvasWajah.height = videoWajah.videoHeight;

                // Gambar frame video ke canvas
                // Flip gambar secara horizontal untuk mengembalikan efek cermin
                context.translate(canvasWajah.width, 0);
                context.scale(-1, 1);
                context.drawImage(videoWajah, 0, 0, canvasWajah.width, canvasWajah.height);

                // Kembalikan transformasi ke normal
                context.setTransform(1, 0, 0, 1, 0, 0);

                // Tampilkan canvas dan sembunyikan video
                videoWajah.style.display = 'none';
                canvasWajah.style.display = 'block';

                // Konversi canvas ke base64
                const dataURL = canvasWajah.toDataURL('image/jpeg', 0.8);
                fotoWajahInput.value = dataURL;

                console.log('Foto wajah berhasil diambil');

                // Update status foto
                fotoWajahTerambil = true;
                btnAmbilFotoWajah.style.display = 'none';
                btnUlangiFotoWajah.style.display = 'block';

                // Matikan kamera
                if (streamWajah) {
                    streamWajah.getTracks().forEach(track => track.stop());
                    streamWajah = null;
                }
            } catch (error) {
                console.error('Error saat mengambil foto wajah:', error);
                alert('Terjadi kesalahan saat mengambil foto: ' + error.message);
            }
        }

        // Fungsi untuk mengambil foto surat tugas
        function ambilFotoSurat() {
            try {
                console.log('Mengambil foto surat');

                // Cek apakah elemen canvas dan video ada
                if (!canvasSurat || !videoSurat) {
                    console.error('Elemen canvas atau video tidak ditemukan');
                    alert('Terjadi kesalahan saat mengambil foto. Silakan coba lagi.');
                    return;
                }

                // Cek apakah video sudah siap
                if (!videoSurat.videoWidth || !videoSurat.videoHeight) {
                    console.error('Video belum siap, width/height: ', videoSurat.videoWidth, videoSurat.videoHeight);
                    alert('Kamera belum siap. Silakan tunggu beberapa saat dan coba lagi.');
                    return;
                }

                // Ambil konteks canvas
                const context = canvasSurat.getContext('2d');

                // Set ukuran canvas sesuai video
                canvasSurat.width = videoSurat.videoWidth;
                canvasSurat.height = videoSurat.videoHeight;

                // Gambar frame video ke canvas (tidak perlu flip untuk kamera belakang)
                context.drawImage(videoSurat, 0, 0, canvasSurat.width, canvasSurat.height);

                // Tampilkan canvas dan sembunyikan video
                videoSurat.style.display = 'none';
                canvasSurat.style.display = 'block';

                // Konversi canvas ke base64
                const dataURL = canvasSurat.toDataURL('image/jpeg', 0.8);
                fotoSuratTugasInput.value = dataURL;

                console.log('Foto surat berhasil diambil');

                // Update status foto
                fotoSuratTerambil = true;
                btnAmbilFotoSurat.style.display = 'none';
                btnUlangiFotoSurat.style.display = 'block';

                // Matikan kamera
                if (streamSurat) {
                    streamSurat.getTracks().forEach(track => track.stop());
                    streamSurat = null;
                }
            } catch (error) {
                console.error('Error saat mengambil foto surat:', error);
                alert('Terjadi kesalahan saat mengambil foto: ' + error.message);
            }
        }

        // Event listener untuk tombol ambil foto wajah
        if (btnAmbilFotoWajah) {
            btnAmbilFotoWajah.addEventListener('click', ambilFotoWajah);
        }

        // Event listener untuk tombol ulangi foto wajah
        if (btnUlangiFotoWajah) {
            btnUlangiFotoWajah.addEventListener('click', startCameraWajah);
        }

        // Event listener untuk tombol ambil foto surat
        if (btnAmbilFotoSurat) {
            btnAmbilFotoSurat.addEventListener('click', ambilFotoSurat);
        }

        // Event listener untuk tombol ulangi foto surat
        if (btnUlangiFotoSurat) {
            btnUlangiFotoSurat.addEventListener('click', startCameraSurat);
        }

        // Fungsi untuk menyesuaikan semua panduan
        function adjustAllGuides() {
            // Hanya sesuaikan panduan wajah, tidak ada panduan untuk surat tugas
            adjustFaceGuideSize('video-wajah', 'face-guide-wajah');
        }

        // Sesuaikan ukuran saat jendela diubah ukurannya
        window.addEventListener('resize', adjustAllGuides);

        // Fungsi untuk mendeteksi Android WebView
        function isAndroidWebView() {
            const userAgent = navigator.userAgent.toLowerCase();
            return /android/.test(userAgent) && /wv/.test(userAgent);
        }

        // Fungsi untuk mendeteksi versi Android
        function getAndroidVersion() {
            const match = navigator.userAgent.toLowerCase().match(/android\s([0-9.]*)/);
            return match ? parseFloat(match[1]) : 0;
        }

        // Aktifkan kamera saat halaman dimuat
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Halaman dimuat, mengaktifkan kamera...');
            console.log('User Agent:', navigator.userAgent);

            const isAndroid = isAndroidWebView();
            const androidVersion = getAndroidVersion();

            console.log('Deteksi Android WebView:', isAndroid);
            console.log('Versi Android:', androidVersion);

            // Tambahkan informasi ke halaman untuk debugging
            if (isAndroid) {
                const infoElement = document.createElement('div');
                infoElement.style.display = 'none'; // Sembunyikan dari pengguna
                infoElement.id = 'android-info';
                infoElement.textContent = 'Android ' + androidVersion + ' WebView terdeteksi';
                document.body.appendChild(infoElement);
            }

            // Aktifkan kamera dengan delay
            setTimeout(function() {
                // Coba aktifkan kamera satu per satu untuk menghindari konflik
                startCameraWajah();

                // Tunggu sebentar sebelum mengaktifkan kamera kedua
                setTimeout(function() {
                    startCameraSurat();

                    // Sesuaikan ukuran panduan setelah kamera aktif
                    setTimeout(adjustAllGuides, 1000);
                }, 2000);
            }, 1000);

            // Tambahkan event listener untuk menangani error video
            const videoElements = [videoWajah, videoSurat];
            videoElements.forEach(function(video) {
                if (video) {
                    video.addEventListener('error', function(e) {
                        console.error('Video error:', e);
                        // Tampilkan pesan error yang lebih informatif
                        const cameraPreview = video.closest('.camera-preview');
                        if (cameraPreview && cameraPreview.querySelector('.camera-message')) {
                            cameraPreview.querySelector('.camera-message').textContent = 'Error video: ' + (e.message || 'Tidak dapat memulai kamera');
                        }
                    });
                }
            });
        });

        // Validasi form sebelum submit
        const formDinasHariIni = document.getElementById('formDinasHariIni');
        if (formDinasHariIni) {
            formDinasHariIni.addEventListener('submit', function(e) {
                if (!fotoWajahTerambil) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'warning',
                        title: 'Foto Wajah Diperlukan',
                        text: 'Anda harus mengambil foto wajah terlebih dahulu!',
                        confirmButtonColor: '#f6c23e',
                        confirmButtonText: 'Saya Mengerti'
                    });
                    return false;
                }

                if (!fotoSuratTerambil) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'warning',
                        title: 'Foto Surat Tugas Diperlukan',
                        text: 'Anda harus mengambil foto surat tugas terlebih dahulu!',
                        confirmButtonColor: '#f6c23e',
                        confirmButtonText: 'Saya Mengerti'
                    });
                    return false;
                }
            });
        }
    </script>

<?php
// Include footer untuk bottom navigation
include_once '../includes/footer.php';
?>
