# Dokumentasi API AbsensiKu

Dokumentasi ini berisi informasi tentang API yang tersedia untuk aplikasi AbsensiKu.

## Endpoint Device Status

### API Blokir Device

Endpoint ini digunakan untuk memblokir device yang tidak diizinkan.

#### URL

```
POST /api/blokir_device.php
```

#### Headers

```
Content-Type: application/json
```

#### Request Format (Single):

```json
{
    "api_key": "absensiku_api_key_2023",
    "nik": "123456789",
    "device_id": "abc123xyz456",
    "alasan": "Device tidak dikenali"
}
```

#### Request Format (Batch):

```json
{
    "api_key": "absensiku_api_key_2023",
    "data": [
        {
            "nik": "123456789",
            "device_id": "abc123xyz456",
            "alasan": "Device tidak dikenali"
        },
        {
            "nik": "987654321",
            "device_id": "def456uvw789",
            "alasan": "<PERSON><PERSON> mencurigakan"
        }
    ]
}
```

#### Response Format (Success):

```json
{
    "status": "success",
    "message": "Data blokir device berhasil disimpan",
    "data": {
        "id": 1,
        "nik": "123456789",
        "nama": "Nama Karyawan",
        "device_id": "abc123xyz456",
        "alasan": "Device tidak dikenali",
        "status": "active",
        "tanggal_blokir": "01/01/2023 12:00:00",
        "tanggal_update": "01/01/2023 12:00:00",
        "can_unblock": false
    }
}
```

### API Check Device

Endpoint ini digunakan untuk memeriksa status blokir device.

#### URL

```
POST /api/check_device.php
```

#### Headers

```
Content-Type: application/json
```

#### Request Format:

```json
{
    "api_key": "absensiku_api_key_2023",
    "device_id": "abc123xyz456",
    "nik": "123456789" // opsional
}
```

#### Response Format (Blocked):

```json
{
    "status": "blocked",
    "message": "Device diblokir",
    "data": {
        "nik": "123456789",
        "nama": "Nama Karyawan",
        "alasan": "Device tidak dikenali",
        "tanggal_blokir": "01/01/2023 12:00:00"
    }
}
```

#### Response Format (Allowed):

```json
{
    "status": "allowed",
    "message": "Device diizinkan"
}
```

### API Get Device Status

Endpoint ini digunakan untuk mendapatkan status device dan informasi tambahan untuk aplikasi Android.

#### URL

```
POST /api/get_device_status.php
```

#### Headers

```
Content-Type: application/json
```

#### Request Format:

```json
{
    "api_key": "absensiku_api_key_2023",
    "device_id": "abc123xyz456",
    "nik": "123456789" // opsional
}
```

#### Response Format (Blocked):

```json
{
    "status": "blocked",
    "message": "Device diblokir",
    "can_unblock": true,
    "data": {
        "id": 1,
        "nik": "123456789",
        "nama": "Nama Karyawan",
        "alasan": "Alasan pemblokiran sementara",
        "tanggal_blokir": "01/01/2023 12:00:00",
        "tanggal_update": "01/01/2023 12:00:00"
    }
}
```

#### Response Format (Allowed):

```json
{
    "status": "allowed",
    "message": "Device diizinkan"
}
```

#### Request Format untuk Membuka Blokir:

```json
{
    "api_key": "absensiku_api_key_2023",
    "device_id": "abc123xyz456",
    "nik": "123456789", // opsional
    "action": "unblock",
    "verification_code": "UNBLOCK123" // kode verifikasi opsional
}
```

#### Response Format untuk Membuka Blokir (Success):

```json
{
    "status": "success",
    "message": "Device berhasil dibuka blokirnya"
}
```

## Endpoint Pelacakan Lokasi

Endpoint ini digunakan untuk mengirim data lokasi karyawan dari aplikasi Android ke server.

### URL

```
POST /api/track_location.php
```

### Headers

```
Content-Type: application/json
```

### Parameter

| Parameter  | Tipe   | Deskripsi                                      |
|------------|--------|------------------------------------------------|
| api_key    | string | Kunci API untuk autentikasi                    |
| nik        | string | NIK karyawan                                   |
| longitude  | string | Koordinat longitude lokasi karyawan            |
| latitude   | string | Koordinat latitude lokasi karyawan             |

### Contoh Request

```json
{
  "api_key": "absensiku_api_key_2023",
  "nik": "123456",
  "longitude": "106.8456",
  "latitude": "-6.2088"
}
```

### Contoh Response Sukses

```json
{
  "status": "success",
  "message": "Data lokasi berhasil disimpan"
}
```

### Contoh Response Error

```json
{
  "status": "error",
  "message": "Data tidak lengkap"
}
```

## Cara Implementasi di Android

### Implementasi Pelacakan Lokasi

Berikut adalah contoh kode untuk implementasi pelacakan lokasi di aplikasi Android:

```java
// Di dalam Activity atau Fragment yang menggunakan WebView
private void setupLocationTracking() {
    // Jadwalkan pengiriman lokasi setiap 1 jam
    Handler handler = new Handler();
    Runnable locationRunnable = new Runnable() {
        @Override
        public void run() {
            sendLocationToServer();
            handler.postDelayed(this, 60 * 60 * 1000); // 1 jam
        }
    };

    // Mulai jadwal
    handler.post(locationRunnable);
}

private void sendLocationToServer() {
    // Cek izin lokasi terlebih dahulu
    if (checkLocationPermission()) {
        // Dapatkan lokasi terkini
        LocationManager locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
        if (locationManager != null) {
            try {
                locationManager.requestSingleUpdate(LocationManager.GPS_PROVIDER, new LocationListener() {
                    @Override
                    public void onLocationChanged(Location location) {
                        // Kirim lokasi ke server
                        String longitude = String.valueOf(location.getLongitude());
                        String latitude = String.valueOf(location.getLatitude());

                        // Buat data JSON
                        JSONObject data = new JSONObject();
                        try {
                            data.put("api_key", "absensiku_api_key_2023");
                            data.put("nik", getUserNik()); // Implementasi fungsi untuk mendapatkan NIK
                            data.put("longitude", longitude);
                            data.put("latitude", latitude);

                            // Kirim data ke server
                            sendDataToServer(data);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }

                    // Implementasi method lain dari LocationListener
                    @Override
                    public void onStatusChanged(String provider, int status, Bundle extras) {}

                    @Override
                    public void onProviderEnabled(String provider) {}

                    @Override
                    public void onProviderDisabled(String provider) {}
                }, null);
            } catch (SecurityException e) {
                e.printStackTrace();
            }
        }
    }
}

private void sendDataToServer(JSONObject data) {
    // Implementasi pengiriman data ke server menggunakan Volley, Retrofit, atau HttpURLConnection
    // Contoh menggunakan Volley:
    RequestQueue queue = Volley.newRequestQueue(this);
    String url = "https://absensiku.trunois.my.id/api/track_location.php";

    JsonObjectRequest jsonRequest = new JsonObjectRequest(Request.Method.POST, url, data,
            new Response.Listener<JSONObject>() {
                @Override
                public void onResponse(JSONObject response) {
                    // Handle response
                    Log.d("LocationTracking", "Success: " + response.toString());
                }
            },
            new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {
                    // Handle error
                    Log.e("LocationTracking", "Error: " + error.toString());
                }
            });

    queue.add(jsonRequest);
}
```

### Implementasi Pengecekan Status Device

Berikut adalah contoh kode untuk implementasi pengecekan status device di aplikasi Android:

```java
// Di dalam Activity atau Fragment yang menggunakan WebView
private void checkDeviceStatus() {
    // Dapatkan device ID
    String deviceId = getDeviceId(); // Implementasi fungsi untuk mendapatkan device ID
    String nik = getUserNik(); // Implementasi fungsi untuk mendapatkan NIK

    // Buat data JSON
    JSONObject data = new JSONObject();
    try {
        data.put("api_key", "absensiku_api_key_2023");
        data.put("device_id", deviceId);
        data.put("nik", nik);

        // Kirim data ke server
        checkDeviceStatusFromServer(data);
    } catch (JSONException e) {
        e.printStackTrace();
    }
}

private void checkDeviceStatusFromServer(JSONObject data) {
    // Implementasi pengiriman data ke server menggunakan Volley, Retrofit, atau HttpURLConnection
    // Contoh menggunakan Volley:
    RequestQueue queue = Volley.newRequestQueue(this);
    String url = "https://absensiku.trunois.my.id/api/get_device_status.php";

    JsonObjectRequest jsonRequest = new JsonObjectRequest(Request.Method.POST, url, data,
            new Response.Listener<JSONObject>() {
                @Override
                public void onResponse(JSONObject response) {
                    // Handle response
                    try {
                        String status = response.getString("status");
                        if (status.equals("blocked")) {
                            // Device diblokir
                            boolean canUnblock = response.getBoolean("can_unblock");
                            JSONObject deviceData = response.getJSONObject("data");

                            // Tampilkan pesan blokir
                            showBlockedMessage(deviceData.getString("alasan"), canUnblock);

                            // Jika device dapat dibuka blokirnya, tampilkan tombol untuk membuka blokir
                            if (canUnblock) {
                                showUnblockButton(deviceId, nik);
                            }
                        } else if (status.equals("allowed")) {
                            // Device diizinkan, lanjutkan ke halaman utama
                            proceedToMainPage();
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            },
            new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {
                    // Handle error
                    Log.e("DeviceStatus", "Error: " + error.toString());

                    // Tampilkan pesan error
                    showErrorMessage("Gagal memeriksa status device. Silakan coba lagi.");
                }
            });

    queue.add(jsonRequest);
}

private void unblockDevice(String deviceId, String nik) {
    // Buat data JSON
    JSONObject data = new JSONObject();
    try {
        data.put("api_key", "absensiku_api_key_2023");
        data.put("device_id", deviceId);
        data.put("nik", nik);
        data.put("action", "unblock");
        data.put("verification_code", "UNBLOCK123"); // Kode verifikasi

        // Kirim data ke server
        RequestQueue queue = Volley.newRequestQueue(this);
        String url = "https://absensiku.trunois.my.id/api/get_device_status.php";

        JsonObjectRequest jsonRequest = new JsonObjectRequest(Request.Method.POST, url, data,
                new Response.Listener<JSONObject>() {
                    @Override
                    public void onResponse(JSONObject response) {
                        // Handle response
                        try {
                            String status = response.getString("status");
                            if (status.equals("success")) {
                                // Device berhasil dibuka blokirnya
                                showSuccessMessage("Device berhasil dibuka blokirnya.");

                                // Lanjutkan ke halaman utama
                                proceedToMainPage();
                            } else {
                                // Gagal membuka blokir
                                showErrorMessage(response.getString("message"));
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                },
                new Response.ErrorListener() {
                    @Override
                    public void onErrorResponse(VolleyError error) {
                        // Handle error
                        Log.e("DeviceStatus", "Error: " + error.toString());

                        // Tampilkan pesan error
                        showErrorMessage("Gagal membuka blokir device. Silakan coba lagi.");
                    }
                });

        queue.add(jsonRequest);
    } catch (JSONException e) {
        e.printStackTrace();
    }
}
```

## Catatan Keamanan

1. API key sebaiknya disimpan dengan aman dan tidak di-hardcode di aplikasi
2. Gunakan HTTPS untuk semua komunikasi API
3. Batasi rate limit untuk mencegah penyalahgunaan API
4. Pertimbangkan untuk menambahkan validasi tambahan seperti device ID jika diperlukan
