<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Fungsi untuk menjalankan query dan menampilkan pesan
function runQuery($conn, $query, $message) {
    if (mysqli_query($conn, $query)) {
        echo "<p class='text-success'>$message berhasil.</p>";
        return true;
    } else {
        echo "<p class='text-danger'>$message gagal: " . mysqli_error($conn) . "</p>";
        return false;
    }
}

// Proses update bidang
$updated = 0;
$failed = 0;
$total = 0;

if (isset($_POST['update'])) {
    // Ambil semua karyawan yang memiliki bidang_id tapi tidak memiliki bidang
    $query = "SELECT id, bidang_id FROM users WHERE bidang_id IS NOT NULL AND (bidang IS NULL OR bidang = '')";
    $result = mysqli_query($conn, $query);
    
    if ($result) {
        $total = mysqli_num_rows($result);
        
        while ($row = mysqli_fetch_assoc($result)) {
            $user_id = $row['id'];
            $bidang_id = $row['bidang_id'];
            
            // Ambil nama bidang
            $query_bidang = "SELECT nama_bidang FROM bidang WHERE id = '$bidang_id'";
            $result_bidang = mysqli_query($conn, $query_bidang);
            
            if ($result_bidang && mysqli_num_rows($result_bidang) > 0) {
                $bidang_data = mysqli_fetch_assoc($result_bidang);
                $nama_bidang = $bidang_data['nama_bidang'];
                
                // Update kolom bidang
                $query_update = "UPDATE users SET bidang = '$nama_bidang' WHERE id = '$user_id'";
                if (mysqli_query($conn, $query_update)) {
                    $updated++;
                } else {
                    $failed++;
                }
            } else {
                $failed++;
            }
        }
    }
    
    // Set pesan
    if ($updated > 0) {
        setMessage('success', "Berhasil memperbarui $updated dari $total data karyawan" . ($failed > 0 ? ", gagal: $failed" : ""));
    } else {
        setMessage('danger', "Gagal memperbarui data karyawan");
    }
    
    // Redirect ke halaman karyawan
    redirect('admin/karyawan.php');
}

// Include header
include_once '../includes/header.php';

// Ambil pesan jika ada
$message = getMessage();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Update Kolom Bidang</h1>
        <a href="karyawan.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
    
    <?php if ($message): ?>
    <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $message['text']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Update Kolom Bidang</h6>
        </div>
        <div class="card-body">
            <p>Fitur ini akan memperbarui kolom <code>bidang</code> pada tabel <code>users</code> berdasarkan <code>bidang_id</code> yang sudah ada.</p>
            <p>Gunakan fitur ini jika ada karyawan yang memiliki <code>bidang_id</code> tetapi kolom <code>bidang</code> kosong.</p>
            
            <form method="post" action="">
                <button type="submit" name="update" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> Update Kolom Bidang
                </button>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
