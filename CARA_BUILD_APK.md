# 🚀 Cara Build APK Aplikasi Absensi Karyawan

## 📋 Langkah-Langkah Build APK

### 1. ⚡ Cara Cepat (Menggunakan Script Otomatis)

#### Windows:
```cmd
cd "aplikasi absen karyawan android"
build_apk.bat
```

#### Linux/macOS:
```bash
cd "aplikasi absen karyawan android"
chmod +x build_apk.sh
./build_apk.sh
```

### 2. 🔧 Cara Manual

#### Persiapan:
1. **Install Flutter SDK**
   - Download dari: https://docs.flutter.dev/get-started/install
   - Tambahkan ke PATH environment variable
   - Jalankan `flutter doctor` untuk verifikasi

2. **Install Android Studio**
   - Download dari: https://developer.android.com/studio
   - Install Android SDK dan Command-line Tools

#### Build Process:
```bash
# 1. Masuk ke direktori project
cd "aplikasi absen karyawan android"

# 2. Install dependencies
flutter pub get

# 3. Build APK
flutter build apk --release
```

## 📱 Hasil Build

Setelah build selesai, file APK akan tersedia di:
```
aplikasi absen karyawan android/build/app/outputs/flutter-apk/app-release.apk
```

## 🔧 Konfigurasi Sebelum Build

### 1. Update URL Server
Edit file `lib/utils/constants.dart`:
```dart
// Ganti dengan IP/domain server Anda
static const String baseUrl = 'http://*************/absenku/admin%20panel%20%20web/api';
```

### 2. Pastikan Server Backend Running
- Web server (Apache/Nginx) harus aktif
- Database MySQL harus running
- API endpoints harus dapat diakses

## 📲 Install APK di Android

### 1. Enable Unknown Sources
Di perangkat Android:
- **Settings** > **Security** > **Unknown Sources** (ON)
- Atau **Settings** > **Apps** > **Special Access** > **Install Unknown Apps**

### 2. Transfer & Install
1. Copy file `app-release.apk` ke perangkat Android
2. Buka file manager, tap file APK
3. Tap **Install**
4. Tunggu proses instalasi selesai

## 🧪 Testing APK

### 1. Test Login
- Gunakan NIK dan password yang sudah terdaftar di web admin
- Pastikan koneksi internet aktif

### 2. Test Fitur
- **Dashboard**: Cek data loading dengan benar
- **Presensi**: Test camera dan GPS
- **Riwayat**: Cek data absensi tampil
- **Profile**: Cek informasi user

## ⚠️ Troubleshooting

### Build Errors:

#### "Flutter not found"
```bash
# Pastikan Flutter di PATH
flutter --version

# Atau gunakan full path
/path/to/flutter/bin/flutter build apk --release
```

#### "Android SDK not found"
```bash
# Install Android SDK via Android Studio
# Atau set ANDROID_HOME
export ANDROID_HOME=/path/to/android/sdk
```

#### "Gradle build failed"
```bash
# Clean project
flutter clean
flutter pub get
flutter build apk --release
```

### Runtime Errors:

#### "Network Error"
- Pastikan URL server benar di `constants.dart`
- Cek koneksi internet
- Pastikan server backend running

#### "Permission Denied"
- Enable camera dan location permission di Android settings
- Restart aplikasi setelah enable permission

#### "Login Failed"
- Pastikan NIK dan password benar
- Cek database user di web admin
- Pastikan API login endpoint berfungsi

## 📊 Optimasi APK

### 1. Reduce Size
```bash
# Build dengan obfuscation
flutter build apk --release --obfuscate --split-debug-info=build/debug-info

# Split by architecture
flutter build apk --release --split-per-abi
```

### 2. Performance
```bash
# Build dengan profile mode untuk debugging performance
flutter build apk --profile
```

## 🔐 Production Build

Untuk production, sebaiknya menggunakan custom keystore:

### 1. Generate Keystore
```bash
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

### 2. Configure Signing
Buat file `android/key.properties`:
```properties
storePassword=your_store_password
keyPassword=your_key_password
keyAlias=upload
storeFile=/path/to/upload-keystore.jks
```

### 3. Update build.gradle
Uncomment bagian signing config di `android/app/build.gradle`

## 📋 Checklist Build

- [ ] Flutter SDK terinstall
- [ ] Android SDK terinstall
- [ ] `flutter doctor` status OK
- [ ] URL server sudah dikonfigurasi
- [ ] Dependencies ter-install (`flutter pub get`)
- [ ] Server backend running
- [ ] Database accessible

## 🎯 Quick Commands

```bash
# Complete build process
cd "aplikasi absen karyawan android"
flutter clean
flutter pub get
flutter build apk --release

# File APK: build/app/outputs/flutter-apk/app-release.apk
```

## 📞 Support

Jika mengalami masalah:
1. Cek `flutter doctor` untuk environment issues
2. Pastikan semua dependencies ter-install
3. Cek log error untuk detail masalah
4. Pastikan server backend dapat diakses

---

**File APK siap untuk diinstall di perangkat Android!** 🎉
