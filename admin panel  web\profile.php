<?php
// Include file konfigurasi
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Cek login
if (!isLoggedIn()) {
    redirect('index.php');
}

// Ambil data user
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = '$user_id'";
$result = mysqli_query($conn, $query);
$user = mysqli_fetch_assoc($result);

// Ambil data lokasi untuk dropdown
$query = "SELECT * FROM lokasi ORDER BY nama_lokasi ASC";
$result = mysqli_query($conn, $query);

$lokasi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $lokasi[] = $row;
    }
}

// Proses update profil
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nama = clean($_POST['nama']);
    $bidang = clean($_POST['bidang']);
    $jabatan = clean($_POST['jabatan']);

    // Untuk karyawan, gunakan lokasi yang sudah ada di database
    if (!isAdmin()) {
        $lokasi_id = $user['lokasi_id'];
    } else {
        $lokasi_id = clean($_POST['lokasi_id']);
    }

    // Upload foto profil baru jika ada
    $foto_profil = null;
    if (isset($_FILES['foto_profil']) && $_FILES['foto_profil']['error'] == 0) {
        $file_tmp = $_FILES['foto_profil']['tmp_name'];
        $file_name = $_FILES['foto_profil']['name'];
        $file_size = $_FILES['foto_profil']['size'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Cek ekstensi file
        if (in_array($file_ext, ALLOWED_EXTENSIONS)) {
            // Cek ukuran file
            if ($file_size <= MAX_UPLOAD_SIZE) {
                $foto_profil = 'profile_' . time() . '.' . $file_ext;
                $upload_path = UPLOAD_PATH . $foto_profil;

                if (move_uploaded_file($file_tmp, $upload_path)) {
                    // Berhasil upload

                    // Hapus foto lama jika ada
                    if (!empty($user['foto_profil'])) {
                        $old_file = UPLOAD_PATH . $user['foto_profil'];
                        if (file_exists($old_file)) {
                            unlink($old_file);
                        }
                    }

                    // Update foto profil
                    $query = "UPDATE users SET foto_profil = '$foto_profil' WHERE id = '$user_id'";
                    mysqli_query($conn, $query);
                } else {
                    setMessage('danger', 'Gagal mengupload foto profil!');
                    redirect('profile.php');
                }
            } else {
                setMessage('danger', 'Ukuran file terlalu besar! Maksimal ' . (MAX_UPLOAD_SIZE / 1024 / 1024) . 'MB');
                redirect('profile.php');
            }
        } else {
            setMessage('danger', 'Ekstensi file tidak diizinkan! Hanya ' . implode(', ', ALLOWED_EXTENSIONS) . ' yang diizinkan');
            redirect('profile.php');
        }
    }

    // Update data profil
    $query = "UPDATE users SET nama = '$nama'";

    // Jika bukan admin, update bidang, jabatan, dan lokasi
    if (!isAdmin()) {
        $query .= ", bidang = '$bidang', jabatan = '$jabatan', lokasi_id = '$lokasi_id'";
    }

    $query .= " WHERE id = '$user_id'";

    if (mysqli_query($conn, $query)) {
        // Update session
        $_SESSION['nama'] = $nama;

        setMessage('success', 'Profil berhasil diperbarui!');
    } else {
        setMessage('danger', 'Gagal memperbarui profil!');
    }

    redirect('profile.php');
}

// Include header
include_once 'includes/header.php';

// Ambil data lokasi untuk dropdown
$query = "SELECT * FROM lokasi WHERE id = '{$user['lokasi_id']}'";
$result = mysqli_query($conn, $query);
$lokasi_user = mysqli_fetch_assoc($result);
?>

<?php if (isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan'): ?>
<!-- Mobile Profile View for Karyawan -->
<div class="mobile-profile-container">
    <!-- Header Section -->
    <div class="profile-header">
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                <div class="user-position"><?php echo $user['bidang'] ?? 'Karyawan'; ?></div>
            </div>
        </div>
        <div class="date-info">
            <i class="fas fa-calendar-alt"></i> <?php echo date('l, d F Y'); ?>
        </div>
    </div>

    <!-- Container -->
    <div class="container px-0">
        <?php
        // Tampilkan pesan jika ada
        $message = getMessage();
        if ($message): ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show mx-3">
                <?php echo $message['text']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Profile Photo Section -->
        <div class="profile-photo-section">
            <?php if (!empty($user['foto_profil'])): ?>
                <img src="<?php echo BASE_URL . 'uploads/' . $user['foto_profil']; ?>" class="profile-photo" alt="Foto Profil">
            <?php else: ?>
                <div class="profile-photo-placeholder">
                    <i class="fas fa-user"></i>
                </div>
            <?php endif; ?>
            <label for="foto_profil" class="custom-file-label">
                <i class="fas fa-camera"></i> Ubah Foto
            </label>
        </div>

        <!-- Profile Form Section -->
        <form method="post" action="" enctype="multipart/form-data" class="px-3">
            <input type="file" class="custom-file-input" id="foto_profil" name="foto_profil" accept="image/*">
            <div id="file-name-display" class="file-name-display text-center"></div>

            <div class="profile-form-section">
                <div class="profile-form-title">Informasi Pribadi</div>

                <div class="form-group">
                    <label for="nik" class="form-label">NIK</label>
                    <input type="text" class="form-control" id="nik" value="<?php echo $user['nik']; ?>" readonly>
                </div>

                <div class="form-group">
                    <label for="nama" class="form-label">Nama</label>
                    <input type="text" class="form-control" id="nama" name="nama" value="<?php echo $user['nama']; ?>" required>
                </div>
            </div>

            <div class="profile-form-section">
                <div class="profile-form-title">Informasi Pekerjaan</div>

                <div class="form-group">
                    <label for="bidang" class="form-label">Bidang</label>
                    <input type="text" class="form-control" id="bidang" name="bidang" value="<?php echo $user['bidang']; ?>" required disabled>
                </div>

                <div class="form-group">
                    <label for="jabatan" class="form-label">Jabatan</label>
                    <input type="text" class="form-control" id="jabatan" name="jabatan" value="<?php echo $user['jabatan']; ?>" required>
                </div>

                <div class="form-group">
                    <label for="lokasi_id" class="form-label">Lokasi</label>
                    <select class="form-select" id="lokasi_id" name="lokasi_id" required disabled>
                        <?php foreach ($lokasi as $l): ?>
                            <option value="<?php echo $l['id']; ?>" <?php echo ($user['lokasi_id'] == $l['id']) ? 'selected' : ''; ?>>
                                <?php echo $l['nama_lokasi']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <input type="hidden" name="lokasi_id" value="<?php echo $user['lokasi_id']; ?>">
                    <small class="text-muted">Lokasi tidak dapat diubah oleh karyawan</small>
                </div>
            </div>

            <div class="action-buttons">
                <button type="submit" class="btn-primary">Simpan Perubahan</button>
            </div>

            <!-- Additional Quick Actions -->
           <br>
           <br>
           <br>
           <br>
           <br>
           <br>
           <br>
        </form>
    </div>
</div>

<script>
    // Display file name when selected
    document.getElementById('foto_profil').addEventListener('change', function() {
        var fileName = this.files[0] ? this.files[0].name : '';
        document.getElementById('file-name-display').textContent = fileName;
    });
</script>

<?php else: ?>
<!-- Regular Profile View for Admin -->
<div class="container">
    <div class="row justify-content-center mt-5">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header text-center">
                    <h4>Profil Saya</h4>
                </div>
                <div class="card-body">
                    <?php
                    // Tampilkan pesan jika ada
                    $message = getMessage();
                    if ($message): ?>
                        <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                            <?php echo $message['text']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <?php if (!empty($user['foto_profil'])): ?>
                                <img src="<?php echo BASE_URL . 'uploads/' . $user['foto_profil']; ?>" class="img-thumbnail rounded-circle" style="width: 200px; height: 200px; object-fit: cover;">
                            <?php else: ?>
                                <img src="https://via.placeholder.com/200" class="img-thumbnail rounded-circle" style="width: 200px; height: 200px; object-fit: cover;">
                            <?php endif; ?>
                        </div>

                        <div class="col-md-8">
                            <form method="post" action="" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="nik" class="form-label">NIK</label>
                                    <input type="text" class="form-control" id="nik" value="<?php echo $user['nik']; ?>" readonly>
                                </div>

                                <div class="mb-3">
                                    <label for="nama" class="form-label">Nama</label>
                                    <input type="text" class="form-control" id="nama" name="nama" value="<?php echo $user['nama']; ?>" required>
                                </div>

                                <input type="hidden" name="bidang" value="<?php echo $user['bidang']; ?>">
                                <input type="hidden" name="jabatan" value="<?php echo $user['jabatan']; ?>">
                                <input type="hidden" name="lokasi_id" value="<?php echo $user['lokasi_id']; ?>">

                                <div class="mb-3">
                                    <label for="foto_profil" class="form-label">Foto Profil</label>
                                    <input type="file" class="form-control" id="foto_profil" name="foto_profil">
                                    <small class="text-muted">Biarkan kosong jika tidak ingin mengubah foto profil</small>
                                </div>

                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                                </div>

                                <div class="text-center">
                                    <a href="change_password.php" class="btn btn-link">Ubah Password</a>
                                    <a href="admin/index.php" class="btn btn-link">Kembali ke Dashboard</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include footer
include_once 'includes/footer.php';
?>
