<?php
/**
 * API untuk memeriksa status blokir device
 * Endpoint ini memeriksa apakah suatu device diblokir atau tidak
 *
 * Format data:
 * {
 *     "api_key": "absensiku_api_key_2023",
 *     "device_id": "abc123xyz456",
 *     "nik": "123456789" // opsional
 * }
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header untuk JSON dan CORS
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Fungsi untuk validasi API key
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Fungsi untuk memeriksa status blokir device
function checkDeviceStatus($device_id, $nik = null) {
    global $conn;

    // Escape data
    $device_id = mysqli_real_escape_string($conn, $device_id);

    // Buat query dasar
    $query = "SELECT bd.*, u.nama
              FROM blokir_device bd
              LEFT JOIN users u ON bd.user_id = u.id
              WHERE bd.status = 'active' AND (bd.device_id = '$device_id'";

    // Tambahkan kondisi NIK jika ada
    if ($nik) {
        $nik = mysqli_real_escape_string($conn, $nik);
        $query .= " OR bd.nik = '$nik'";
    }

    $query .= ")";

    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        // Device atau NIK diblokir
        $data = mysqli_fetch_assoc($result);
        return [
            'status' => 'blocked',
            'message' => 'Device diblokir',
            'data' => [
                'nik' => $data['nik'],
                'nama' => $data['nama'],
                'alasan' => $data['alasan'],
                'tanggal_blokir' => date('d/m/Y H:i:s', strtotime($data['created_at']))
            ]
        ];
    } else {
        // Device dan NIK tidak diblokir
        return [
            'status' => 'allowed',
            'message' => 'Device diizinkan'
        ];
    }
}

// Ambil data dari request
$data = json_decode(file_get_contents('php://input'), true);

// Jika data tidak valid, coba ambil dari $_POST
if (!$data) {
    $data = $_POST;
}

// Validasi API key
if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'API key tidak valid'
    ]);
    exit;
}

// Validasi device_id
if (!isset($data['device_id'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Device ID tidak ditemukan'
    ]);
    exit;
}

// Ambil NIK jika ada
$nik = isset($data['nik']) ? $data['nik'] : null;

// Periksa status device
$result = checkDeviceStatus($data['device_id'], $nik);
echo json_encode($result);
?>
