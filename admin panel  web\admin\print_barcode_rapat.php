<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID rapat tidak valid!');
    redirect('admin/rapat.php');
}

$id = clean($_GET['id']);

// Ambil data rapat
$query = "SELECT * FROM rapat WHERE id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Data rapat tidak ditemukan!');
    redirect('admin/rapat.php');
}

$rapat = mysqli_fetch_assoc($result);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cetak Barcode Rapat - <?php echo $rapat['judul']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .print-buttons {
            padding: 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            text-align: right;
        }
        
        .barcode-card {
            padding: 30px;
            text-align: center;
        }
        
        .barcode-image {
            margin: 30px auto;
            max-width: 300px;
        }
        
        .barcode-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .barcode-value {
            font-family: monospace;
            font-size: 14px;
            color: #6c757d;
            margin-top: 10px;
        }
        
        @media print {
            .print-buttons {
                display: none;
            }
            
            body {
                background-color: white;
                padding: 0;
            }
            
            .print-container {
                box-shadow: none;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-buttons">
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print"></i> Cetak Barcode
            </button>
            <a href="detail_rapat.php?id=<?php echo $rapat['id']; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
        
        <div class="barcode-card">
            <h2><?php echo APP_NAME; ?> - Absensi Rapat</h2>
            <h3><?php echo $rapat['judul']; ?></h3>
            
            <div class="barcode-info">
                <div class="row">
                    <div class="col-md-6 text-md-end">
                        <strong>Tanggal:</strong>
                    </div>
                    <div class="col-md-6 text-md-start">
                        <?php echo date('d/m/Y', strtotime($rapat['tanggal'])); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 text-md-end">
                        <strong>Waktu:</strong>
                    </div>
                    <div class="col-md-6 text-md-start">
                        <?php echo date('H:i', strtotime($rapat['waktu_mulai'])) . ' - ' . date('H:i', strtotime($rapat['waktu_selesai'])); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 text-md-end">
                        <strong>Lokasi:</strong>
                    </div>
                    <div class="col-md-6 text-md-start">
                        <?php echo $rapat['lokasi']; ?>
                    </div>
                </div>
            </div>
            
            <div class="barcode-image">
                <img src="https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=<?php echo urlencode($rapat['barcode_value']); ?>" alt="Barcode" class="img-fluid">
            </div>
            
            <p class="barcode-value"><?php echo $rapat['barcode_value']; ?></p>
            
            <p class="text-muted">Scan barcode ini untuk melakukan absensi rapat</p>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
