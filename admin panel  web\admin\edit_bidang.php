<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Cek apakah ada parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID bidang tidak valid');
    redirect('bidang.php');
}

$id = clean($_GET['id']);

// Ambil data bidang berdasarkan id
$query = "SELECT * FROM bidang WHERE id = '$id'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Bidang tidak ditemukan');
    redirect('bidang.php');
}

$bidang = mysqli_fetch_assoc($result);

// Proses edit bidang
if (isset($_POST['edit'])) {
    $nama_bidang = clean($_POST['nama_bidang']);
    $keterangan = clean($_POST['keterangan']);
    
    // Validasi input
    if (empty($nama_bidang)) {
        setMessage('danger', 'Nama bidang tidak boleh kosong');
        redirect("edit_bidang.php?id=$id");
    }
    
    // Cek apakah nama bidang sudah ada (selain bidang ini)
    $query = "SELECT * FROM bidang WHERE nama_bidang = '$nama_bidang' AND id != '$id'";
    $result = mysqli_query($conn, $query);
    
    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Nama bidang sudah ada');
        redirect("edit_bidang.php?id=$id");
    }
    
    // Update bidang
    $query = "UPDATE bidang SET nama_bidang = '$nama_bidang', keterangan = '$keterangan' WHERE id = '$id'";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Bidang berhasil diupdate');
        redirect('admin/bidang.php');
    } else {
        setMessage('danger', 'Gagal mengupdate bidang: ' . mysqli_error($conn));
        redirect("edit_bidang.php?id=$id");
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Edit Bidang</h1>
        <a href="bidang.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Form Edit Bidang</h6>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="mb-3">
                    <label for="nama_bidang" class="form-label">Nama Bidang</label>
                    <input type="text" class="form-control" id="nama_bidang" name="nama_bidang" value="<?php echo $bidang['nama_bidang']; ?>" required>
                </div>

                <div class="mb-3">
                    <label for="keterangan" class="form-label">Keterangan</label>
                    <textarea class="form-control" id="keterangan" name="keterangan" rows="3"><?php echo $bidang['keterangan']; ?></textarea>
                </div>

                <div class="mt-3">
                    <button type="submit" name="edit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                    <a href="bidang.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
