<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Ambil data user
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = '$user_id'";
$result = mysqli_query($conn, $query);
$user = mysqli_fetch_assoc($result);

// Proses ubah password
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validasi password lama
    if (!password_verify($current_password, $user['password'])) {
        setMessage('danger', 'Password lama tidak sesuai!');
        redirect('admin/change_password.php');
    }

    // Validasi password baru
    if ($new_password != $confirm_password) {
        setMessage('danger', 'Konfirmasi password baru tidak sesuai!');
        redirect('admin/change_password.php');
    }

    // Validasi panjang password
    if (strlen($new_password) < 6) {
        setMessage('danger', 'Password baru minimal 6 karakter!');
        redirect('admin/change_password.php');
    }

    // Update password
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    $query = "UPDATE users SET password = '$hashed_password' WHERE id = '$user_id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Password berhasil diubah!');
        // Tambahkan parameter untuk menampilkan SweetAlert
        $_SESSION['show_sweet_alert'] = true;
    } else {
        setMessage('danger', 'Gagal mengubah password!');
    }

    redirect('admin/change_password.php');
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Ubah Password</h1>
        <a href="profile.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Profil
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Form Ubah Password</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="" id="passwordForm">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Password Lama</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="current_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">Password Baru</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="new_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength mt-2">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <small class="text-muted">Kekuatan Password: <span id="strength-text">Sangat Lemah</span></small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Konfirmasi Password Baru</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="confirm_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div id="password-match" class="form-text"></div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-key me-2"></i> Ubah Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Tips Keamanan</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>Gunakan minimal 8 karakter</li>
                        <li>Kombinasikan huruf besar, huruf kecil, angka, dan simbol</li>
                        <li>Jangan gunakan informasi pribadi seperti tanggal lahir</li>
                        <li>Jangan gunakan password yang sama untuk akun lain</li>
                        <li>Ganti password secara berkala</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
document.querySelectorAll('.toggle-password').forEach(button => {
    button.addEventListener('click', function() {
        const targetId = this.getAttribute('data-target');
        const input = document.getElementById(targetId);
        const icon = this.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
});

// Check password strength
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const progressBar = document.querySelector('.progress-bar');
    const strengthText = document.getElementById('strength-text');
    
    // Calculate strength
    let strength = 0;
    if (password.length >= 8) strength += 20;
    if (password.match(/[a-z]+/)) strength += 20;
    if (password.match(/[A-Z]+/)) strength += 20;
    if (password.match(/[0-9]+/)) strength += 20;
    if (password.match(/[^a-zA-Z0-9]+/)) strength += 20;
    
    // Update UI
    progressBar.style.width = strength + '%';
    
    // Set color based on strength
    if (strength < 40) {
        progressBar.className = 'progress-bar bg-danger';
        strengthText.textContent = 'Lemah';
    } else if (strength < 80) {
        progressBar.className = 'progress-bar bg-warning';
        strengthText.textContent = 'Sedang';
    } else {
        progressBar.className = 'progress-bar bg-success';
        strengthText.textContent = 'Kuat';
    }
});

// Check if passwords match
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    const matchText = document.getElementById('password-match');
    
    if (newPassword === confirmPassword) {
        matchText.textContent = 'Password cocok';
        matchText.className = 'form-text text-success';
    } else {
        matchText.textContent = 'Password tidak cocok';
        matchText.className = 'form-text text-danger';
    }
});

// Tampilkan SweetAlert jika ada pesan sukses
document.addEventListener('DOMContentLoaded', function() {
    <?php if (isset($_SESSION['message']) && isset($_SESSION['show_sweet_alert']) && $_SESSION['show_sweet_alert']): ?>
        Swal.fire({
            icon: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'success' : 'error'; ?>',
            title: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'Berhasil!' : 'Gagal!'; ?>',
            text: '<?php echo $_SESSION['message']['text']; ?>',
            timer: 2000,
            showConfirmButton: false
        });
        <?php unset($_SESSION['show_sweet_alert']); ?>
    <?php endif; ?>
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
