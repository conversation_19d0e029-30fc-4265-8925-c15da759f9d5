<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses update hari kerja
if (isset($_POST['update'])) {
    $bidang_id = clean($_POST['bidang_id']);

    // Tambahkan data hari kerja baru
    $hari = ['Senin', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Jumat', 'Sabtu', 'Minggu'];
    $success = true;

    foreach ($hari as $h) {
        $status = isset($_POST['hari'][$h]) ? 1 : 0;

        // Cek apakah data sudah ada
        $check_query = "SELECT id FROM hari_kerja WHERE bidang_id = '$bidang_id' AND hari = '$h'";
        $check_result = mysqli_query($conn, $check_query);

        if (mysqli_num_rows($check_result) > 0) {
            // Update data hari kerja
            $query = "UPDATE hari_kerja SET status = '$status' WHERE bidang_id = '$bidang_id' AND hari = '$h'";
            if (!mysqli_query($conn, $query)) {
                error_log("Error updating hari_kerja: " . mysqli_error($conn) . " - Query: " . $query);
                $success = false;
            }
        } else {
            // Insert data hari kerja
            $query = "INSERT INTO hari_kerja (bidang_id, hari, status) VALUES ('$bidang_id', '$h', '$status')";
            if (!mysqli_query($conn, $query)) {
                error_log("Error inserting hari_kerja: " . mysqli_error($conn) . " - Query: " . $query);
                $success = false;
            }
        }
    }

    if ($success) {
        setMessage('success', 'Konfigurasi hari kerja berhasil diperbarui!');
    } else {
        setMessage('danger', 'Terjadi kesalahan saat memperbarui konfigurasi hari kerja. Silakan coba lagi.');
    }
    redirect('admin/hari_kerja.php?bidang_id=' . $bidang_id);
}

// Cek apakah tabel hari_kerja sudah ada
$query = "SHOW TABLES LIKE 'hari_kerja'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('warning', 'Database perlu diperbarui. Silakan klik tombol Update Database.');
    redirect('admin/update_database.php');
}

// Ambil data bidang
$query = "SELECT * FROM bidang ORDER BY nama_bidang ASC";
$result = mysqli_query($conn, $query);

$bidang = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $bidang[] = $row;
    }
}

// Ambil data bidang yang dipilih
$selected_bidang_id = isset($_GET['bidang_id']) ? clean($_GET['bidang_id']) : (isset($bidang[0]) ? $bidang[0]['id'] : null);

// Ambil data hari kerja untuk bidang yang dipilih
$hari_kerja = [];
if ($selected_bidang_id) {
    // Cek apakah data sudah ada
    $check_query = "SELECT COUNT(*) as count FROM hari_kerja WHERE bidang_id = '$selected_bidang_id'";
    $check_result = mysqli_query($conn, $check_query);
    $check_row = mysqli_fetch_assoc($check_result);

    // Jika data belum ada, inisialisasi data hari kerja
    if ($check_row['count'] == 0) {
        $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
        $default_status = [1, 1, 1, 1, 1, 0, 0]; // Senin-Jumat kerja, Sabtu-Minggu libur

        for ($i = 0; $i < count($hari); $i++) {
            $h = $hari[$i];
            $status = $default_status[$i];

            $query = "INSERT INTO hari_kerja (bidang_id, hari, status)
                      VALUES ('$selected_bidang_id', '$h', '$status')";
            if (!mysqli_query($conn, $query)) {
                error_log("Error inserting hari_kerja: " . mysqli_error($conn) . " - Query: " . $query);
            }
        }

        // Log inisialisasi
        error_log("Initialized hari_kerja for bidang_id: $selected_bidang_id");
    }

    // Ambil data hari kerja
    $query = "SELECT * FROM hari_kerja WHERE bidang_id = '$selected_bidang_id' ORDER BY FIELD(hari, 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu')";
    $result = mysqli_query($conn, $query);

    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $hari_kerja[$row['hari']] = $row['status'];
        }
    }
}

// Ambil nama bidang yang dipilih
$selected_bidang_name = '';
foreach ($bidang as $b) {
    if ($b['id'] == $selected_bidang_id) {
        $selected_bidang_name = $b['nama_bidang'];
        break;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Konfigurasi Hari Kerja</h1>
        <div>
            <a href="update_database.php" class="btn btn-warning me-2">
                <i class="fas fa-database"></i> Update Database
            </a>
            <a href="bidang.php" class="btn btn-info">
                <i class="fas fa-building"></i> Kelola Bidang
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Pilih Bidang</h6>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <?php foreach ($bidang as $b): ?>
                            <a href="hari_kerja.php?bidang_id=<?php echo $b['id']; ?>" class="list-group-item list-group-item-action <?php echo ($b['id'] == $selected_bidang_id) ? 'active' : ''; ?>">
                                <?php echo $b['nama_bidang']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Hari Kerja untuk Bidang: <?php echo $selected_bidang_name; ?></h6>
                </div>
                <div class="card-body">
                    <?php if ($selected_bidang_id): ?>
                        <form method="post" action="">
                            <input type="hidden" name="bidang_id" value="<?php echo $selected_bidang_id; ?>">

                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Hari</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
                                        foreach ($hari as $h):
                                        ?>
                                            <tr>
                                                <td><?php echo $h; ?></td>
                                                <td>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="hari_<?php echo $h; ?>" name="hari[<?php echo $h; ?>]" <?php echo (isset($hari_kerja[$h]) && $hari_kerja[$h]) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="hari_<?php echo $h; ?>">
                                                            <?php echo (isset($hari_kerja[$h]) && $hari_kerja[$h]) ? 'Hari Kerja' : 'Hari Libur'; ?>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-3">
                                <button type="submit" name="update" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Simpan Perubahan
                                </button>
                                <a href="jam_kerja_bidang.php?bidang_id=<?php echo $selected_bidang_id; ?>" class="btn btn-info ms-2">
                                    <i class="fas fa-calendar-alt"></i> Atur Jam Kerja Bidang
                                </a>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <p>Silakan pilih bidang terlebih dahulu.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informasi Hari Kerja</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5 class="alert-heading">Penggunaan Hari Kerja:</h5>
                        <ul>
                            <li>Hari kerja digunakan untuk menentukan hari apa saja karyawan harus bekerja.</li>
                            <li>Karyawan tidak perlu melakukan absensi pada hari yang bukan hari kerja.</li>
                            <li>Setiap bidang dapat memiliki konfigurasi hari kerja yang berbeda.</li>
                        </ul>
                        <hr>
                        <p class="mb-0">Pastikan untuk mengatur hari kerja sesuai dengan kebijakan perusahaan untuk setiap bidang.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Update label saat checkbox diubah
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('.form-check-input');

        checkboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                const label = this.nextElementSibling;
                if (this.checked) {
                    label.textContent = 'Hari Kerja';
                } else {
                    label.textContent = 'Hari Libur';
                }
            });
        });
    });
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
