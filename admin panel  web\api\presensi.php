<?php
/**
 * API untuk melakukan presensi dari aplikasi mobile
 */

// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header untuk JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Hanya terima method POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Method tidak diizinkan'
    ]);
    exit;
}

// Ambil data JSON dari request body
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// Validasi data
if (!$data || !isset($data['user_id']) || !isset($data['jenis_absen']) || !isset($data['latitude']) || !isset($data['longitude'])) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'Data tidak lengkap'
    ]);
    exit;
}

$user_id = clean($data['user_id']);
$jenis_absen = clean($data['jenis_absen']); // 'masuk' atau 'pulang'
$latitude = clean($data['latitude']);
$longitude = clean($data['longitude']);
$foto_base64 = isset($data['foto']) ? $data['foto'] : null;

// Validasi jenis absen
if (!in_array($jenis_absen, ['masuk', 'pulang'])) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'Jenis absen tidak valid'
    ]);
    exit;
}

// Ambil data karyawan
$query = "SELECT u.*, b.nama_bidang, l.nama_lokasi, l.latitude as lokasi_lat, l.longitude as lokasi_lng, l.radius
          FROM users u 
          LEFT JOIN bidang b ON u.bidang_id = b.id 
          LEFT JOIN lokasi l ON u.lokasi_id = l.id 
          WHERE u.id = '$user_id' AND u.role = 'karyawan'";
$result = mysqli_query($conn, $query);

if (!$result || mysqli_num_rows($result) == 0) {
    http_response_code(404);
    echo json_encode([
        'status' => 'error',
        'message' => 'Karyawan tidak ditemukan'
    ]);
    exit;
}

$karyawan = mysqli_fetch_assoc($result);

// Validasi lokasi (jika ada lokasi yang ditetapkan)
if ($karyawan['lokasi_lat'] && $karyawan['lokasi_lng'] && $karyawan['radius']) {
    $distance = calculateDistance($latitude, $longitude, $karyawan['lokasi_lat'], $karyawan['lokasi_lng']);
    if ($distance > $karyawan['radius']) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Anda berada di luar radius lokasi absensi',
            'data' => [
                'jarak' => round($distance, 2),
                'radius_maksimal' => $karyawan['radius']
            ]
        ]);
        exit;
    }
}

// Ambil jam kerja
$jam_kerja = null;
if ($karyawan['bidang_id']) {
    $query_jam = "SELECT * FROM jam_kerja WHERE bidang_id = '{$karyawan['bidang_id']}'";
    $result_jam = mysqli_query($conn, $query_jam);
    if ($result_jam && mysqli_num_rows($result_jam) > 0) {
        $jam_kerja = mysqli_fetch_assoc($result_jam);
    }
}

if (!$jam_kerja) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Jam kerja belum diatur untuk bidang Anda'
    ]);
    exit;
}

$today = date('Y-m-d');
$current_time = date('H:i:s');

// Cek presensi hari ini
$query_presensi = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result_presensi = mysqli_query($conn, $query_presensi);
$presensi_hari_ini = mysqli_num_rows($result_presensi) > 0 ? mysqli_fetch_assoc($result_presensi) : null;

// Simpan foto jika ada
$nama_foto = null;
if ($foto_base64) {
    $foto_data = base64_decode($foto_base64);
    $nama_foto = 'presensi_' . $user_id . '_' . $jenis_absen . '_' . date('YmdHis') . '.jpg';
    $path_foto = '../uploads/' . $nama_foto;
    
    if (!file_put_contents($path_foto, $foto_data)) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Gagal menyimpan foto'
        ]);
        exit;
    }
}

$lokasi_string = $latitude . ',' . $longitude;

if ($jenis_absen === 'masuk') {
    // Validasi waktu absen masuk
    if ($current_time > $jam_kerja['akhir_jam_masuk']) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Waktu absen masuk sudah berakhir'
        ]);
        exit;
    }
    
    // Cek apakah sudah absen masuk
    if ($presensi_hari_ini) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Anda sudah melakukan absen masuk hari ini'
        ]);
        exit;
    }
    
    // Tentukan status
    $status = ($current_time <= $jam_kerja['jam_masuk']) ? 'Tepat Waktu' : 'Terlambat';
    
    // Insert presensi masuk
    $query_insert = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, status) 
                     VALUES ('$user_id', '$today', '$current_time', '$nama_foto', '$lokasi_string', '$status')";
    
    if (mysqli_query($conn, $query_insert)) {
        echo json_encode([
            'status' => 'success',
            'message' => 'Absen masuk berhasil',
            'data' => [
                'jenis_absen' => 'masuk',
                'waktu' => $current_time,
                'status' => $status,
                'foto' => $nama_foto
            ]
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'Gagal menyimpan data absensi'
        ]);
    }
    
} else { // pulang
    // Cek apakah sudah absen masuk
    if (!$presensi_hari_ini) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Anda belum melakukan absen masuk'
        ]);
        exit;
    }
    
    // Cek apakah sudah absen pulang
    if ($presensi_hari_ini['jam_pulang']) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Anda sudah melakukan absen pulang hari ini'
        ]);
        exit;
    }
    
    // Update presensi pulang
    $query_update = "UPDATE presensi 
                     SET jam_pulang = '$current_time', foto_pulang = '$nama_foto', lokasi_pulang = '$lokasi_string'
                     WHERE id = '{$presensi_hari_ini['id']}'";
    
    if (mysqli_query($conn, $query_update)) {
        echo json_encode([
            'status' => 'success',
            'message' => 'Absen pulang berhasil',
            'data' => [
                'jenis_absen' => 'pulang',
                'waktu' => $current_time,
                'foto' => $nama_foto
            ]
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'Gagal menyimpan data absensi'
        ]);
    }
}

// Fungsi untuk menghitung jarak antara dua koordinat
function calculateDistance($lat1, $lon1, $lat2, $lon2) {
    $earth_radius = 6371000; // meter
    
    $lat1_rad = deg2rad($lat1);
    $lat2_rad = deg2rad($lat2);
    $delta_lat = deg2rad($lat2 - $lat1);
    $delta_lon = deg2rad($lon2 - $lon1);
    
    $a = sin($delta_lat/2) * sin($delta_lat/2) + cos($lat1_rad) * cos($lat2_rad) * sin($delta_lon/2) * sin($delta_lon/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $earth_radius * $c;
}
?>
