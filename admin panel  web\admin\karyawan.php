<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Proses tambah karyawan
if (isset($_POST['tambah'])) {
    $nik = clean($_POST['nik']);
    $nama = clean($_POST['nama']);
    $bidang_id = clean($_POST['bidang_id']);
    $jabatan = clean($_POST['jabatan']);
    $lokasi_id = clean($_POST['lokasi_id']);
    $password = clean($_POST['password']);

    // Validasi bidang_id
    if (empty($bidang_id)) {
        setMessage('danger', 'Bidang harus dipilih!');
        redirect('admin/karyawan.php');
    }

    // Cek apakah bidang_id valid
    $query = "SELECT * FROM bidang WHERE id = '$bidang_id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) == 0) {
        setMessage('danger', 'Bidang yang dipilih tidak valid!');
        redirect('admin/karyawan.php');
    }

    // Upload foto profil
    $foto_profil = null;
    if (isset($_FILES['foto_profil']) && $_FILES['foto_profil']['error'] == 0) {
        $file_tmp = $_FILES['foto_profil']['tmp_name'];
        $file_name = $_FILES['foto_profil']['name'];
        $file_size = $_FILES['foto_profil']['size'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Cek ekstensi file
        if (in_array($file_ext, ALLOWED_EXTENSIONS)) {
            // Cek ukuran file
            if ($file_size <= MAX_UPLOAD_SIZE) {
                $foto_profil = 'profile_' . time() . '.' . $file_ext;
                $upload_path = UPLOAD_PATH . $foto_profil;

                if (move_uploaded_file($file_tmp, $upload_path)) {
                    // Berhasil upload
                } else {
                    setMessage('danger', 'Gagal mengupload foto profil!');
                    redirect('admin/karyawan.php');
                }
            } else {
                setMessage('danger', 'Ukuran file terlalu besar! Maksimal ' . (MAX_UPLOAD_SIZE / 1024 / 1024) . 'MB');
                redirect('admin/karyawan.php');
            }
        } else {
            setMessage('danger', 'Ekstensi file tidak diizinkan! Hanya ' . implode(', ', ALLOWED_EXTENSIONS) . ' yang diizinkan');
            redirect('admin/karyawan.php');
        }
    }

    // Daftarkan karyawan
    if (registerKaryawan($nik, $nama, $bidang_id, $jabatan, $lokasi_id, $password, $foto_profil)) {
        setMessage('success', 'Karyawan berhasil ditambahkan!');
    } else {
        setMessage('danger', 'Gagal menambahkan karyawan! NIK mungkin sudah terdaftar atau bidang yang dipilih tidak valid.');
    }

    redirect('admin/karyawan.php');
}

// Kode untuk edit karyawan dan reset password telah dipindahkan ke file terpisah

// Proses hapus karyawan
if (isset($_GET['hapus'])) {
    $id = clean($_GET['hapus']);

    // Cek apakah karyawan memiliki data presensi
    $query = "SELECT * FROM presensi WHERE user_id = '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Karyawan tidak dapat dihapus karena memiliki data presensi!');
        redirect('admin/karyawan.php');
    }

    // Hapus foto profil jika ada
    $query = "SELECT foto_profil FROM users WHERE id = '$id'";
    $result = mysqli_query($conn, $query);
    $row = mysqli_fetch_assoc($result);

    if (!empty($row['foto_profil'])) {
        $file = UPLOAD_PATH . $row['foto_profil'];
        if (file_exists($file)) {
            unlink($file);
        }
    }

    // Hapus karyawan
    $query = "DELETE FROM users WHERE id = '$id' AND role = 'karyawan'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Karyawan berhasil dihapus!');
    } else {
        setMessage('danger', 'Gagal menghapus karyawan!');
    }

    redirect('admin/karyawan.php');
}

// Ambil data karyawan
$query = "SELECT u.*, l.nama_lokasi, b.nama_bidang
          FROM users u
          LEFT JOIN lokasi l ON u.lokasi_id = l.id
          LEFT JOIN bidang b ON u.bidang_id = b.id
          WHERE u.role = 'karyawan'
          ORDER BY u.nama ASC";
$result = mysqli_query($conn, $query);

$karyawan = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $karyawan[] = $row;
    }
}

// Ambil data lokasi untuk dropdown
$query = "SELECT * FROM lokasi ORDER BY nama_lokasi ASC";
$result = mysqli_query($conn, $query);

$lokasi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $lokasi[] = $row;
    }
}

// Ambil data bidang untuk dropdown
$query = "SELECT * FROM bidang ORDER BY nama_bidang ASC";
$result = mysqli_query($conn, $query);

$bidang_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $bidang_list[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Manajemen Karyawan</h1>
        <div>
            <a href="update_bidang.php" class="btn btn-info me-2">
                <i class="fas fa-sync-alt"></i> Update Kolom Bidang
            </a>
            <a href="add_barcode_permission.php" class="btn btn-info me-2">
                <i class="fas fa-qrcode"></i> Tambah Kolom Barcode
            </a>
            <a href="add_face_permission.php" class="btn btn-info me-2">
                <i class="fas fa-camera"></i> Tambah Kolom Wajah
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tambahKaryawanModal" <?php echo empty($bidang_list) ? 'disabled' : ''; ?>>
                <i class="fas fa-plus"></i> Tambah Karyawan
            </button>
        </div>
    </div>

    <?php if (empty($bidang_list)): ?>
    <div class="alert alert-warning mb-4">
        <i class="fas fa-exclamation-triangle me-2"></i> Anda harus menambahkan data bidang terlebih dahulu sebelum dapat menambahkan karyawan.
        <a href="bidang.php" class="btn btn-sm btn-warning ms-2">
            <i class="fas fa-plus"></i> Tambah Bidang
        </a>
    </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Data Karyawan</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Bidang</th>
                            <th>Jabatan</th>
                            <th>Lokasi</th>
                            <th>Foto Profil</th>
                            <th>Absen Barcode</th>
                            <th>Absen Wajah</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($karyawan)): ?>
                            <tr>
                                <td colspan="8" class="text-center">Tidak ada data karyawan</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($karyawan as $k): ?>
                                <tr>
                                    <td><?php echo $k['nik']; ?></td>
                                    <td><?php echo $k['nama']; ?></td>
                                    <td><?php echo $k['nama_bidang'] ?? $k['bidang']; ?></td>
                                    <td><?php echo $k['jabatan']; ?></td>
                                    <td><?php echo $k['nama_lokasi'] ?? '-'; ?></td>
                                    <td>
                                        <?php if (!empty($k['foto_profil'])): ?>
                                            <a href="<?php echo BASE_URL . 'uploads/' . $k['foto_profil']; ?>" target="_blank" class="btn btn-sm btn-info">
                                                <i class="fas fa-image"></i> Lihat
                                            </a>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if (isset($k['allow_barcode']) && $k['allow_barcode'] == 1): ?>
                                            <span class="badge bg-success"><i class="fas fa-check"></i> Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=barcode&value=0" class="btn btn-sm btn-outline-danger mt-1" title="Nonaktifkan izin barcode">
                                                <i class="fas fa-ban"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><i class="fas fa-times"></i> Tidak Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=barcode&value=1" class="btn btn-sm btn-outline-success mt-1" title="Aktifkan izin barcode">
                                                <i class="fas fa-check"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if (isset($k['allow_face']) && $k['allow_face'] == 1): ?>
                                            <span class="badge bg-success"><i class="fas fa-check"></i> Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=face&value=0" class="btn btn-sm btn-outline-danger mt-1" title="Nonaktifkan izin wajah">
                                                <i class="fas fa-ban"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><i class="fas fa-times"></i> Tidak Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=face&value=1" class="btn btn-sm btn-outline-success mt-1" title="Aktifkan izin wajah">
                                                <i class="fas fa-check"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="edit_karyawan.php?id=<?php echo $k['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="reset_password.php?id=<?php echo $k['id']; ?>" class="btn btn-sm btn-warning">
                                            <i class="fas fa-key"></i> Reset Password
                                        </a>
                                        <a href="karyawan.php?hapus=<?php echo $k['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus karyawan ini?')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </a>
                                    </td>
                                </tr>


                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Karyawan -->
<div class="modal fade add-modal" id="tambahKaryawanModal" tabindex="-1" aria-labelledby="tambahKaryawanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tambahKaryawanModalLabel">Tambah Karyawan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="" enctype="multipart/form-data" class="add-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nik_add" class="form-label">NIK</label>
                        <input type="text" class="form-control" id="nik_add" name="nik" required>
                    </div>

                    <div class="mb-3">
                        <label for="nama_add" class="form-label">Nama</label>
                        <input type="text" class="form-control" id="nama_add" name="nama" required>
                    </div>

                    <div class="mb-3">
                        <label for="bidang_id_add" class="form-label">Bidang</label>
                        <select class="form-select" id="bidang_id_add" name="bidang_id" required>
                            <option value="">Pilih Bidang</option>
                            <?php if (empty($bidang_list)): ?>
                                <option value="" disabled>Tidak ada data bidang. Tambahkan bidang terlebih dahulu.</option>
                            <?php else: ?>
                                <?php foreach ($bidang_list as $b): ?>
                                    <option value="<?php echo $b['id']; ?>"><?php echo $b['nama_bidang']; ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <?php if (empty($bidang_list)): ?>
                            <div class="form-text text-danger">Anda harus menambahkan data bidang terlebih dahulu di menu Bidang.</div>
                            <a href="bidang.php" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-plus"></i> Tambah Bidang
                            </a>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="jabatan_add" class="form-label">Jabatan</label>
                        <input type="text" class="form-control" id="jabatan_add" name="jabatan" required>
                    </div>

                    <div class="mb-3">
                        <label for="lokasi_id_add" class="form-label">Lokasi</label>
                        <select class="form-select" id="lokasi_id_add" name="lokasi_id" required>
                            <option value="">Pilih Lokasi</option>
                            <?php foreach ($lokasi as $l): ?>
                                <option value="<?php echo $l['id']; ?>"><?php echo $l['nama_lokasi']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="password_add" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password_add" name="password" required>
                    </div>

                    <div class="mb-3">
                        <label for="foto_profil_add" class="form-label">Foto Profil</label>
                        <input type="file" class="form-control" id="foto_profil_add" name="foto_profil">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="tambah" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
