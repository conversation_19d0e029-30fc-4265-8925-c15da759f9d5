#!/bin/bash

echo "========================================"
echo "   BUILD APK APLIKASI ABSENSI KARYAWAN"
echo "========================================"
echo

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "ERROR: Flutter tidak ditemukan!"
    echo "<PERSON>lakan install Flutter terlebih dahulu."
    echo "Download dari: https://docs.flutter.dev/get-started/install"
    exit 1
fi

echo "[1/5] Checking Flutter installation..."
flutter doctor --android-licenses

echo
echo "[2/5] Cleaning project..."
flutter clean

echo
echo "[3/5] Getting dependencies..."
flutter pub get

echo
echo "[4/5] Building APK..."
echo "Pilih jenis build:"
echo "1. Debug APK (untuk testing)"
echo "2. Release APK (untuk production)"
echo "3. Release APK dengan optimasi"
read -p "Ma<PERSON><PERSON><PERSON> pilihan (1-3): " choice

case $choice in
    1)
        echo "Building Debug APK..."
        flutter build apk --debug
        apk_file="app-debug.apk"
        ;;
    2)
        echo "Building Release APK..."
        flutter build apk --release
        apk_file="app-release.apk"
        ;;
    3)
        echo "Building Optimized Release APK..."
        flutter build apk --release --obfuscate --split-debug-info=build/debug-info
        apk_file="app-release.apk"
        ;;
    *)
        echo "Pilihan tidak valid!"
        exit 1
        ;;
esac

echo
echo "[5/5] Build completed!"

# Check if APK was created successfully
if [ -f "build/app/outputs/flutter-apk/$apk_file" ]; then
    echo
    echo "========================================"
    echo "          BUILD BERHASIL!"
    echo "========================================"
    echo
    echo "File APK tersedia di:"
    echo "$(pwd)/build/app/outputs/flutter-apk/$apk_file"
    echo
    echo "Ukuran file:"
    ls -lh "build/app/outputs/flutter-apk/$apk_file" | awk '{print $5}'
    echo
    echo "Cara install di Android:"
    echo "1. Copy file APK ke perangkat Android"
    echo "2. Enable 'Unknown Sources' di Settings"
    echo "3. Tap file APK dan install"
    echo
    
    # Ask if user wants to open the APK folder
    read -p "Buka folder APK? (y/n): " open_folder
    if [[ $open_folder == "y" || $open_folder == "Y" ]]; then
        if command -v xdg-open &> /dev/null; then
            xdg-open "build/app/outputs/flutter-apk"
        elif command -v open &> /dev/null; then
            open "build/app/outputs/flutter-apk"
        else
            echo "Silakan buka folder: build/app/outputs/flutter-apk"
        fi
    fi
else
    echo
    echo "========================================"
    echo "          BUILD GAGAL!"
    echo "========================================"
    echo
    echo "Silakan cek error di atas dan coba lagi."
    echo
    echo "Tips troubleshooting:"
    echo "1. Pastikan Android SDK terinstall"
    echo "2. Jalankan: flutter doctor"
    echo "3. Pastikan ada koneksi internet"
fi

echo
