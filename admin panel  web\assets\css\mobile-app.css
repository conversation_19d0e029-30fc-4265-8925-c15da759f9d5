/* Mobile App Style for Karyawan Dashboard */

/* Base Styles */
.mobile-app-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 0;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

@media (min-width: 768px) {
    .mobile-app-container {
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .mobile-app-container {
        max-width: 720px;
    }
}

@media (min-width: 1200px) {
    .mobile-app-container {
        max-width: 840px;
    }
}

.mobile-app-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 20px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
}

.mobile-app-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.mobile-app-header .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
    overflow: hidden;
    position: relative;
    padding: 2px;
}

.mobile-app-header .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
    object-position: center;
    background-color: rgba(255, 255, 255, 0.2);
}

.mobile-app-header .user-avatar .user-photo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
    max-height: 100%;
}

.mobile-app-header .user-details {
    flex: 1;
}

.mobile-app-header .user-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.mobile-app-header .user-position {
    font-size: 14px;
    opacity: 0.8;
}

.mobile-app-header .left-section {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.mobile-app-header .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    margin-right: 15px;
}

.mobile-app-header .date-info {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 15px;
    font-size: 14px;
    align-self: flex-start;
}

.mobile-app-header .date-info i {
    margin-right: 5px;
}

.mobile-app-header .logout-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.mobile-app-header .logout-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Status Card */
.status-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 12px;
    margin-bottom: 12px;
}

.status-card .status-title {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 10px;
}

.status-card .status-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.status-card .status-value {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
}

.status-card .status-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.status-card .status-icon.primary {
    background-color: #4e73df;
}

.status-card .status-icon.success {
    background-color: #1cc88a;
}

.status-card .status-icon.warning {
    background-color: #f6c23e;
}

.status-card .status-icon.info {
    background-color: #36b9cc;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.action-button {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 12px 8px;
    text-align: center;
    transition: all 0.3s ease;
}

.action-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-button .action-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    color: white;
    font-size: 18px;
}

.action-button .action-icon.primary {
    background-color: #4e73df;
}

.action-button .action-icon.success {
    background-color: #1cc88a;
}

.action-button .action-icon.warning {
    background-color: #f6c23e;
}

.action-button .action-icon.info {
    background-color: #36b9cc;
}

.action-button .action-icon.danger {
    background-color: #e74a3b;
}

.action-button .action-icon.secondary {
    background-color: #6c757d;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    position: relative;
}

.section-header h6 {
    margin-bottom: 0;
}

.toggle-arrow-button {
    background: none;
    border: none;
    color: #4e73df;
    font-size: 16px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background-color: rgba(78, 115, 223, 0.1);
}

.toggle-arrow-button:hover {
    background-color: rgba(78, 115, 223, 0.2);
}

.toggle-arrow-button:focus {
    outline: none;
}

.toggle-arrow-button.active i {
    transform: rotate(180deg);
}

.toggle-arrow-button i {
    transition: transform 0.3s ease;
}

.additional-menu {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    max-height: 0;
    opacity: 0;
}

.additional-menu.show {
    max-height: 500px;
    opacity: 1;
}

.action-button .action-text {
    font-size: 14px;
    color: #343a40;
    font-weight: 500;
}

/* Attendance Button */
.attendance-button {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    text-align: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
    font-size: 50px;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(78, 115, 223, 0.7);
        transform: translateX(-50%) rotate(0deg);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(78, 115, 223, 0);
        transform: translateX(-50%) rotate(5deg);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(78, 115, 223, 0);
        transform: translateX(-50%) rotate(0deg);
    }
}

.attendance-button:hover {
    transform: translateX(-50%) translateY(-3px) scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
    background: linear-gradient(135deg, #3a5fcc 0%, #1a3da3 100%);
    animation: none;
}

.attendance-button:active {
    transform: translateX(-50%) translateY(0) scale(0.95);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* No longer needed as we've simplified the button */

/* Add space at the bottom of the container to prevent content from being hidden behind the floating button */
.mobile-app-container {
    padding-bottom: 100px;
}

/* Responsive adjustments for the attendance button */
@media (max-width: 576px) {
    .attendance-button {
        width: 70px;
        height: 70px;
        font-size: 30px;
        bottom: 15px;
    }
}

@media (min-width: 992px) {
    .attendance-button {
        width: 70px;
        height: 70px;
        font-size: 30px;
        bottom: 30px;
    }
}

/* Info Section */
.info-section {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 20px;
}

.info-section .info-title {
    font-size: 16px;
    font-weight: bold;
    color: #343a40;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.info-section .info-title i {
    margin-right: 10px;
    color: #4e73df;
}

.info-section .info-content {
    font-size: 14px;
    color: #6c757d;
}

.info-section .info-content ol {
    padding-left: 20px;
}

.info-section .info-content li {
    margin-bottom: 8px;
}

/* Badge Styles */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-badge.success {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.danger {
    background-color: #f8d7da;
    color: #721c24;
}

.status-icon.danger {
    background-color: #e74a3b;
}

.holiday-name {
    font-size: 14px;
    color: #721c24;
    margin-top: 5px;
    font-style: italic;
    font-weight: normal;
}

/* Mobile Adjustments */
@media (max-width: 576px) {
    .mobile-app-container {
        max-width: 100%;
        margin: 0 auto;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .container.px-0 {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }

    .mobile-app-header {
        border-radius: 0 0 20px 20px;
    }
}

/* Tablet Adjustments */
@media (min-width: 577px) and (max-width: 991px) {
    .mobile-app-container {
        border-radius: 0;
        box-shadow: none;
        margin: 0 auto;
    }

    .mobile-app-header {
        border-radius: 0 0 30px 30px;
        padding: 25px;
    }

    .mobile-app-header .user-avatar {
        width: 70px;
        height: 70px;
        font-size: 28px;
    }

    .mobile-app-header .user-name {
        font-size: 22px;
    }

    .mobile-app-header .user-position {
        font-size: 16px;
    }

    .mobile-app-header .date-info {
        font-size: 16px;
        padding: 12px 18px;
    }

    .status-card {
        padding: 20px;
        border-radius: 20px;
    }

    .status-card .status-title {
        font-size: 16px;
    }

    .status-card .status-value {
        font-size: 18px;
    }

    .status-card .status-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .quick-actions {
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
    }

    .action-button {
        padding: 15px 10px;
    }

    .action-button .action-text {
        font-size: 16px;
    }

    .container.px-0 {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
    }

    .attendance-button {
        padding: 20px;
        border-radius: 20px;
    }

    .attendance-button .btn-icon {
        font-size: 28px;
    }

    .attendance-button .btn-text {
        font-size: 18px;
    }

    .info-section {
        padding: 20px;
        border-radius: 20px;
    }

    .info-section .info-title {
        font-size: 18px;
    }

    .info-section .info-content {
        font-size: 16px;
    }

    .status-badge {
        font-size: 14px;
        padding: 6px 12px;
    }
}

/* Desktop Specific Adjustments */
@media (min-width: 992px) {
    .mobile-app-container {
        padding: 0;
        border-radius: 15px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        margin-top: 20px;
        margin-bottom: 20px;
        min-height: calc(100vh - 40px);
    }

    .mobile-app-header {
        margin-left: 0;
        margin-right: 0;
        margin-top: 0;
        border-radius: 15px 15px 20px 20px;
    }

    .container.px-0 {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }

    .quick-actions {
        gap: 12px;
    }

    .action-button {
        padding: 15px 10px;
    }
}
