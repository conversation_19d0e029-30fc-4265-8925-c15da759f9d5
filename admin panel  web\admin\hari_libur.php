<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses tambah hari libur
if (isset($_POST['tambah'])) {
    $nama_libur = clean($_POST['nama_libur']);
    $tanggal = clean($_POST['tanggal']);

    // Cek apakah tanggal sudah terdaftar
    $query = "SELECT * FROM hari_libur WHERE tanggal = '$tanggal'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Tanggal tersebut sudah terdaftar sebagai hari libur!');
        redirect('admin/hari_libur.php');
    }

    // Insert data hari libur baru
    $query = "INSERT INTO hari_libur (nama_libur, tanggal) VALUES ('$nama_libur', '$tanggal')";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Hari libur berhasil ditambahkan!');
    } else {
        setMessage('danger', 'Gagal menambahkan hari libur!');
    }

    redirect('admin/hari_libur.php');
}

// Proses edit hari libur
if (isset($_POST['edit'])) {
    $id = clean($_POST['id']);
    $nama_libur = clean($_POST['nama_libur']);
    $tanggal = clean($_POST['tanggal']);

    // Cek apakah tanggal sudah terdaftar (selain hari libur ini)
    $query = "SELECT * FROM hari_libur WHERE tanggal = '$tanggal' AND id != '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Tanggal tersebut sudah terdaftar sebagai hari libur!');
        redirect('admin/hari_libur.php');
    }

    // Update data hari libur
    $query = "UPDATE hari_libur SET nama_libur = '$nama_libur', tanggal = '$tanggal' WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Data hari libur berhasil diperbarui!');
        // Tambahkan parameter untuk menampilkan SweetAlert
        $_SESSION['show_sweet_alert'] = true;
    } else {
        setMessage('danger', 'Gagal memperbarui data hari libur!');
    }

    redirect('admin/hari_libur.php');
}

// Proses hapus hari libur
if (isset($_GET['hapus'])) {
    $id = clean($_GET['hapus']);

    // Hapus hari libur
    $query = "DELETE FROM hari_libur WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Hari libur berhasil dihapus!');
    } else {
        setMessage('danger', 'Gagal menghapus hari libur!');
    }

    redirect('admin/hari_libur.php');
}

// Ambil data hari libur
$query = "SELECT * FROM hari_libur ORDER BY tanggal ASC";
$result = mysqli_query($conn, $query);

$hari_libur = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $hari_libur[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<style>
/* CSS untuk form edit inline */
.bg-light {
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Memperbaiki tampilan form pada layar kecil */
@media (max-width: 768px) {
    .row > div {
        margin-bottom: 15px;
    }
}

/* Memperbaiki tampilan tabel */
.table td {
    vertical-align: middle;
}

/* Animasi transisi untuk form edit */
tr {
    transition: all 0.3s ease;
}

/* Highlight untuk baris yang sedang diedit */
.editing-row {
    background-color: #e8f4ff !important;
}
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Manajemen Hari Libur</h1>
        <div>
            <a href="sync_hari_libur.php" class="btn btn-success me-2">
                <i class="fas fa-sync-alt"></i> Sinkronisasi dari API
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tambahHariLiburModal">
                <i class="fas fa-plus"></i> Tambah Hari Libur
            </button>
        </div>
    </div>

    <div class="alert alert-info mb-4">
        <h5 class="alert-heading">Informasi Hari Libur:</h5>
        <p>Anda dapat menambahkan hari libur secara manual atau menggunakan fitur <strong>Sinkronisasi dari API</strong> untuk mengambil data hari libur nasional secara otomatis dari Kalender Indonesia.</p>
        <p>Fitur sinkronisasi akan mengambil data hari libur nasional untuk tahun yang dipilih dan menambahkannya ke database tanpa duplikasi.</p>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Data Hari Libur</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Nama Libur</th>
                            <th>Tanggal</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($hari_libur)): ?>
                            <tr>
                                <td colspan="3" class="text-center">Tidak ada data hari libur</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($hari_libur as $hl): ?>
                                <!-- Baris tampilan data -->
                                <tr id="view_<?php echo $hl['id']; ?>">
                                    <td><?php echo $hl['nama_libur']; ?></td>
                                    <td><?php echo date('d-m-Y', strtotime($hl['tanggal'])); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="toggleEdit(<?php echo $hl['id']; ?>)">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <a href="hari_libur.php?hapus=<?php echo $hl['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus hari libur ini?')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </a>
                                    </td>
                                </tr>

                                <!-- Baris form edit -->
                                <tr id="edit_<?php echo $hl['id']; ?>" style="display: none;">
                                    <td colspan="3">
                                        <form method="post" action="" class="p-3 bg-light rounded">
                                            <input type="hidden" name="id" value="<?php echo $hl['id']; ?>">

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="nama_libur_<?php echo $hl['id']; ?>" class="form-label">Nama Libur</label>
                                                    <input type="text" class="form-control" id="nama_libur_<?php echo $hl['id']; ?>" name="nama_libur" value="<?php echo $hl['nama_libur']; ?>" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="tanggal_<?php echo $hl['id']; ?>" class="form-label">Tanggal</label>
                                                    <input type="date" class="form-control" id="tanggal_<?php echo $hl['id']; ?>" name="tanggal" value="<?php echo $hl['tanggal']; ?>" required>
                                                </div>
                                            </div>

                                            <div class="text-end">
                                                <button type="button" class="btn btn-secondary" onclick="toggleEdit(<?php echo $hl['id']; ?>)">
                                                    <i class="fas fa-times"></i> Batal
                                                </button>
                                                <button type="submit" name="edit" class="btn btn-primary">
                                                    <i class="fas fa-save"></i> Simpan
                                                </button>
                                            </div>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Hari Libur -->
<div class="modal fade" id="tambahHariLiburModal" tabindex="-1" aria-labelledby="tambahHariLiburModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tambahHariLiburModalLabel">Tambah Hari Libur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nama_libur" class="form-label">Nama Libur</label>
                        <input type="text" class="form-control" id="nama_libur" name="nama_libur" required>
                    </div>

                    <div class="mb-3">
                        <label for="tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="tambah" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Fungsi untuk menampilkan/menyembunyikan form edit
function toggleEdit(id) {
    // Tampilkan/sembunyikan baris tampilan data
    const viewRow = document.getElementById('view_' + id);
    const editRow = document.getElementById('edit_' + id);

    // Sembunyikan semua form edit yang terbuka
    const allEditRows = document.querySelectorAll('[id^="edit_"]');
    const allViewRows = document.querySelectorAll('[id^="view_"]');

    allEditRows.forEach(row => {
        if (row.id !== 'edit_' + id) {
            row.style.display = 'none';
        }
    });

    allViewRows.forEach(row => {
        if (row.id !== 'view_' + id) {
            row.style.display = '';
            row.classList.remove('editing-row');
        }
    });

    // Toggle tampilan baris yang diklik
    if (editRow.style.display === 'none') {
        viewRow.classList.add('editing-row');
        editRow.style.display = '';
    } else {
        viewRow.classList.remove('editing-row');
        viewRow.style.display = '';
        editRow.style.display = 'none';
    }
}

// Tampilkan SweetAlert jika ada pesan sukses
document.addEventListener('DOMContentLoaded', function() {
    <?php if (isset($_SESSION['message']) && isset($_SESSION['show_sweet_alert']) && $_SESSION['show_sweet_alert']): ?>
        Swal.fire({
            icon: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'success' : 'error'; ?>',
            title: '<?php echo ($_SESSION['message']['type'] == 'success') ? 'Berhasil!' : 'Gagal!'; ?>',
            text: '<?php echo $_SESSION['message']['text']; ?>',
            timer: 2000,
            showConfirmButton: false
        });
        <?php unset($_SESSION['show_sweet_alert']); ?>
    <?php endif; ?>
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
