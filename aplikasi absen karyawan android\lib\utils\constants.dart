class Constants {
  // API Configuration
  static const String baseUrl = 'http://localhost/absenku/admin%20panel%20%20web/api';
  
  // API Endpoints
  static const String loginEndpoint = '$baseUrl/login.php';
  static const String dashboardEndpoint = '$baseUrl/dashboard.php';
  static const String presensiEndpoint = '$baseUrl/presensi.php';
  static const String riwayatEndpoint = '$baseUrl/riwayat.php';
  
  // SharedPreferences Keys
  static const String keyIsLoggedIn = 'is_logged_in';
  static const String keyUserId = 'user_id';
  static const String keyUserNik = 'user_nik';
  static const String keyUserName = 'user_name';
  static const String keyUserBidang = 'user_bidang';
  static const String keyUserJabatan = 'user_jabatan';
  static const String keyUserLokasi = 'user_lokasi';
  static const String keyUserFoto = 'user_foto';
  
  // App Configuration
  static const String appName = 'Absensi Karyawan';
  static const String appVersion = '1.0.0';
  
  // Image Configuration
  static const int maxImageSize = 1024 * 1024; // 1MB
  static const int imageQuality = 80;
  
  // Location Configuration
  static const double locationAccuracy = 10.0; // meters
  static const int locationTimeout = 30; // seconds
  
  // Status Messages
  static const String msgLoginSuccess = 'Login berhasil';
  static const String msgLoginFailed = 'Login gagal';
  static const String msgPresensiSuccess = 'Presensi berhasil';
  static const String msgPresensiFailed = 'Presensi gagal';
  static const String msgNetworkError = 'Tidak ada koneksi internet';
  static const String msgServerError = 'Terjadi kesalahan server';
  static const String msgLocationError = 'Gagal mendapatkan lokasi';
  static const String msgCameraError = 'Gagal mengakses kamera';
  static const String msgPermissionDenied = 'Izin ditolak';
  
  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String apiDateFormat = 'yyyy-MM-dd';
  static const String apiTimeFormat = 'HH:mm:ss';
}
