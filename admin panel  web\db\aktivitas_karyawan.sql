-- <PERSON><PERSON> untuk menyimpan data aktivitas karyawan
CREATE TABLE IF NOT EXISTS `aktivitas_karyawan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `tanggal` date NOT NULL,
  `waktu` time NOT NULL,
  `longitude` varchar(50) NOT NULL,
  `latitude` varchar(50) NOT NULL,
  `alamat` text DEFAULT NULL,
  `status` enum('di dalam radius','di luar radius') NOT NULL DEFAULT 'di luar radius',
  `jarak_dari_kantor` float DEFAULT NULL,
  `lokasi_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `lokasi_id` (`lokasi_id`),
  CONSTRAINT `aktivitas_karyawan_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `aktivitas_karyawan_ibfk_2` FOREIGN KEY (`lokasi_id`) REFERENCES `lokasi` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
