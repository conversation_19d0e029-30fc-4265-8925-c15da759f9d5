<?php
/**
 * File untuk menguji API track_location.php
 * File ini berisi contoh penggunaan API track_location.php
 */

// Fungsi untuk mengirim request ke API
function sendRequest($url, $data) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

// URL API
$api_url = 'http://localhost/absensiku/api/track_location.php';

// API key
$api_key = 'absensiku_api_key_2023';

// Contoh data NIK karyawan (ganti dengan NIK yang valid di database)
$nik1 = '123456789'; // Ganti dengan NIK yang valid
$nik2 = '987654321'; // Ganti dengan NIK yang valid

// Contoh 1: Mengirim satu data lokasi
$single_data = [
    'api_key' => $api_key,
    'nik' => $nik1,
    'longitude' => '106.8456',
    'latitude' => '-6.2088'
];

// Contoh 2: Mengirim beberapa data lokasi sekaligus
$batch_data = [
    'api_key' => $api_key,
    'locations' => [
        [
            'nik' => $nik1,
            'longitude' => '106.8456',
            'latitude' => '-6.2088'
        ],
        [
            'nik' => $nik2,
            'longitude' => '106.8256',
            'latitude' => '-6.1988'
        ]
    ]
];

// Pilih contoh yang ingin dijalankan
$test_case = isset($_GET['test']) ? $_GET['test'] : 'single';

// Jalankan test case yang dipilih
if ($test_case === 'single') {
    $result = sendRequest($api_url, $single_data);
    $title = 'Test Case: Mengirim Satu Data Lokasi';
    $data_sent = $single_data;
} else {
    $result = sendRequest($api_url, $batch_data);
    $title = 'Test Case: Mengirim Beberapa Data Lokasi Sekaligus';
    $data_sent = $batch_data;
}

// Tampilkan hasil
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Track Location</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .json-key {
            color: #d63384;
        }
        .json-string {
            color: #20c997;
        }
        .json-number {
            color: #0d6efd;
        }
        .json-boolean {
            color: #fd7e14;
        }
        .json-null {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Test API Track Location</h1>
        
        <div class="mb-3">
            <a href="?test=single" class="btn btn-primary me-2">Test Single Data</a>
            <a href="?test=batch" class="btn btn-success">Test Batch Data</a>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo $title; ?></h5>
            </div>
            <div class="card-body">
                <h6>Data yang Dikirim:</h6>
                <pre><?php echo json_encode($data_sent, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?></pre>
                
                <h6 class="mt-4">HTTP Status Code:</h6>
                <pre><?php echo $result['code']; ?></pre>
                
                <h6 class="mt-4">Response:</h6>
                <pre><?php echo json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?></pre>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Dokumentasi API</h5>
            </div>
            <div class="card-body">
                <h6>Format Data Tunggal:</h6>
                <pre>{
    "api_key": "absensiku_api_key_2023",
    "nik": "123456789",
    "longitude": "106.8456",
    "latitude": "-6.2088"
}</pre>
                
                <h6 class="mt-4">Format Data Batch (Multiple):</h6>
                <pre>{
    "api_key": "absensiku_api_key_2023",
    "locations": [
        {
            "nik": "123456789",
            "longitude": "106.8456",
            "latitude": "-6.2088"
        },
        {
            "nik": "987654321",
            "longitude": "106.8256",
            "latitude": "-6.1988"
        }
    ]
}</pre>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
