# Aplikasi Absensi Karyawan Android

Aplikasi mobile Android untuk sistem absensi karyawan yang terintegrasi dengan sistem web admin. Dibangun menggunakan Flutter dengan fitur-fitur modern seperti GPS tracking, camera integration, dan real-time data synchronization.

## 🚀 Fitur Utama

### 📱 Untuk Karyawan (Mobile App)
1. **Login** - Autentikasi dengan NIK dan password
2. **Dashboard** - Ringkasan absensi hari ini dan statistik bulanan
3. **Presensi** - Absen masuk/pulang dengan foto selfie dan GPS location
4. **Riwayat** - Melihat riwayat absensi per bulan dengan statistik
5. **Profile** - Informasi profil karyawan dan pengaturan aplikasi

### 🌐 Integrasi dengan Web Admin
- Sinkronisasi real-time dengan sistem web admin
- Data absensi langsung tersimpan di database pusat
- Validasi lokasi berdasarkan setting admin
- Jam kerja dinamis sesuai bidang karyawan

## 🛠 Teknologi yang Digunakan

### Frontend (Mobile)
- **Flutter 3.0+** - Cross-platform mobile framework
- **Dart** - Programming language
- **Provider** - State management
- **HTTP** - API communication
- **Geolocator** - GPS location services
- **Image Picker** - Camera integration
- **Shared Preferences** - Local storage

### Backend (API)
- **PHP 7.4+** - Server-side scripting
- **MySQL 5.7+** - Database
- **REST API** - Communication protocol

### Dependencies
```yaml
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.0.5
  http: ^1.1.0
  shared_preferences: ^2.2.2
  image_picker: ^1.0.4
  camera: ^0.10.5+5
  geolocator: ^10.1.0
  permission_handler: ^11.0.1
  intl: ^0.18.1
  flutter_spinkit: ^5.2.0
  fluttertoast: ^8.2.4
  cached_network_image: ^3.3.0
```

## 📋 Persyaratan Sistem

### Minimum Requirements
- **Android**: 5.0 (API level 21) atau lebih tinggi
- **RAM**: 2GB
- **Storage**: 100MB ruang kosong
- **Permissions**: Camera, Location, Storage

### Recommended
- **Android**: 8.0 (API level 26) atau lebih tinggi
- **RAM**: 4GB atau lebih
- **Storage**: 500MB ruang kosong
- **Network**: Koneksi internet stabil

## 🔧 Instalasi & Setup

### 1. Setup Environment
```bash
# Install Flutter
# Download dari https://flutter.dev/docs/get-started/install

# Verify installation
flutter doctor

# Clone repository
git clone <repository-url>
cd aplikasi-absensi-karyawan-android
```

### 2. Install Dependencies
```bash
flutter pub get
```

### 3. Konfigurasi API
Edit file `lib/utils/constants.dart`:
```dart
class Constants {
  // Ganti dengan URL server Anda
  static const String baseUrl = 'https://yourdomain.com/api';
  
  // API Endpoints
  static const String loginEndpoint = '$baseUrl/login.php';
  static const String dashboardEndpoint = '$baseUrl/dashboard.php';
  static const String presensiEndpoint = '$baseUrl/presensi.php';
  static const String riwayatEndpoint = '$baseUrl/riwayat.php';
}
```

### 4. Build & Run
```bash
# Debug mode
flutter run

# Release mode
flutter build apk --release
```

## 📱 Struktur Aplikasi

```
lib/
├── main.dart                 # Entry point aplikasi
├── models/                   # Data models
│   ├── user_model.dart
│   └── presensi_model.dart
├── providers/                # State management
│   ├── auth_provider.dart
│   ├── dashboard_provider.dart
│   ├── presensi_provider.dart
│   └── riwayat_provider.dart
├── screens/                  # UI Screens
│   ├── splash_screen.dart
│   ├── login_screen.dart
│   ├── dashboard_screen.dart
│   ├── presensi_screen.dart
│   ├── riwayat_screen.dart
│   └── profile_screen.dart
├── services/                 # API services
│   └── api_service.dart
├── utils/                    # Utilities
│   ├── app_colors.dart
│   └── constants.dart
└── widgets/                  # Reusable widgets
    └── bottom_navigation.dart
```

## 🔐 Keamanan

### Authentication
- Session-based authentication dengan SharedPreferences
- Token validation pada setiap API call
- Auto-logout pada session expired

### Data Protection
- HTTPS communication untuk semua API calls
- Input validation dan sanitization
- Encrypted local storage untuk data sensitif

### Location & Camera
- Permission-based access
- Real-time location validation
- Secure image upload dengan base64 encoding

## 📊 API Endpoints

### Authentication
- `POST /api/login.php` - Login karyawan

### Dashboard
- `POST /api/dashboard.php` - Get dashboard data

### Presensi
- `POST /api/presensi.php` - Submit presensi (masuk/pulang)

### Riwayat
- `POST /api/riwayat.php` - Get riwayat presensi

## 🎨 UI/UX Design

### Design System
- **Primary Color**: Blue (#2196F3)
- **Secondary Color**: Teal (#03DAC6)
- **Success**: Green (#4CAF50)
- **Warning**: Orange (#FF9800)
- **Error**: Red (#B00020)

### Typography
- **Font Family**: Poppins
- **Heading**: Bold, 18-24px
- **Body**: Regular, 14-16px
- **Caption**: Regular, 12px

### Components
- Material Design 3 components
- Consistent spacing (8px grid)
- Rounded corners (8-16px radius)
- Subtle shadows and elevations

## 🚀 Deployment

### Build Release APK
```bash
flutter build apk --release
```

### Build App Bundle (untuk Play Store)
```bash
flutter build appbundle --release
```

### Signing APK
1. Generate keystore
2. Configure `android/app/build.gradle`
3. Build signed APK

## 🧪 Testing

### Unit Tests
```bash
flutter test
```

### Integration Tests
```bash
flutter drive --target=test_driver/app.dart
```

## 📈 Performance

### Optimizations
- Image compression untuk upload foto
- Lazy loading untuk list data
- Caching dengan SharedPreferences
- Efficient state management dengan Provider

### Monitoring
- Error tracking dan crash reporting
- Performance monitoring
- User analytics

## 🔄 Update & Maintenance

### Version Control
- Semantic versioning (MAJOR.MINOR.PATCH)
- Changelog documentation
- Backward compatibility

### Maintenance
- Regular dependency updates
- Security patches
- Bug fixes dan improvements

## 📞 Support

Untuk bantuan teknis atau pertanyaan:
- **Email**: <EMAIL>
- **Phone**: +62-xxx-xxxx-xxxx
- **Documentation**: [Link to docs]

## 📄 License

Copyright © 2024 Company Name. All rights reserved.

---

**Catatan**: Pastikan server backend sudah running dan API endpoints dapat diakses sebelum menjalankan aplikasi mobile.
